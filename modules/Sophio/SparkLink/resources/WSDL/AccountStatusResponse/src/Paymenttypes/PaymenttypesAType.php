<?php

namespace SparkLinkAPI\AccountStatusResponse\Paymenttypes;

/**
 * Class representing PaymenttypesAType
 */
class PaymenttypesAType
{
    /**
     * @var \SparkLinkAPI\AccountStatusResponse\Paymenttype[] $paymenttype
     */
    private $paymenttype = [
        
    ];

    /**
     * Adds as paymenttype
     *
     * @return self
     * @param \SparkLinkAPI\AccountStatusResponse\Paymenttype $paymenttype
     */
    public function addToPaymenttype(\SparkLinkAPI\AccountStatusResponse\Paymenttype $paymenttype)
    {
        $this->paymenttype[] = $paymenttype;
        return $this;
    }

    /**
     * isset paymenttype
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPaymenttype($index)
    {
        return isset($this->paymenttype[$index]);
    }

    /**
     * unset paymenttype
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPaymenttype($index)
    {
        unset($this->paymenttype[$index]);
    }

    /**
     * Gets as paymenttype
     *
     * @return \SparkLinkAPI\AccountStatusResponse\Paymenttype[]
     */
    public function getPaymenttype()
    {
        return $this->paymenttype;
    }

    /**
     * Sets a new paymenttype
     *
     * @param \SparkLinkAPI\AccountStatusResponse\Paymenttype[] $paymenttype
     * @return self
     */
    public function setPaymenttype(array $paymenttype = null)
    {
        $this->paymenttype = $paymenttype;
        return $this;
    }
}

