<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <xs:element name="SparkLink">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="accountstatus"/>
            </xs:sequence>
            <xs:attribute name="Rev" use="required" type="xs:string"/>
            <xs:attribute name="TransId" use="required" type="xs:string"/>
            <xs:attribute name="environment" use="required" type="xs:string"/>
            <xs:attribute name="lang" use="required" type="xs:string"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="accountstatus">
        <xs:complexType> <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element ref="header" minOccurs="1" maxOccurs="1"/>
            <xs:element ref="status" minOccurs="1" maxOccurs="1"/>
            <xs:element ref="branches" minOccurs="0" maxOccurs="1"/>
            <xs:element ref="shiptoparty" minOccurs="0" maxOccurs="1"/>
            <xs:element ref="billtoparty" minOccurs="0" maxOccurs="1" />
            <xs:element ref="paymenttypes" minOccurs="0" maxOccurs="1"/>
            <!-- delivery methods element depricated outside branches element. -->
            <xs:element ref="deliverymethods" minOccurs="0" maxOccurs="1" />
            <xs:element ref="promotions" minOccurs="0" maxOccurs="1"/> <xs:element ref="cutofftimes" minOccurs="0" maxOccurs="1" /> </xs:choice>
        </xs:complexType>
    </xs:element>
    <xs:element name="header">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="routing" minOccurs="1" maxOccurs="1"/>
            </xs:sequence>
            <xs:attribute name="account" use="required" type="xs:string"/>
            <xs:attribute name="pwd" type="xs:string" />
            <xs:attribute name="branch" use="required" type="xs:string"/>
            <xs:attribute name="clerk_no" type="xs:string"/>
            <xs:attribute name="address_code" type="xs:string"/>
            <xs:attribute name="area" type="xs:string"/>
            <xs:attribute name="errcode" use="required" type="xs:string"/>
            <xs:attribute name="errmsg" use="required" type="xs:string"/>
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="routing">
        <xs:complexType>
            <xs:attribute name="buyer_id" type="xs:string" />
            <xs:attribute name="client_id" type="xs:string" />
            <xs:attribute name="rid" type="xs:string" />
            <xs:attribute name="seller_id" type="xs:string" />
            <xs:attribute name="supplier" type="xs:string" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="status">
        <xs:complexType>
            <xs:attribute name="name" type="xs:string" use="required" />
            <xs:attribute name="poreq" type="xs:string"/>
            <xs:attribute name="credstatus" type="xs:string" use="required" />
            <xs:attribute name="discountcat" type="xs:string" />
            <xs:attribute name="email" type="xs:string" />
            <xs:attribute name="homeurl" type="xs:string" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="branches">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="deliverymethods" minOccurs="0" maxOccurs="1" />
                <xs:element ref="branch" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="branch">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="deliverymethods" minOccurs="0" maxOccurs="1"/>
                <xs:element ref="ordertypes" minOccurs="0" maxOccurs="1"/>
            </xs:sequence>
            <xs:attribute name="seqno" use="required" type="xs:string"/>
            <xs:attribute name="branchid" use="required" type="xs:string"/>
            <xs:attribute name="name" use="required" type="xs:string"/>
            <xs:attribute name="abbr" use="required" type="xs:string"/>
            <xs:attribute name="address1" use="required" type="xs:string"/>
            <xs:attribute name="address2" type="xs:string"/>
            <xs:attribute name="city" use="required" type="xs:string"/>
            <xs:attribute name="state" use="required" type="xs:string"/>
            <xs:attribute name="zip" use="required" type="xs:string"/>
            <xs:attribute name="country" use="required" type="xs:string"/>
            <xs:attribute name="phone" use="required" type="xs:string"/>
            <xs:attribute name="fax" type="xs:string"/>
            <xs:attribute name="seller_id" type="xs:string"/>
            <xs:attribute name="viewonly" type="xs:string"/>
            <xs:attribute name="area" type="xs:string"/>
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="shiptoparty">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="shipto" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="shipto" >
        <xs:complexType>
            <xs:attribute name="seqno" use="required" type="xs:string"/>
            <xs:attribute name="code" type="xs:string"/>
            <xs:attribute name="name" use="required" type="xs:string"/>
            <xs:attribute name="address1" use="required" type="xs:string" />
            <xs:attribute name="address2" type="xs:string" />
            <xs:attribute name="city" use="required" type="xs:string" />
            <xs:attribute name="state" use="required" type="xs:string"/>
            <xs:attribute name="zip" use="required" type="xs:string"/>
            <xs:attribute name="country" use="required" type="xs:string"/>
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="billtoparty">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="billto" minOccurs="0" maxOccurs="1" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="billto">
        <xs:complexType>
            <xs:attribute name="code" type="xs:string"/>
            <xs:attribute name="name" use="required" type="xs:string" />
            <xs:attribute name="address1" use="required" type="xs:string" />
            <xs:attribute name="address2" type="xs:string" />
            <xs:attribute name="city" use="required" type="xs:string" />
            <xs:attribute name="state" use="required" type="xs:string"/>
            <xs:attribute name="zip" use="required" type="xs:string"/>
            <xs:attribute name="country" use="required" type="xs:string"/>
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="paymenttypes">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="unbounded" ref="paymenttype"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="paymenttype">
        <xs:complexType>
            <xs:attribute name="seqno" use="required" type="xs:string" />
            <xs:attribute name="typeid" use="required" type="xs:string" />
            <xs:attribute name="typedesc" use="required" type="xs:string" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="promotions">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="unbounded" ref="promotion"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="promotion">
        <xs:complexType>
            <xs:attribute name="linenum" use="required" type="xs:string"/>
            <xs:attribute name="code" use="required" type="xs:string"/>
            <xs:attribute name="name" use="required" type="xs:string"/>
            <xs:attribute name="savings" use="required" type="xs:string"/>
            <xs:attribute name="redeemed" use="required" type="xs:string"/>
            <xs:attribute name="longdesc" use="required" type="xs:string" />
            <xs:attribute name="shortdesc" use="required" type="xs:string" />
            <xs:attribute name="enddate" use="required" type="xs:string"/>
            <xs:attribute name="startdate" use="required" type="xs:string"/>
            <xs:attribute name="details_alttext" use="required" type="xs:string"/>
            <xs:attribute name="detailsurl" use="required" type="xs:string"/>
            <xs:attribute name="banner_alttext" use="required" type="xs:string" />
            <xs:attribute name="bannerurl" use="required" type="xs:string" />
            <xs:attribute name="type" use="required" type="xs:string"/>
            <xs:attribute name="currency" use="required" type="xs:string"/>
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="deliverymethods">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="unbounded" ref="deliverymethod"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="deliverymethod">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="ordertypes" minOccurs="0" maxOccurs="unbounded" />
            </xs:sequence>
            <xs:attribute name="deliverycode" use="required" type="xs:string"/>
            <xs:attribute name="deliverydesc" type="xs:string" use="optional" />
            <xs:attribute name="seqno" use="required" type="xs:string"/> <xs:attribute name="cutoffidx" type="xs:integer" use="optional" />
        </xs:complexType>
    </xs:element>
    <xs:element name="ordertypes">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="unbounded" ref="ordertype"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="ordertype">
        <xs:complexType>
            <xs:simpleContent>
                <xs:extension base="xs:string">
                    <xs:attribute name="label" type="xs:string" />
                </xs:extension>
            </xs:simpleContent>
        </xs:complexType>
    </xs:element> <xs:element name="cutofftimes"> <xs:complexType> <xs:sequence> <xs:element maxOccurs="unbounded" ref="cutoff"/> </xs:sequence> </xs:complexType> </xs:element> <xs:element name="cutoff"> <xs:complexType> <xs:sequence> <xs:element maxOccurs="unbounded" ref="schedule"/> </xs:sequence> <xs:attribute name="idx" use="required" type="xs:integer"/> </xs:complexType> </xs:element> <xs:element name="schedule"> <xs:complexType> <xs:attribute name="deliveryby" use="required" type="xs:dateTime"/> <xs:attribute name="note" use="required" type="xs:string" /> <xs:attribute name="orderby" use="required" type="xs:dateTime"/> <xs:attribute name="seqno" use="required" type="xs:integer"/> </xs:complexType> </xs:element>
</xs:schema>