SparkLinkAPI\AccountStatusResponse\Branch\BranchAType:
    properties:
        seqno:
            expose: true
            access_type: public_method
            serialized_name: seqno
            accessor:
                getter: getSeqno
                setter: setSeqno
            xml_attribute: true
            type: string
        branchid:
            expose: true
            access_type: public_method
            serialized_name: branchid
            accessor:
                getter: getBranchid
                setter: setBranchid
            xml_attribute: true
            type: string
        name:
            expose: true
            access_type: public_method
            serialized_name: name
            accessor:
                getter: getName
                setter: setName
            xml_attribute: true
            type: string
        abbr:
            expose: true
            access_type: public_method
            serialized_name: abbr
            accessor:
                getter: getAbbr
                setter: setAbbr
            xml_attribute: true
            type: string
        address1:
            expose: true
            access_type: public_method
            serialized_name: address1
            accessor:
                getter: getAddress1
                setter: setAddress1
            xml_attribute: true
            type: string
        address2:
            expose: true
            access_type: public_method
            serialized_name: address2
            accessor:
                getter: getAddress2
                setter: setAddress2
            xml_attribute: true
            type: string
        city:
            expose: true
            access_type: public_method
            serialized_name: city
            accessor:
                getter: getCity
                setter: setCity
            xml_attribute: true
            type: string
        state:
            expose: true
            access_type: public_method
            serialized_name: state
            accessor:
                getter: getState
                setter: setState
            xml_attribute: true
            type: string
        zip:
            expose: true
            access_type: public_method
            serialized_name: zip
            accessor:
                getter: getZip
                setter: setZip
            xml_attribute: true
            type: string
        country:
            expose: true
            access_type: public_method
            serialized_name: country
            accessor:
                getter: getCountry
                setter: setCountry
            xml_attribute: true
            type: string
        phone:
            expose: true
            access_type: public_method
            serialized_name: phone
            accessor:
                getter: getPhone
                setter: setPhone
            xml_attribute: true
            type: string
        fax:
            expose: true
            access_type: public_method
            serialized_name: fax
            accessor:
                getter: getFax
                setter: setFax
            xml_attribute: true
            type: string
        sellerId:
            expose: true
            access_type: public_method
            serialized_name: seller_id
            accessor:
                getter: getSellerId
                setter: setSellerId
            xml_attribute: true
            type: string
        viewonly:
            expose: true
            access_type: public_method
            serialized_name: viewonly
            accessor:
                getter: getViewonly
                setter: setViewonly
            xml_attribute: true
            type: string
        area:
            expose: true
            access_type: public_method
            serialized_name: area
            accessor:
                getter: getArea
                setter: setArea
            xml_attribute: true
            type: string
        deliverymethods:
            expose: true
            access_type: public_method
            serialized_name: deliverymethods
            xml_element:
                cdata: false
            accessor:
                getter: getDeliverymethods
                setter: setDeliverymethods
            type: array<SparkLinkAPI\AccountStatusResponse\Deliverymethod>
            xml_list:
                inline: false
                entry_name: deliverymethod
                skip_when_empty: true
        ordertypes:
            expose: true
            access_type: public_method
            serialized_name: ordertypes
            xml_element:
                cdata: false
            accessor:
                getter: getOrdertypes
                setter: setOrdertypes
            type: array<SparkLinkAPI\AccountStatusResponse\Ordertype>
            xml_list:
                inline: false
                entry_name: ordertype
                skip_when_empty: true
