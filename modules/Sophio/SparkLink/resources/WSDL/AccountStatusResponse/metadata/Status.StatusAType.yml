SparkLinkAPI\AccountStatusResponse\Status\StatusAType:
    properties:
        name:
            expose: true
            access_type: public_method
            serialized_name: name
            accessor:
                getter: getName
                setter: setName
            xml_attribute: true
            type: string
        poreq:
            expose: true
            access_type: public_method
            serialized_name: poreq
            accessor:
                getter: getPoreq
                setter: setPoreq
            xml_attribute: true
            type: string
        credstatus:
            expose: true
            access_type: public_method
            serialized_name: credstatus
            accessor:
                getter: getCredstatus
                setter: setCredstatus
            xml_attribute: true
            type: string
        discountcat:
            expose: true
            access_type: public_method
            serialized_name: discountcat
            accessor:
                getter: getDiscountcat
                setter: setDiscountcat
            xml_attribute: true
            type: string
        email:
            expose: true
            access_type: public_method
            serialized_name: email
            accessor:
                getter: getEmail
                setter: setEmail
            xml_attribute: true
            type: string
        homeurl:
            expose: true
            access_type: public_method
            serialized_name: homeurl
            accessor:
                getter: getHomeurl
                setter: setHomeurl
            xml_attribute: true
            type: string
