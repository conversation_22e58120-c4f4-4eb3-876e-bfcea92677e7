SparkLinkAPI\PurchaseOrderRequest\Header\HeaderAType:
    properties:
        account:
            expose: true
            access_type: public_method
            serialized_name: account
            accessor:
                getter: getAccount
                setter: setAccount
            xml_attribute: true
            type: string
        pwd:
            expose: true
            access_type: public_method
            serialized_name: pwd
            accessor:
                getter: getPwd
                setter: setPwd
            xml_attribute: true
            type: string
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            accessor:
                getter: getType
                setter: setType
            xml_attribute: true
            type: string
        fillflag:
            expose: true
            access_type: public_method
            serialized_name: fillflag
            accessor:
                getter: getFillflag
                setter: setFillflag
            xml_attribute: true
            type: string
        branch:
            expose: true
            access_type: public_method
            serialized_name: branch
            accessor:
                getter: getBranch
                setter: setBranch
            xml_attribute: true
            type: string
        wonumber:
            expose: true
            access_type: public_method
            serialized_name: wonumber
            accessor:
                getter: getWonumber
                setter: setWonumber
            xml_attribute: true
            type: string
        ponumber:
            expose: true
            access_type: public_method
            serialized_name: ponumber
            accessor:
                getter: getPonumber
                setter: setPonumber
            xml_attribute: true
            type: string
        delmethod:
            expose: true
            access_type: public_method
            serialized_name: delmethod
            accessor:
                getter: getDelmethod
                setter: setDelmethod
            xml_attribute: true
            type: string
        clerkNo:
            expose: true
            access_type: public_method
            serialized_name: clerk_no
            accessor:
                getter: getClerkNo
                setter: setClerkNo
            xml_attribute: true
            type: string
        printQue:
            expose: true
            access_type: public_method
            serialized_name: print_que
            accessor:
                getter: getPrintQue
                setter: setPrintQue
            xml_attribute: true
            type: string
        invoiceType:
            expose: true
            access_type: public_method
            serialized_name: invoice_type
            accessor:
                getter: getInvoiceType
                setter: setInvoiceType
            xml_attribute: true
            type: string
        chargeCore:
            expose: true
            access_type: public_method
            serialized_name: charge_core
            accessor:
                getter: getChargeCore
                setter: setChargeCore
            xml_attribute: true
            type: string
        paymenttype:
            expose: true
            access_type: public_method
            serialized_name: paymenttype
            accessor:
                getter: getPaymenttype
                setter: setPaymenttype
            xml_attribute: true
            type: string
        area:
            expose: true
            access_type: public_method
            serialized_name: area
            accessor:
                getter: getArea
                setter: setArea
            xml_attribute: true
            type: string
        delcost:
            expose: true
            access_type: public_method
            serialized_name: delcost
            accessor:
                getter: getDelcost
                setter: setDelcost
            xml_attribute: true
            type: string
        tax:
            expose: true
            access_type: public_method
            serialized_name: tax
            accessor:
                getter: getTax
                setter: setTax
            xml_attribute: true
            type: string
        addressCode:
            expose: true
            access_type: public_method
            serialized_name: address_code
            accessor:
                getter: getAddressCode
                setter: setAddressCode
            xml_attribute: true
            type: string
        invoice:
            expose: true
            access_type: public_method
            serialized_name: invoice
            accessor:
                getter: getInvoice
                setter: setInvoice
            xml_attribute: true
            type: string
        salesTaxPct:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_pct
            accessor:
                getter: getSalesTaxPct
                setter: setSalesTaxPct
            xml_attribute: true
            type: string
        salesTaxState:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_state
            accessor:
                getter: getSalesTaxState
                setter: setSalesTaxState
            xml_attribute: true
            type: string
        shiptype:
            expose: true
            access_type: public_method
            serialized_name: shiptype
            accessor:
                getter: getShiptype
                setter: setShiptype
            xml_attribute: true
            type: string
        ordertotal:
            expose: true
            access_type: public_method
            serialized_name: ordertotal
            accessor:
                getter: getOrdertotal
                setter: setOrdertotal
            xml_attribute: true
            type: string
        paymethod:
            expose: true
            access_type: public_method
            serialized_name: paymethod
            accessor:
                getter: getPaymethod
                setter: setPaymethod
            xml_attribute: true
            type: string
        paytransid:
            expose: true
            access_type: public_method
            serialized_name: paytransid
            accessor:
                getter: getPaytransid
                setter: setPaytransid
            xml_attribute: true
            type: string
        b2ctransid:
            expose: true
            access_type: public_method
            serialized_name: b2ctransid
            accessor:
                getter: getB2ctransid
                setter: setB2ctransid
            xml_attribute: true
            type: string
        miscCharges:
            expose: true
            access_type: public_method
            serialized_name: misc_charges
            accessor:
                getter: getMiscCharges
                setter: setMiscCharges
            xml_attribute: true
            type: string
        totalDiscAmt:
            expose: true
            access_type: public_method
            serialized_name: total_disc_amt
            accessor:
                getter: getTotalDiscAmt
                setter: setTotalDiscAmt
            xml_attribute: true
            type: string
        totalRebateAmt:
            expose: true
            access_type: public_method
            serialized_name: total_rebate_amt
            accessor:
                getter: getTotalRebateAmt
                setter: setTotalRebateAmt
            xml_attribute: true
            type: string
        currencycode:
            expose: true
            access_type: public_method
            serialized_name: currencycode
            accessor:
                getter: getCurrencycode
                setter: setCurrencycode
            xml_attribute: true
            type: string
        insurancecost:
            expose: true
            access_type: public_method
            serialized_name: insurancecost
            accessor:
                getter: getInsurancecost
                setter: setInsurancecost
            xml_attribute: true
            type: string
        shiprefid:
            expose: true
            access_type: public_method
            serialized_name: shiprefid
            accessor:
                getter: getShiprefid
                setter: setShiprefid
            xml_attribute: true
            type: string
        shippingcarrier:
            expose: true
            access_type: public_method
            serialized_name: shippingcarrier
            accessor:
                getter: getShippingcarrier
                setter: setShippingcarrier
            xml_attribute: true
            type: string
        ccv:
            expose: true
            access_type: public_method
            serialized_name: ccv
            accessor:
                getter: getCcv
                setter: setCcv
            xml_attribute: true
            type: string
        salesTaxAmt:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_amt
            accessor:
                getter: getSalesTaxAmt
                setter: setSalesTaxAmt
            xml_attribute: true
            type: string
        freighttotal:
            expose: true
            access_type: public_method
            serialized_name: freighttotal
            accessor:
                getter: getFreighttotal
                setter: setFreighttotal
            xml_attribute: true
            type: string
        totalFet:
            expose: true
            access_type: public_method
            serialized_name: total_fet
            accessor:
                getter: getTotalFet
                setter: setTotalFet
            xml_attribute: true
            type: string
        vehid:
            expose: true
            access_type: public_method
            serialized_name: vehid
            accessor:
                getter: getVehid
                setter: setVehid
            xml_attribute: true
            type: string
        servicewriterno:
            expose: true
            access_type: public_method
            serialized_name: servicewriterno
            accessor:
                getter: getServicewriterno
                setter: setServicewriterno
            xml_attribute: true
            type: string
        salesman:
            expose: true
            access_type: public_method
            serialized_name: salesman
            accessor:
                getter: getSalesman
                setter: setSalesman
            xml_attribute: true
            type: string
        appSourceCode:
            expose: true
            access_type: public_method
            serialized_name: app_source_code
            accessor:
                getter: getAppSourceCode
                setter: setAppSourceCode
            xml_attribute: true
            type: string
        priority:
            expose: true
            access_type: public_method
            serialized_name: priority
            accessor:
                getter: getPriority
                setter: setPriority
            xml_attribute: true
            type: string
        loyaltycardMemberid:
            expose: true
            access_type: public_method
            serialized_name: loyaltycard_memberid
            accessor:
                getter: getLoyaltycardMemberid
                setter: setLoyaltycardMemberid
            xml_attribute: true
            type: string
        routing:
            expose: true
            access_type: public_method
            serialized_name: routing
            xml_element:
                cdata: false
            accessor:
                getter: getRouting
                setter: setRouting
            xml_list:
                inline: true
                entry_name: routing
            type: array<SparkLinkAPI\PurchaseOrderRequest\Routing>
        nationalProgram:
            expose: true
            access_type: public_method
            serialized_name: national_program
            xml_element:
                cdata: false
            accessor:
                getter: getNationalProgram
                setter: setNationalProgram
            xml_list:
                inline: true
                entry_name: national_program
            type: array<SparkLinkAPI\PurchaseOrderRequest\NationalProgram>
        buyer:
            expose: true
            access_type: public_method
            serialized_name: buyer
            xml_element:
                cdata: false
            accessor:
                getter: getBuyer
                setter: setBuyer
            xml_list:
                inline: true
                entry_name: buyer
            type: array<SparkLinkAPI\PurchaseOrderRequest\Buyer>
        promotions:
            expose: true
            access_type: public_method
            serialized_name: promotions
            xml_element:
                cdata: false
            accessor:
                getter: getPromotions
                setter: setPromotions
            type: array<SparkLinkAPI\PurchaseOrderRequest\Promotions\PromotionsAType\PromotionAType>
            xml_list:
                inline: false
                entry_name: promotion
                skip_when_empty: true
