<?php

namespace SparkLinkAPI\OrderStatusDetailResponse\Promotions\PromotionsAType;

/**
 * Class representing PromotionAType
 */
class PromotionAType
{
    /**
     * @var string $name
     */
    private $name = null;

    /**
     * @var string $linenum
     */
    private $linenum = null;

    /**
     * @var string $savings
     */
    private $savings = null;

    /**
     * @var string $code
     */
    private $code = null;

    /**
     * @var string $type
     */
    private $type = null;

    /**
     * @var string $shortdesc
     */
    private $shortdesc = null;

    /**
     * @var string $longdesc
     */
    private $longdesc = null;

    /**
     * @var string $errcode
     */
    private $errcode = null;

    /**
     * @var string $errmsg
     */
    private $errmsg = null;

    /**
     * Gets as name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Sets a new name
     *
     * @param string $name
     * @return self
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Gets as linenum
     *
     * @return string
     */
    public function getLinenum()
    {
        return $this->linenum;
    }

    /**
     * Sets a new linenum
     *
     * @param string $linenum
     * @return self
     */
    public function setLinenum($linenum)
    {
        $this->linenum = $linenum;
        return $this;
    }

    /**
     * Gets as savings
     *
     * @return string
     */
    public function getSavings()
    {
        return $this->savings;
    }

    /**
     * Sets a new savings
     *
     * @param string $savings
     * @return self
     */
    public function setSavings($savings)
    {
        $this->savings = $savings;
        return $this;
    }

    /**
     * Gets as code
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Sets a new code
     *
     * @param string $code
     * @return self
     */
    public function setCode($code)
    {
        $this->code = $code;
        return $this;
    }

    /**
     * Gets as type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Sets a new type
     *
     * @param string $type
     * @return self
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Gets as shortdesc
     *
     * @return string
     */
    public function getShortdesc()
    {
        return $this->shortdesc;
    }

    /**
     * Sets a new shortdesc
     *
     * @param string $shortdesc
     * @return self
     */
    public function setShortdesc($shortdesc)
    {
        $this->shortdesc = $shortdesc;
        return $this;
    }

    /**
     * Gets as longdesc
     *
     * @return string
     */
    public function getLongdesc()
    {
        return $this->longdesc;
    }

    /**
     * Sets a new longdesc
     *
     * @param string $longdesc
     * @return self
     */
    public function setLongdesc($longdesc)
    {
        $this->longdesc = $longdesc;
        return $this;
    }

    /**
     * Gets as errcode
     *
     * @return string
     */
    public function getErrcode()
    {
        return $this->errcode;
    }

    /**
     * Sets a new errcode
     *
     * @param string $errcode
     * @return self
     */
    public function setErrcode($errcode)
    {
        $this->errcode = $errcode;
        return $this;
    }

    /**
     * Gets as errmsg
     *
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Sets a new errmsg
     *
     * @param string $errmsg
     * @return self
     */
    public function setErrmsg($errmsg)
    {
        $this->errmsg = $errmsg;
        return $this;
    }
}

