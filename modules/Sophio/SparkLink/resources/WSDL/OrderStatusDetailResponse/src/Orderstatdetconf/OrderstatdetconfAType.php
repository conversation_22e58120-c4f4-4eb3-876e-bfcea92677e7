<?php

namespace SparkLinkAPI\OrderStatusDetailResponse\Orderstatdetconf;

/**
 * Class representing OrderstatdetconfAType
 */
class OrderstatdetconfAType
{
    /**
     * @var \SparkLinkAPI\OrderStatusDetailResponse\Orderheader $orderheader
     */
    private $orderheader = null;

    /**
     * Gets as orderheader
     *
     * @return \SparkLinkAPI\OrderStatusDetailResponse\Orderheader
     */
    public function getOrderheader()
    {
        return $this->orderheader;
    }

    /**
     * Sets a new orderheader
     *
     * @param \SparkLinkAPI\OrderStatusDetailResponse\Orderheader $orderheader
     * @return self
     */
    public function setOrderheader(\SparkLinkAPI\OrderStatusDetailResponse\Orderheader $orderheader)
    {
        $this->orderheader = $orderheader;
        return $this;
    }
}

