SparkLinkAPI\PurchaseOrderResponse\Promotions\PromotionsAType:
    properties:
        totalsavings:
            expose: true
            access_type: public_method
            serialized_name: totalsavings
            accessor:
                getter: getTotalsavings
                setter: setTotalsavings
            xml_attribute: true
            type: string
        promotion:
            expose: true
            access_type: public_method
            serialized_name: promotion
            xml_element:
                cdata: false
            accessor:
                getter: getPromotion
                setter: setPromotion
            xml_list:
                inline: true
                entry_name: promotion
            type: array<SparkLinkAPI\PurchaseOrderResponse\Promotions\PromotionsAType\PromotionAType>
