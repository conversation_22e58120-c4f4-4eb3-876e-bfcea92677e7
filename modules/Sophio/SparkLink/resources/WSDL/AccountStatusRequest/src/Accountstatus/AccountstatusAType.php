<?php

namespace SparkLinkAPI\AccountStatusRequest\Accountstatus;

/**
 * Class representing AccountstatusAType
 */
class AccountstatusAType
{
    /**
     * @var \SparkLinkAPI\AccountStatusRequest\Header $header
     */
    private $header = null;

    /**
     * Gets as header
     *
     * @return \SparkLinkAPI\AccountStatusRequest\Header
     */
    public function getHeader()
    {
        return $this->header;
    }

    /**
     * Sets a new header
     *
     * @param \SparkLinkAPI\AccountStatusRequest\Header $header
     * @return self
     */
    public function setHeader(\SparkLinkAPI\AccountStatusRequest\Header $header)
    {
        $this->header = $header;
        return $this;
    }
}

