<?php

namespace SparkLinkAPI\StockCheckRequest\Altbranch;

/**
 * Class representing AltbranchAType
 */
class AltbranchAType
{
    /**
     * @var string $albr
     */
    private $albr = null;

    /**
     * @var string $sellerId
     */
    private $sellerId = null;

    /**
     * @var string $clientId
     */
    private $clientId = null;

    /**
     * @var string $area
     */
    private $area = null;

    /**
     * Gets as albr
     *
     * @return string
     */
    public function getAlbr()
    {
        return $this->albr;
    }

    /**
     * Sets a new albr
     *
     * @param string $albr
     * @return self
     */
    public function setAlbr($albr)
    {
        $this->albr = $albr;
        return $this;
    }

    /**
     * Gets as sellerId
     *
     * @return string
     */
    public function getSellerId()
    {
        return $this->sellerId;
    }

    /**
     * Sets a new sellerId
     *
     * @param string $sellerId
     * @return self
     */
    public function setSellerId($sellerId)
    {
        $this->sellerId = $sellerId;
        return $this;
    }

    /**
     * Gets as clientId
     *
     * @return string
     */
    public function getClientId()
    {
        return $this->clientId;
    }

    /**
     * Sets a new clientId
     *
     * @param string $clientId
     * @return self
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
        return $this;
    }

    /**
     * Gets as area
     *
     * @return string
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * Sets a new area
     *
     * @param string $area
     * @return self
     */
    public function setArea($area)
    {
        $this->area = $area;
        return $this;
    }
}

