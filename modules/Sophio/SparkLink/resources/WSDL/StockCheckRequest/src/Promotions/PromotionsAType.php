<?php

namespace SparkLinkAPI\StockCheckRequest\Promotions;

/**
 * Class representing PromotionsAType
 */
class PromotionsAType
{
    /**
     * @var \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[] $promotion
     */
    private $promotion = [
        
    ];

    /**
     * Adds as promotion
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType $promotion
     */
    public function addToPromotion(\SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType $promotion)
    {
        $this->promotion[] = $promotion;
        return $this;
    }

    /**
     * isset promotion
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPromotion($index)
    {
        return isset($this->promotion[$index]);
    }

    /**
     * unset promotion
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPromotion($index)
    {
        unset($this->promotion[$index]);
    }

    /**
     * Gets as promotion
     *
     * @return \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[]
     */
    public function getPromotion()
    {
        return $this->promotion;
    }

    /**
     * Sets a new promotion
     *
     * @param \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[] $promotion
     * @return self
     */
    public function setPromotion(array $promotion = null)
    {
        $this->promotion = $promotion;
        return $this;
    }
}

