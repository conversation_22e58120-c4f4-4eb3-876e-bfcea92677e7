<?php

namespace SparkLinkAPI\StockCheckRequest\Header;

/**
 * Class representing HeaderAType
 */
class HeaderAType
{
    /**
     * @var string $account
     */
    private $account = null;

    /**
     * @var string $pwd
     */
    private $pwd = null;

    /**
     * @var string $ordertype
     */
    private $ordertype = null;

    /**
     * @var string $discountcat
     */
    private $discountcat = null;

    /**
     * @var string $alternateflag
     */
    private $alternateflag = null;

    /**
     * @var string $branch
     */
    private $branch = null;

    /**
     * @var string $dataSource
     */
    private $dataSource = null;

    /**
     * @var string $delmethod
     */
    private $delmethod = null;

    /**
     * @var string $clerkNo
     */
    private $clerkNo = null;

    /**
     * @var string $area
     */
    private $area = null;

    /**
     * @var string $addressCode
     */
    private $addressCode = null;

    /**
     * @var string $familyprflag
     */
    private $familyprflag = null;

    /**
     * @var string $currency
     */
    private $currency = null;

    /**
     * @var string $orderreview
     */
    private $orderreview = null;

    /**
     * @var string $appSourceCode
     */
    private $appSourceCode = null;

    /**
     * @var string $qtybreak
     */
    private $qtybreak = null;

    /**
     * @var string $source
     */
    private $source = null;

    /**
     * @var \SparkLinkAPI\StockCheckRequest\Routing[] $routing
     */
    private $routing = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckRequest\Shipto[] $shipto
     */
    private $shipto = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckRequest\Billto[] $billto
     */
    private $billto = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckRequest\Altbranch[] $altbranch
     */
    private $altbranch = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[] $promotions
     */
    private $promotions = null;

    /**
     * Gets as account
     *
     * @return string
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * Sets a new account
     *
     * @param string $account
     * @return self
     */
    public function setAccount($account)
    {
        $this->account = $account;
        return $this;
    }

    /**
     * Gets as pwd
     *
     * @return string
     */
    public function getPwd()
    {
        return $this->pwd;
    }

    /**
     * Sets a new pwd
     *
     * @param string $pwd
     * @return self
     */
    public function setPwd($pwd)
    {
        $this->pwd = $pwd;
        return $this;
    }

    /**
     * Gets as ordertype
     *
     * @return string
     */
    public function getOrdertype()
    {
        return $this->ordertype;
    }

    /**
     * Sets a new ordertype
     *
     * @param string $ordertype
     * @return self
     */
    public function setOrdertype($ordertype)
    {
        $this->ordertype = $ordertype;
        return $this;
    }

    /**
     * Gets as discountcat
     *
     * @return string
     */
    public function getDiscountcat()
    {
        return $this->discountcat;
    }

    /**
     * Sets a new discountcat
     *
     * @param string $discountcat
     * @return self
     */
    public function setDiscountcat($discountcat)
    {
        $this->discountcat = $discountcat;
        return $this;
    }

    /**
     * Gets as alternateflag
     *
     * @return string
     */
    public function getAlternateflag()
    {
        return $this->alternateflag;
    }

    /**
     * Sets a new alternateflag
     *
     * @param string $alternateflag
     * @return self
     */
    public function setAlternateflag($alternateflag)
    {
        $this->alternateflag = $alternateflag;
        return $this;
    }

    /**
     * Gets as branch
     *
     * @return string
     */
    public function getBranch()
    {
        return $this->branch;
    }

    /**
     * Sets a new branch
     *
     * @param string $branch
     * @return self
     */
    public function setBranch($branch)
    {
        $this->branch = $branch;
        return $this;
    }

    /**
     * Gets as dataSource
     *
     * @return string
     */
    public function getDataSource()
    {
        return $this->dataSource;
    }

    /**
     * Sets a new dataSource
     *
     * @param string $dataSource
     * @return self
     */
    public function setDataSource($dataSource)
    {
        $this->dataSource = $dataSource;
        return $this;
    }

    /**
     * Gets as delmethod
     *
     * @return string
     */
    public function getDelmethod()
    {
        return $this->delmethod;
    }

    /**
     * Sets a new delmethod
     *
     * @param string $delmethod
     * @return self
     */
    public function setDelmethod($delmethod)
    {
        $this->delmethod = $delmethod;
        return $this;
    }

    /**
     * Gets as clerkNo
     *
     * @return string
     */
    public function getClerkNo()
    {
        return $this->clerkNo;
    }

    /**
     * Sets a new clerkNo
     *
     * @param string $clerkNo
     * @return self
     */
    public function setClerkNo($clerkNo)
    {
        $this->clerkNo = $clerkNo;
        return $this;
    }

    /**
     * Gets as area
     *
     * @return string
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * Sets a new area
     *
     * @param string $area
     * @return self
     */
    public function setArea($area)
    {
        $this->area = $area;
        return $this;
    }

    /**
     * Gets as addressCode
     *
     * @return string
     */
    public function getAddressCode()
    {
        return $this->addressCode;
    }

    /**
     * Sets a new addressCode
     *
     * @param string $addressCode
     * @return self
     */
    public function setAddressCode($addressCode)
    {
        $this->addressCode = $addressCode;
        return $this;
    }

    /**
     * Gets as familyprflag
     *
     * @return string
     */
    public function getFamilyprflag()
    {
        return $this->familyprflag;
    }

    /**
     * Sets a new familyprflag
     *
     * @param string $familyprflag
     * @return self
     */
    public function setFamilyprflag($familyprflag)
    {
        $this->familyprflag = $familyprflag;
        return $this;
    }

    /**
     * Gets as currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Sets a new currency
     *
     * @param string $currency
     * @return self
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * Gets as orderreview
     *
     * @return string
     */
    public function getOrderreview()
    {
        return $this->orderreview;
    }

    /**
     * Sets a new orderreview
     *
     * @param string $orderreview
     * @return self
     */
    public function setOrderreview($orderreview)
    {
        $this->orderreview = $orderreview;
        return $this;
    }

    /**
     * Gets as appSourceCode
     *
     * @return string
     */
    public function getAppSourceCode()
    {
        return $this->appSourceCode;
    }

    /**
     * Sets a new appSourceCode
     *
     * @param string $appSourceCode
     * @return self
     */
    public function setAppSourceCode($appSourceCode)
    {
        $this->appSourceCode = $appSourceCode;
        return $this;
    }

    /**
     * Gets as qtybreak
     *
     * @return string
     */
    public function getQtybreak()
    {
        return $this->qtybreak;
    }

    /**
     * Sets a new qtybreak
     *
     * @param string $qtybreak
     * @return self
     */
    public function setQtybreak($qtybreak)
    {
        $this->qtybreak = $qtybreak;
        return $this;
    }

    /**
     * Gets as source
     *
     * @return string
     */
    public function getSource()
    {
        return $this->source;
    }

    /**
     * Sets a new source
     *
     * @param string $source
     * @return self
     */
    public function setSource($source)
    {
        $this->source = $source;
        return $this;
    }

    /**
     * Adds as routing
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Routing $routing
     */
    public function addToRouting(\SparkLinkAPI\StockCheckRequest\Routing $routing)
    {
        $this->routing[] = $routing;
        return $this;
    }

    /**
     * isset routing
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRouting($index)
    {
        return isset($this->routing[$index]);
    }

    /**
     * unset routing
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRouting($index)
    {
        unset($this->routing[$index]);
    }

    /**
     * Gets as routing
     *
     * @return \SparkLinkAPI\StockCheckRequest\Routing[]
     */
    public function getRouting()
    {
        return $this->routing;
    }

    /**
     * Sets a new routing
     *
     * @param \SparkLinkAPI\StockCheckRequest\Routing[] $routing
     * @return self
     */
    public function setRouting(array $routing = null)
    {
        $this->routing = $routing;
        return $this;
    }

    /**
     * Adds as shipto
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Shipto $shipto
     */
    public function addToShipto(\SparkLinkAPI\StockCheckRequest\Shipto $shipto)
    {
        $this->shipto[] = $shipto;
        return $this;
    }

    /**
     * isset shipto
     *
     * @param int|string $index
     * @return bool
     */
    public function issetShipto($index)
    {
        return isset($this->shipto[$index]);
    }

    /**
     * unset shipto
     *
     * @param int|string $index
     * @return void
     */
    public function unsetShipto($index)
    {
        unset($this->shipto[$index]);
    }

    /**
     * Gets as shipto
     *
     * @return \SparkLinkAPI\StockCheckRequest\Shipto[]
     */
    public function getShipto()
    {
        return $this->shipto;
    }

    /**
     * Sets a new shipto
     *
     * @param \SparkLinkAPI\StockCheckRequest\Shipto[] $shipto
     * @return self
     */
    public function setShipto(array $shipto = null)
    {
        $this->shipto = $shipto;
        return $this;
    }

    /**
     * Adds as billto
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Billto $billto
     */
    public function addToBillto(\SparkLinkAPI\StockCheckRequest\Billto $billto)
    {
        $this->billto[] = $billto;
        return $this;
    }

    /**
     * isset billto
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBillto($index)
    {
        return isset($this->billto[$index]);
    }

    /**
     * unset billto
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBillto($index)
    {
        unset($this->billto[$index]);
    }

    /**
     * Gets as billto
     *
     * @return \SparkLinkAPI\StockCheckRequest\Billto[]
     */
    public function getBillto()
    {
        return $this->billto;
    }

    /**
     * Sets a new billto
     *
     * @param \SparkLinkAPI\StockCheckRequest\Billto[] $billto
     * @return self
     */
    public function setBillto(array $billto = null)
    {
        $this->billto = $billto;
        return $this;
    }

    /**
     * Adds as altbranch
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Altbranch $altbranch
     */
    public function addToAltbranch(\SparkLinkAPI\StockCheckRequest\Altbranch $altbranch)
    {
        $this->altbranch[] = $altbranch;
        return $this;
    }

    /**
     * isset altbranch
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAltbranch($index)
    {
        return isset($this->altbranch[$index]);
    }

    /**
     * unset altbranch
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAltbranch($index)
    {
        unset($this->altbranch[$index]);
    }

    /**
     * Gets as altbranch
     *
     * @return \SparkLinkAPI\StockCheckRequest\Altbranch[]
     */
    public function getAltbranch()
    {
        return $this->altbranch;
    }

    /**
     * Sets a new altbranch
     *
     * @param \SparkLinkAPI\StockCheckRequest\Altbranch[] $altbranch
     * @return self
     */
    public function setAltbranch(array $altbranch = null)
    {
        $this->altbranch = $altbranch;
        return $this;
    }

    /**
     * Adds as promotion
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType $promotion
     */
    public function addToPromotions(\SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType $promotion)
    {
        $this->promotions[] = $promotion;
        return $this;
    }

    /**
     * isset promotions
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPromotions($index)
    {
        return isset($this->promotions[$index]);
    }

    /**
     * unset promotions
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPromotions($index)
    {
        unset($this->promotions[$index]);
    }

    /**
     * Gets as promotions
     *
     * @return \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[]
     */
    public function getPromotions()
    {
        return $this->promotions;
    }

    /**
     * Sets a new promotions
     *
     * @param \SparkLinkAPI\StockCheckRequest\Promotions\PromotionsAType\PromotionAType[] $promotions
     * @return self
     */
    public function setPromotions(array $promotions = null)
    {
        $this->promotions = $promotions;
        return $this;
    }
}

