SparkLinkAPI\StockCheckResponse\SparkLink\SparkLinkAType:
    properties:
        rev:
            expose: true
            access_type: public_method
            serialized_name: Rev
            accessor:
                getter: getRev
                setter: setRev
            xml_attribute: true
            type: string
        transId:
            expose: true
            access_type: public_method
            serialized_name: TransId
            accessor:
                getter: getTransId
                setter: setTransId
            xml_attribute: true
            type: string
        environment:
            expose: true
            access_type: public_method
            serialized_name: environment
            accessor:
                getter: getEnvironment
                setter: setEnvironment
            xml_attribute: true
            type: string
        lang:
            expose: true
            access_type: public_method
            serialized_name: lang
            accessor:
                getter: getLang
                setter: setLang
            xml_attribute: true
            type: string
        stockcheckresp:
            expose: true
            access_type: public_method
            serialized_name: stockcheckresp
            xml_element:
                cdata: false
            accessor:
                getter: getStockcheckresp
                setter: setStockcheckresp
            type: SparkLinkAPI\StockCheckResponse\Stockcheckresp
