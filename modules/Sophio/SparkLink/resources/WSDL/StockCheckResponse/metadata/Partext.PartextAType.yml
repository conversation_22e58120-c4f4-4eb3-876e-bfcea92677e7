SparkLinkAPI\StockCheckResponse\Partext\PartextAType:
    properties:
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            accessor:
                getter: getType
                setter: setType
            xml_attribute: true
            type: string
        linecode:
            expose: true
            access_type: public_method
            serialized_name: linecode
            accessor:
                getter: getLinecode
                setter: setLinecode
            xml_attribute: true
            type: string
        partno:
            expose: true
            access_type: public_method
            serialized_name: partno
            accessor:
                getter: getPartno
                setter: setPartno
            xml_attribute: true
            type: string
        desc:
            expose: true
            access_type: public_method
            serialized_name: desc
            accessor:
                getter: getDesc
                setter: setDesc
            xml_attribute: true
            type: string
        core:
            expose: true
            access_type: public_method
            serialized_name: core
            accessor:
                getter: getCore
                setter: setCore
            xml_attribute: true
            type: string
        cost:
            expose: true
            access_type: public_method
            serialized_name: cost
            accessor:
                getter: getCost
                setter: setCost
            xml_attribute: true
            type: string
        list:
            expose: true
            access_type: public_method
            serialized_name: list
            accessor:
                getter: getList
                setter: setList
            xml_attribute: true
            type: string
        uom:
            expose: true
            access_type: public_method
            serialized_name: uom
            accessor:
                getter: getUom
                setter: setUom
            xml_attribute: true
            type: string
        minqty:
            expose: true
            access_type: public_method
            serialized_name: minqty
            accessor:
                getter: getMinqty
                setter: setMinqty
            xml_attribute: true
            type: string
        csqty:
            expose: true
            access_type: public_method
            serialized_name: csqty
            accessor:
                getter: getCsqty
                setter: setCsqty
            xml_attribute: true
            type: string
        qtyreq:
            expose: true
            access_type: public_method
            serialized_name: qtyreq
            accessor:
                getter: getQtyreq
                setter: setQtyreq
            xml_attribute: true
            type: string
        qtyavail:
            expose: true
            access_type: public_method
            serialized_name: qtyavail
            accessor:
                getter: getQtyavail
                setter: setQtyavail
            xml_attribute: true
            type: string
        errcode:
            expose: true
            access_type: public_method
            serialized_name: errcode
            accessor:
                getter: getErrcode
                setter: setErrcode
            xml_attribute: true
            type: string
        errmsg:
            expose: true
            access_type: public_method
            serialized_name: errmsg
            accessor:
                getter: getErrmsg
                setter: setErrmsg
            xml_attribute: true
            type: string
        uPC:
            expose: true
            access_type: public_method
            serialized_name: UPC
            accessor:
                getter: getUPC
                setter: setUPC
            xml_attribute: true
            type: string
        commenteta:
            expose: true
            access_type: public_method
            serialized_name: commenteta
            accessor:
                getter: getCommenteta
                setter: setCommenteta
            xml_attribute: true
            type: string
        altbranch:
            expose: true
            access_type: public_method
            serialized_name: altbranch
            xml_element:
                cdata: false
            accessor:
                getter: getAltbranch
                setter: setAltbranch
            xml_list:
                inline: true
                entry_name: altbranch
            type: array<SparkLinkAPI\StockCheckResponse\Altbranch>
        branchdisplay:
            expose: true
            access_type: public_method
            serialized_name: branchdisplay
            xml_element:
                cdata: false
            accessor:
                getter: getBranchdisplay
                setter: setBranchdisplay
            xml_list:
                inline: true
                entry_name: branchdisplay
            type: array<SparkLinkAPI\StockCheckResponse\Branchdisplay>
        qtybreakdetails:
            expose: true
            access_type: public_method
            serialized_name: qtybreakdetails
            xml_element:
                cdata: false
            accessor:
                getter: getQtybreakdetails
                setter: setQtybreakdetails
            type: array<SparkLinkAPI\StockCheckResponse\QtybreakType>
            xml_list:
                inline: false
                entry_name: qtybreak
                skip_when_empty: true
        comment:
            expose: true
            access_type: public_method
            serialized_name: comment
            xml_element:
                cdata: false
            accessor:
                getter: getComment
                setter: setComment
            xml_list:
                inline: true
                entry_name: comment
            type: array<string>
        partinfo:
            expose: true
            access_type: public_method
            serialized_name: partinfo
            xml_element:
                cdata: false
            accessor:
                getter: getPartinfo
                setter: setPartinfo
            xml_list:
                inline: true
                entry_name: partinfo
            type: array<SparkLinkAPI\StockCheckResponse\Partinfo>
