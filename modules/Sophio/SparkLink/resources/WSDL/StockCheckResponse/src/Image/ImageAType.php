<?php

namespace SparkLinkAPI\StockCheckResponse\Image;

/**
 * Class representing ImageAType
 */
class ImageAType
{
    /**
     * @var string $imageurl
     */
    private $imageurl = null;

    /**
     * @var string $seqno
     */
    private $seqno = null;

    /**
     * @var string $thumbnailurl
     */
    private $thumbnailurl = null;

    /**
     * Gets as imageurl
     *
     * @return string
     */
    public function getImageurl()
    {
        return $this->imageurl;
    }

    /**
     * Sets a new imageurl
     *
     * @param string $imageurl
     * @return self
     */
    public function setImageurl($imageurl)
    {
        $this->imageurl = $imageurl;
        return $this;
    }

    /**
     * Gets as seqno
     *
     * @return string
     */
    public function getSeqno()
    {
        return $this->seqno;
    }

    /**
     * Sets a new seqno
     *
     * @param string $seqno
     * @return self
     */
    public function setSeqno($seqno)
    {
        $this->seqno = $seqno;
        return $this;
    }

    /**
     * Gets as thumbnailurl
     *
     * @return string
     */
    public function getThumbnailurl()
    {
        return $this->thumbnailurl;
    }

    /**
     * Sets a new thumbnailurl
     *
     * @param string $thumbnailurl
     * @return self
     */
    public function setThumbnailurl($thumbnailurl)
    {
        $this->thumbnailurl = $thumbnailurl;
        return $this;
    }
}

