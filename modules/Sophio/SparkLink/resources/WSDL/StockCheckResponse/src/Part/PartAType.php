<?php

namespace SparkLinkAPI\StockCheckResponse\Part;

/**
 * Class representing PartAType
 */
class PartAType
{
    /**
     * @var string $linenum
     */
    private $linenum = null;

    /**
     * @var string $linecode
     */
    private $linecode = null;

    /**
     * @var string $partno
     */
    private $partno = null;

    /**
     * @var string $qtyreq
     */
    private $qtyreq = null;

    /**
     * @var string $desc
     */
    private $desc = null;

    /**
     * @var string $core
     */
    private $core = null;

    /**
     * @var string $cost
     */
    private $cost = null;

    /**
     * @var string $list
     */
    private $list = null;

    /**
     * @var string $uom
     */
    private $uom = null;

    /**
     * @var string $minqty
     */
    private $minqty = null;

    /**
     * @var string $csqty
     */
    private $csqty = null;

    /**
     * @var string $qtyavail
     */
    private $qtyavail = null;

    /**
     * @var string $alternateflag
     */
    private $alternateflag = null;

    /**
     * @var string $errcode
     */
    private $errcode = null;

    /**
     * @var string $errmsg
     */
    private $errmsg = null;

    /**
     * @var string $uPC
     */
    private $uPC = null;

    /**
     * @var string $familycode
     */
    private $familycode = null;

    /**
     * @var string $regular
     */
    private $regular = null;

    /**
     * @var string $yousave
     */
    private $yousave = null;

    /**
     * @var string $instantsavings
     */
    private $instantsavings = null;

    /**
     * @var string $iconurl
     */
    private $iconurl = null;

    /**
     * @var string $iconAlttext
     */
    private $iconAlttext = null;

    /**
     * @var string $detailsurl
     */
    private $detailsurl = null;

    /**
     * @var string $detailsAlttext
     */
    private $detailsAlttext = null;

    /**
     * @var string $qtybreak
     */
    private $qtybreak = null;

    /**
     * @var string $acost
     */
    private $acost = null;

    /**
     * @var string $commenteta
     */
    private $commenteta = null;

    /**
     * @var string $mfgcode
     */
    private $mfgcode = null;

    /**
     * @var string $parttype
     */
    private $parttype = null;

    /**
     * @var string $type
     */
    private $type = null;

    /**
     * @var \SparkLinkAPI\StockCheckResponse\Choosepart[] $choosepart
     */
    private $choosepart = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckResponse\Altbranch[] $altbranch
     */
    private $altbranch = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckResponse\Partext[] $partext
     */
    private $partext = [
        
    ];

    /**
     * @var string[] $comment
     */
    private $comment = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckResponse\Branchdisplay[] $branchdisplay
     */
    private $branchdisplay = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckResponse\Partinfo[] $partinfo
     */
    private $partinfo = [
        
    ];

    /**
     * @var \SparkLinkAPI\StockCheckResponse\QtybreakType[] $qtybreakdetails
     */
    private $qtybreakdetails = null;

    /**
     * Gets as linenum
     *
     * @return string
     */
    public function getLinenum()
    {
        return $this->linenum;
    }

    /**
     * Sets a new linenum
     *
     * @param string $linenum
     * @return self
     */
    public function setLinenum($linenum)
    {
        $this->linenum = $linenum;
        return $this;
    }

    /**
     * Gets as linecode
     *
     * @return string
     */
    public function getLinecode()
    {
        return $this->linecode;
    }

    /**
     * Sets a new linecode
     *
     * @param string $linecode
     * @return self
     */
    public function setLinecode($linecode)
    {
        $this->linecode = $linecode;
        return $this;
    }

    /**
     * Gets as partno
     *
     * @return string
     */
    public function getPartno()
    {
        return $this->partno;
    }

    /**
     * Sets a new partno
     *
     * @param string $partno
     * @return self
     */
    public function setPartno($partno)
    {
        $this->partno = $partno;
        return $this;
    }

    /**
     * Gets as qtyreq
     *
     * @return string
     */
    public function getQtyreq()
    {
        return $this->qtyreq;
    }

    /**
     * Sets a new qtyreq
     *
     * @param string $qtyreq
     * @return self
     */
    public function setQtyreq($qtyreq)
    {
        $this->qtyreq = $qtyreq;
        return $this;
    }

    /**
     * Gets as desc
     *
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * Sets a new desc
     *
     * @param string $desc
     * @return self
     */
    public function setDesc($desc)
    {
        $this->desc = $desc;
        return $this;
    }

    /**
     * Gets as core
     *
     * @return string
     */
    public function getCore()
    {
        return $this->core;
    }

    /**
     * Sets a new core
     *
     * @param string $core
     * @return self
     */
    public function setCore($core)
    {
        $this->core = $core;
        return $this;
    }

    /**
     * Gets as cost
     *
     * @return string
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * Sets a new cost
     *
     * @param string $cost
     * @return self
     */
    public function setCost($cost)
    {
        $this->cost = $cost;
        return $this;
    }

    /**
     * Gets as list
     *
     * @return string
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Sets a new list
     *
     * @param string $list
     * @return self
     */
    public function setList($list)
    {
        $this->list = $list;
        return $this;
    }

    /**
     * Gets as uom
     *
     * @return string
     */
    public function getUom()
    {
        return $this->uom;
    }

    /**
     * Sets a new uom
     *
     * @param string $uom
     * @return self
     */
    public function setUom($uom)
    {
        $this->uom = $uom;
        return $this;
    }

    /**
     * Gets as minqty
     *
     * @return string
     */
    public function getMinqty()
    {
        return $this->minqty;
    }

    /**
     * Sets a new minqty
     *
     * @param string $minqty
     * @return self
     */
    public function setMinqty($minqty)
    {
        $this->minqty = $minqty;
        return $this;
    }

    /**
     * Gets as csqty
     *
     * @return string
     */
    public function getCsqty()
    {
        return $this->csqty;
    }

    /**
     * Sets a new csqty
     *
     * @param string $csqty
     * @return self
     */
    public function setCsqty($csqty)
    {
        $this->csqty = $csqty;
        return $this;
    }

    /**
     * Gets as qtyavail
     *
     * @return string
     */
    public function getQtyavail()
    {
        return $this->qtyavail;
    }

    /**
     * Sets a new qtyavail
     *
     * @param string $qtyavail
     * @return self
     */
    public function setQtyavail($qtyavail)
    {
        $this->qtyavail = $qtyavail;
        return $this;
    }

    /**
     * Gets as alternateflag
     *
     * @return string
     */
    public function getAlternateflag()
    {
        return $this->alternateflag;
    }

    /**
     * Sets a new alternateflag
     *
     * @param string $alternateflag
     * @return self
     */
    public function setAlternateflag($alternateflag)
    {
        $this->alternateflag = $alternateflag;
        return $this;
    }

    /**
     * Gets as errcode
     *
     * @return string
     */
    public function getErrcode()
    {
        return $this->errcode;
    }

    /**
     * Sets a new errcode
     *
     * @param string $errcode
     * @return self
     */
    public function setErrcode($errcode)
    {
        $this->errcode = $errcode;
        return $this;
    }

    /**
     * Gets as errmsg
     *
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Sets a new errmsg
     *
     * @param string $errmsg
     * @return self
     */
    public function setErrmsg($errmsg)
    {
        $this->errmsg = $errmsg;
        return $this;
    }

    /**
     * Gets as uPC
     *
     * @return string
     */
    public function getUPC()
    {
        return $this->uPC;
    }

    /**
     * Sets a new uPC
     *
     * @param string $uPC
     * @return self
     */
    public function setUPC($uPC)
    {
        $this->uPC = $uPC;
        return $this;
    }

    /**
     * Gets as familycode
     *
     * @return string
     */
    public function getFamilycode()
    {
        return $this->familycode;
    }

    /**
     * Sets a new familycode
     *
     * @param string $familycode
     * @return self
     */
    public function setFamilycode($familycode)
    {
        $this->familycode = $familycode;
        return $this;
    }

    /**
     * Gets as regular
     *
     * @return string
     */
    public function getRegular()
    {
        return $this->regular;
    }

    /**
     * Sets a new regular
     *
     * @param string $regular
     * @return self
     */
    public function setRegular($regular)
    {
        $this->regular = $regular;
        return $this;
    }

    /**
     * Gets as yousave
     *
     * @return string
     */
    public function getYousave()
    {
        return $this->yousave;
    }

    /**
     * Sets a new yousave
     *
     * @param string $yousave
     * @return self
     */
    public function setYousave($yousave)
    {
        $this->yousave = $yousave;
        return $this;
    }

    /**
     * Gets as instantsavings
     *
     * @return string
     */
    public function getInstantsavings()
    {
        return $this->instantsavings;
    }

    /**
     * Sets a new instantsavings
     *
     * @param string $instantsavings
     * @return self
     */
    public function setInstantsavings($instantsavings)
    {
        $this->instantsavings = $instantsavings;
        return $this;
    }

    /**
     * Gets as iconurl
     *
     * @return string
     */
    public function getIconurl()
    {
        return $this->iconurl;
    }

    /**
     * Sets a new iconurl
     *
     * @param string $iconurl
     * @return self
     */
    public function setIconurl($iconurl)
    {
        $this->iconurl = $iconurl;
        return $this;
    }

    /**
     * Gets as iconAlttext
     *
     * @return string
     */
    public function getIconAlttext()
    {
        return $this->iconAlttext;
    }

    /**
     * Sets a new iconAlttext
     *
     * @param string $iconAlttext
     * @return self
     */
    public function setIconAlttext($iconAlttext)
    {
        $this->iconAlttext = $iconAlttext;
        return $this;
    }

    /**
     * Gets as detailsurl
     *
     * @return string
     */
    public function getDetailsurl()
    {
        return $this->detailsurl;
    }

    /**
     * Sets a new detailsurl
     *
     * @param string $detailsurl
     * @return self
     */
    public function setDetailsurl($detailsurl)
    {
        $this->detailsurl = $detailsurl;
        return $this;
    }

    /**
     * Gets as detailsAlttext
     *
     * @return string
     */
    public function getDetailsAlttext()
    {
        return $this->detailsAlttext;
    }

    /**
     * Sets a new detailsAlttext
     *
     * @param string $detailsAlttext
     * @return self
     */
    public function setDetailsAlttext($detailsAlttext)
    {
        $this->detailsAlttext = $detailsAlttext;
        return $this;
    }

    /**
     * Gets as qtybreak
     *
     * @return string
     */
    public function getQtybreak()
    {
        return $this->qtybreak;
    }

    /**
     * Sets a new qtybreak
     *
     * @param string $qtybreak
     * @return self
     */
    public function setQtybreak($qtybreak)
    {
        $this->qtybreak = $qtybreak;
        return $this;
    }

    /**
     * Gets as acost
     *
     * @return string
     */
    public function getAcost()
    {
        return $this->acost;
    }

    /**
     * Sets a new acost
     *
     * @param string $acost
     * @return self
     */
    public function setAcost($acost)
    {
        $this->acost = $acost;
        return $this;
    }

    /**
     * Gets as commenteta
     *
     * @return string
     */
    public function getCommenteta()
    {
        return $this->commenteta;
    }

    /**
     * Sets a new commenteta
     *
     * @param string $commenteta
     * @return self
     */
    public function setCommenteta($commenteta)
    {
        $this->commenteta = $commenteta;
        return $this;
    }

    /**
     * Gets as mfgcode
     *
     * @return string
     */
    public function getMfgcode()
    {
        return $this->mfgcode;
    }

    /**
     * Sets a new mfgcode
     *
     * @param string $mfgcode
     * @return self
     */
    public function setMfgcode($mfgcode)
    {
        $this->mfgcode = $mfgcode;
        return $this;
    }

    /**
     * Gets as parttype
     *
     * @return string
     */
    public function getParttype()
    {
        return $this->parttype;
    }

    /**
     * Sets a new parttype
     *
     * @param string $parttype
     * @return self
     */
    public function setParttype($parttype)
    {
        $this->parttype = $parttype;
        return $this;
    }

    /**
     * Gets as type
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Sets a new type
     *
     * @param string $type
     * @return self
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Adds as choosepart
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\Choosepart $choosepart
     */
    public function addToChoosepart(\SparkLinkAPI\StockCheckResponse\Choosepart $choosepart)
    {
        $this->choosepart[] = $choosepart;
        return $this;
    }

    /**
     * isset choosepart
     *
     * @param int|string $index
     * @return bool
     */
    public function issetChoosepart($index)
    {
        return isset($this->choosepart[$index]);
    }

    /**
     * unset choosepart
     *
     * @param int|string $index
     * @return void
     */
    public function unsetChoosepart($index)
    {
        unset($this->choosepart[$index]);
    }

    /**
     * Gets as choosepart
     *
     * @return \SparkLinkAPI\StockCheckResponse\Choosepart[]
     */
    public function getChoosepart()
    {
        return $this->choosepart;
    }

    /**
     * Sets a new choosepart
     *
     * @param \SparkLinkAPI\StockCheckResponse\Choosepart[] $choosepart
     * @return self
     */
    public function setChoosepart(array $choosepart = null)
    {
        $this->choosepart = $choosepart;
        return $this;
    }

    /**
     * Adds as altbranch
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\Altbranch $altbranch
     */
    public function addToAltbranch(\SparkLinkAPI\StockCheckResponse\Altbranch $altbranch)
    {
        $this->altbranch[] = $altbranch;
        return $this;
    }

    /**
     * isset altbranch
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAltbranch($index)
    {
        return isset($this->altbranch[$index]);
    }

    /**
     * unset altbranch
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAltbranch($index)
    {
        unset($this->altbranch[$index]);
    }

    /**
     * Gets as altbranch
     *
     * @return \SparkLinkAPI\StockCheckResponse\Altbranch[]
     */
    public function getAltbranch()
    {
        return $this->altbranch;
    }

    /**
     * Sets a new altbranch
     *
     * @param \SparkLinkAPI\StockCheckResponse\Altbranch[] $altbranch
     * @return self
     */
    public function setAltbranch(array $altbranch = null)
    {
        $this->altbranch = $altbranch;
        return $this;
    }

    /**
     * Adds as partext
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\Partext $partext
     */
    public function addToPartext(\SparkLinkAPI\StockCheckResponse\Partext $partext)
    {
        $this->partext[] = $partext;
        return $this;
    }

    /**
     * isset partext
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPartext($index)
    {
        return isset($this->partext[$index]);
    }

    /**
     * unset partext
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPartext($index)
    {
        unset($this->partext[$index]);
    }

    /**
     * Gets as partext
     *
     * @return \SparkLinkAPI\StockCheckResponse\Partext[]
     */
    public function getPartext()
    {
        return $this->partext;
    }

    /**
     * Sets a new partext
     *
     * @param \SparkLinkAPI\StockCheckResponse\Partext[] $partext
     * @return self
     */
    public function setPartext(array $partext = null)
    {
        $this->partext = $partext;
        return $this;
    }

    /**
     * Adds as comment
     *
     * @return self
     * @param string $comment
     */
    public function addToComment($comment)
    {
        $this->comment[] = $comment;
        return $this;
    }

    /**
     * isset comment
     *
     * @param int|string $index
     * @return bool
     */
    public function issetComment($index)
    {
        return isset($this->comment[$index]);
    }

    /**
     * unset comment
     *
     * @param int|string $index
     * @return void
     */
    public function unsetComment($index)
    {
        unset($this->comment[$index]);
    }

    /**
     * Gets as comment
     *
     * @return string[]
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * Sets a new comment
     *
     * @param string[] $comment
     * @return self
     */
    public function setComment(array $comment = null)
    {
        $this->comment = $comment;
        return $this;
    }

    /**
     * Adds as branchdisplay
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\Branchdisplay $branchdisplay
     */
    public function addToBranchdisplay(\SparkLinkAPI\StockCheckResponse\Branchdisplay $branchdisplay)
    {
        $this->branchdisplay[] = $branchdisplay;
        return $this;
    }

    /**
     * isset branchdisplay
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBranchdisplay($index)
    {
        return isset($this->branchdisplay[$index]);
    }

    /**
     * unset branchdisplay
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBranchdisplay($index)
    {
        unset($this->branchdisplay[$index]);
    }

    /**
     * Gets as branchdisplay
     *
     * @return \SparkLinkAPI\StockCheckResponse\Branchdisplay[]
     */
    public function getBranchdisplay()
    {
        return $this->branchdisplay;
    }

    /**
     * Sets a new branchdisplay
     *
     * @param \SparkLinkAPI\StockCheckResponse\Branchdisplay[] $branchdisplay
     * @return self
     */
    public function setBranchdisplay(array $branchdisplay = null)
    {
        $this->branchdisplay = $branchdisplay;
        return $this;
    }

    /**
     * Adds as partinfo
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\Partinfo $partinfo
     */
    public function addToPartinfo(\SparkLinkAPI\StockCheckResponse\Partinfo $partinfo)
    {
        $this->partinfo[] = $partinfo;
        return $this;
    }

    /**
     * isset partinfo
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPartinfo($index)
    {
        return isset($this->partinfo[$index]);
    }

    /**
     * unset partinfo
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPartinfo($index)
    {
        unset($this->partinfo[$index]);
    }

    /**
     * Gets as partinfo
     *
     * @return \SparkLinkAPI\StockCheckResponse\Partinfo[]
     */
    public function getPartinfo()
    {
        return $this->partinfo;
    }

    /**
     * Sets a new partinfo
     *
     * @param \SparkLinkAPI\StockCheckResponse\Partinfo[] $partinfo
     * @return self
     */
    public function setPartinfo(array $partinfo = null)
    {
        $this->partinfo = $partinfo;
        return $this;
    }

    /**
     * Adds as qtybreak
     *
     * @return self
     * @param \SparkLinkAPI\StockCheckResponse\QtybreakType $qtybreak
     */
    public function addToQtybreakdetails(\SparkLinkAPI\StockCheckResponse\QtybreakType $qtybreak)
    {
        $this->qtybreakdetails[] = $qtybreak;
        return $this;
    }

    /**
     * isset qtybreakdetails
     *
     * @param int|string $index
     * @return bool
     */
    public function issetQtybreakdetails($index)
    {
        return isset($this->qtybreakdetails[$index]);
    }

    /**
     * unset qtybreakdetails
     *
     * @param int|string $index
     * @return void
     */
    public function unsetQtybreakdetails($index)
    {
        unset($this->qtybreakdetails[$index]);
    }

    /**
     * Gets as qtybreakdetails
     *
     * @return \SparkLinkAPI\StockCheckResponse\QtybreakType[]
     */
    public function getQtybreakdetails()
    {
        return $this->qtybreakdetails;
    }

    /**
     * Sets a new qtybreakdetails
     *
     * @param \SparkLinkAPI\StockCheckResponse\QtybreakType[] $qtybreakdetails
     * @return self
     */
    public function setQtybreakdetails(array $qtybreakdetails = null)
    {
        $this->qtybreakdetails = $qtybreakdetails;
        return $this;
    }
}

