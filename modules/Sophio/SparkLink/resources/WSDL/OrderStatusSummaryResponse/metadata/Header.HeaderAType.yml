SparkLinkAPI\OrderStatusSummaryResponse\Header\HeaderAType:
    properties:
        account:
            expose: true
            access_type: public_method
            serialized_name: account
            accessor:
                getter: getAccount
                setter: setAccount
            xml_attribute: true
            type: string
        pwd:
            expose: true
            access_type: public_method
            serialized_name: pwd
            accessor:
                getter: getPwd
                setter: setPwd
            xml_attribute: true
            type: string
        branch:
            expose: true
            access_type: public_method
            serialized_name: branch
            accessor:
                getter: getBranch
                setter: setBranch
            xml_attribute: true
            type: string
        reqrecs:
            expose: true
            access_type: public_method
            serialized_name: reqrecs
            accessor:
                getter: getReqrecs
                setter: setReqrecs
            xml_attribute: true
            type: string
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            accessor:
                getter: getType
                setter: setType
            xml_attribute: true
            type: string
        errcode:
            expose: true
            access_type: public_method
            serialized_name: errcode
            accessor:
                getter: getErrcode
                setter: setErrcode
            xml_attribute: true
            type: string
        errmsg:
            expose: true
            access_type: public_method
            serialized_name: errmsg
            accessor:
                getter: getErrmsg
                setter: setErrmsg
            xml_attribute: true
            type: string
        invoice:
            expose: true
            access_type: public_method
            serialized_name: invoice
            accessor:
                getter: getInvoice
                setter: setInvoice
            xml_attribute: true
            type: string
        orderno:
            expose: true
            access_type: public_method
            serialized_name: orderno
            accessor:
                getter: getOrderno
                setter: setOrderno
            xml_attribute: true
            type: string
        ponumber:
            expose: true
            access_type: public_method
            serialized_name: ponumber
            accessor:
                getter: getPonumber
                setter: setPonumber
            xml_attribute: true
            type: string
        area:
            expose: true
            access_type: public_method
            serialized_name: area
            accessor:
                getter: getArea
                setter: setArea
            xml_attribute: true
            type: string
        routing:
            expose: true
            access_type: public_method
            serialized_name: routing
            xml_element:
                cdata: false
            accessor:
                getter: getRouting
                setter: setRouting
            type: SparkLinkAPI\OrderStatusSummaryResponse\Routing
