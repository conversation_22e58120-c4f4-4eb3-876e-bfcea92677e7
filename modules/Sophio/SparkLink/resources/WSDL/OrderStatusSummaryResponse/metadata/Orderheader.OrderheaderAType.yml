SparkLinkAPI\OrderStatusSummaryResponse\Orderheader\OrderheaderAType:
    properties:
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            accessor:
                getter: getType
                setter: setType
            xml_attribute: true
            type: string
        lineitems:
            expose: true
            access_type: public_method
            serialized_name: lineitems
            accessor:
                getter: getLineitems
                setter: setLineitems
            xml_attribute: true
            type: string
        branch:
            expose: true
            access_type: public_method
            serialized_name: branch
            accessor:
                getter: getBranch
                setter: setBranch
            xml_attribute: true
            type: string
        dateentered:
            expose: true
            access_type: public_method
            serialized_name: dateentered
            accessor:
                getter: getDateentered
                setter: setDateentered
            xml_attribute: true
            type: string
        dateinvoiced:
            expose: true
            access_type: public_method
            serialized_name: dateinvoiced
            accessor:
                getter: getDateinvoiced
                setter: setDateinvoiced
            xml_attribute: true
            type: string
        timeentered:
            expose: true
            access_type: public_method
            serialized_name: timeentered
            accessor:
                getter: getTimeentered
                setter: setTimeentered
            xml_attribute: true
            type: string
        timeinvoiced:
            expose: true
            access_type: public_method
            serialized_name: timeinvoiced
            accessor:
                getter: getTimeinvoiced
                setter: setTimeinvoiced
            xml_attribute: true
            type: string
        invoice:
            expose: true
            access_type: public_method
            serialized_name: invoice
            accessor:
                getter: getInvoice
                setter: setInvoice
            xml_attribute: true
            type: string
        orderno:
            expose: true
            access_type: public_method
            serialized_name: orderno
            accessor:
                getter: getOrderno
                setter: setOrderno
            xml_attribute: true
            type: string
        ponumber:
            expose: true
            access_type: public_method
            serialized_name: ponumber
            accessor:
                getter: getPonumber
                setter: setPonumber
            xml_attribute: true
            type: string
        shipadd1:
            expose: true
            access_type: public_method
            serialized_name: shipadd1
            accessor:
                getter: getShipadd1
                setter: setShipadd1
            xml_attribute: true
            type: string
        shipadd2:
            expose: true
            access_type: public_method
            serialized_name: shipadd2
            accessor:
                getter: getShipadd2
                setter: setShipadd2
            xml_attribute: true
            type: string
        shipadd3:
            expose: true
            access_type: public_method
            serialized_name: shipadd3
            accessor:
                getter: getShipadd3
                setter: setShipadd3
            xml_attribute: true
            type: string
        shipadd4:
            expose: true
            access_type: public_method
            serialized_name: shipadd4
            accessor:
                getter: getShipadd4
                setter: setShipadd4
            xml_attribute: true
            type: string
        shipmethod:
            expose: true
            access_type: public_method
            serialized_name: shipmethod
            accessor:
                getter: getShipmethod
                setter: setShipmethod
            xml_attribute: true
            type: string
        status:
            expose: true
            access_type: public_method
            serialized_name: status
            accessor:
                getter: getStatus
                setter: setStatus
            xml_attribute: true
            type: string
        terms:
            expose: true
            access_type: public_method
            serialized_name: terms
            accessor:
                getter: getTerms
                setter: setTerms
            xml_attribute: true
            type: string
        total:
            expose: true
            access_type: public_method
            serialized_name: total
            accessor:
                getter: getTotal
                setter: setTotal
            xml_attribute: true
            type: string
        coretotal:
            expose: true
            access_type: public_method
            serialized_name: coretotal
            accessor:
                getter: getCoretotal
                setter: setCoretotal
            xml_attribute: true
            type: string
        delcost:
            expose: true
            access_type: public_method
            serialized_name: delcost
            accessor:
                getter: getDelcost
                setter: setDelcost
            xml_attribute: true
            type: string
        tax:
            expose: true
            access_type: public_method
            serialized_name: tax
            accessor:
                getter: getTax
                setter: setTax
            xml_attribute: true
            type: string
        area:
            expose: true
            access_type: public_method
            serialized_name: area
            accessor:
                getter: getArea
                setter: setArea
            xml_attribute: true
            type: string
        totalFet:
            expose: true
            access_type: public_method
            serialized_name: total_fet
            accessor:
                getter: getTotalFet
                setter: setTotalFet
            xml_attribute: true
            type: string
        totalpromosavings:
            expose: true
            access_type: public_method
            serialized_name: totalpromosavings
            accessor:
                getter: getTotalpromosavings
                setter: setTotalpromosavings
            xml_attribute: true
            type: string
        wonumber:
            expose: true
            access_type: public_method
            serialized_name: wonumber
            accessor:
                getter: getWonumber
                setter: setWonumber
            xml_attribute: true
            type: string
