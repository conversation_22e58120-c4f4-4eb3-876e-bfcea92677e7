<?php

namespace SparkLinkAPI\OrderStatusSummaryRequest\Orderstatsum;

/**
 * Class representing OrderstatsumAType
 */
class OrderstatsumAType
{
    /**
     * @var \SparkLinkAPI\OrderStatusSummaryRequest\Header $header
     */
    private $header = null;

    /**
     * Gets as header
     *
     * @return \SparkLinkAPI\OrderStatusSummaryRequest\Header
     */
    public function getHeader()
    {
        return $this->header;
    }

    /**
     * Sets a new header
     *
     * @param \SparkLinkAPI\OrderStatusSummaryRequest\Header $header
     * @return self
     */
    public function setHeader(\SparkLinkAPI\OrderStatusSummaryRequest\Header $header)
    {
        $this->header = $header;
        return $this;
    }
}

