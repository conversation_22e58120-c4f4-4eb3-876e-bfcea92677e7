<?php

namespace <PERSON><PERSON><PERSON>\SparkLink\Library\Operation;

use Illuminate\Support\Carbon;
use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\SparkLink\Library\Request\OrderStatusDetailRequest;
use Sophio\SparkLink\Library\Response\OrderStatusDetailResponse;
use SparkLinkAPI\OrderStatusDetailResponse\Orderheader;
use SparkLinkAPI\OrderStatusDetailResponse\Orderstatdetconf;
use SparkLinkAPI\OrderStatusDetailResponse\Part;
use SparkLinkAPI\OrderStatusDetailResponse\SparkLink;

class OrderStatusDetail extends Operation
{
    protected OrderStatusDetailRequest $request;
    public function execute()
    {

        $this->request = new OrderStatusDetailRequest();
        $this->request->setContent($this->requestInput);
        $o_request = $this->request->parse();
        $o_header = $o_request->getOrderstatdet()->getHeader();
        $this->header = new Orderheader();
        $this->response =  new OrderStatusDetailResponse();
        $this->response->item = new SparkLink();
        if (!$this->verifyCustomer($o_header)) {
            $this->supplierlog = (new SupplierLogCreate(config('sophio.admin.sparklink_order_database')))([
                'storepk' => $this->store->STOREPK,
                'timein' => now(),
                'accountnum' =>  '',
                'apitype' => 'sparklink',
                'action' => 'orderstatdet',
                'request' => $this->requestInput,
                'response' => "",
                'clientip' => request()->server('REMOTE_ADDR')
            ]);
            $orderstatdetconf = new Orderstatdetconf();
            $orderstatdetconf->setOrderheader($this->header);
            $this->response->item->setOrderstatdetconf($orderstatdetconf);
            return $this->returnResponse('orderstatdet');
        }
        $this->supplierlog = (new SupplierLogCreate(config('sophio.admin.sparklink_order_database')))([
            'storepk' => $this->store->STOREPK,
            'timein' => now(),
            'accountnum' => $this->customer?$this->customer->accountnum:'',
            'apitype' => 'sparklink',
            'action' => 'orderstatdet',
            'request' => $this->requestInput,
            'response' => "",
            'clientip' => request()->server('REMOTE_ADDR')
        ]);
        $orderstatdetconf = new Orderstatdetconf();
        $this->header->setOrderno($o_header->getOrderno());
        \Config::set('tenant_db',$this->settings->get('fbs_database'));
        $invoice = Invoice::where('pk',$o_header->getOrderno())->first();
        if($invoice) {
            if(in_array($invoice->invstatus, ['P','N','H','3','9','W','O','K','9','X','4','5'])) {
                $status = "ordered";
            }elseif($invoice->invstatus=="W") {
                $status="picked";
            }elseif(in_array($invoice->invstatus,['C','F'])) {
                $status = "shipped";
            }else{
                $status="ordered";
            }

            $this->header->setStatus($status);
            $this->header->setBranch(1);
            $this->header->setOrderno($invoice->pk);
            $this->header->setInvoice($invoice->pk);
            $this->header->setPonumber($invoice->ponumber);
            $this->header->setDateentered(Carbon::createFromDate($invoice->invdate)->setTimezone('UTC')->format('Ymd'));
            $this->header->setDateinvoiced(Carbon::createFromDate($invoice->invdate)->setTimezone('UTC')->format('Ymd'));
            $this->header->setTimeentered(Carbon::createFromDate($invoice->invdate)->setTimezone('UTC')->format('H:i:s').' UTC');
            $this->header->setTimeinvoiced(Carbon::createFromDate($invoice->invdate)->setTimezone('UTC')->format('H:i:s').' UTC');
            $this->header->setShipmethod($invoice->SHIPPING);
            $this->header->setShipadd1($invoice->st_addr);
            $this->header->setShipadd2($invoice->st_addr2);
            $this->header->setType('order');
            $this->header->setTotal($invoice->invtotal);
            $this->header->setCoretotal($invoice->coretotal);
            $this->header->setLineitems(count($invoice->lineitem));
            $this->header->setTerms($invoice->paymethod);
            $this->header->setCurrency('USD');
            $i=1;
            foreach($invoice->lineitem as $lineitem) {
                $part = new Part();
                $part->setLinenum($i);
                $part->setLinecode($lineitem->wdlinecode);
                $part->setPartno($lineitem->itemform);
                $part->setQtyord($lineitem->qty_ord);

                $part->setQtyship(($lineitem->sup_ord_id!=0 && $lineitem->track_num!=""?$lineitem->qty:0));
                $part->setDescription($lineitem->descript.' '.$lineitem->track_num);
                $part->setCost($lineitem->price);
                $part->setList($lineitem->list);
                $part->setCore($lineitem->coreprice);
                $this->header->addToPart($part);
                $i++;
            }

        }else{
            $this->header->setErrcode('fail');
            $this->header->setErrmsg('Order not found!');
        }
        $orderstatdetconf->setOrderheader($this->header);
        $this->response->item->setOrderstatdetconf($orderstatdetconf);
        return $this->returnResponse('orderstatdet');

    }

}