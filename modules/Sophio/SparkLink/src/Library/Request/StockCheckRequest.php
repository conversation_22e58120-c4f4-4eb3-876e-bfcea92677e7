<?php

namespace So<PERSON><PERSON>\SparkLink\Library\Request;

use Illuminate\Http\Request;
use Sophio\SparkLink\Library\SparkLinkBase;
use SparkLinkAPI\StockCheckRequest\SparkLink;


class StockCheckRequest extends SparkLinkBase
{
    protected SparkLink $object;
    protected \SparkLinkAPI\StockCheckRequest\Stockcheck $stockcheck;

    public function __construct()
    {
        $this->metadata = base_path() . '/modules/Sophio/SparkLink/resources/WSDL/StockCheckRequest/metadata';
        $this->namespace = 'SparkLinkAPI\StockCheckRequest';
        parent::__construct();
    }

    public function parse()
    {

        $this->object = $this->serializer->deserialize($this->xml, \SparkLinkAPI\StockCheckRequest\SparkLink::class, 'xml');
        $this->stockcheck = $this->object->getStockcheck();
        return $this->object;
    }

    public function getHeader()
    {
        return $this->stockcheck->getHeader()[0];
    }

    public function getParts()
    {
        return $this->stockcheck->getPart();
    }

    public function getObject()
    {
        return $this->object;
    }
}