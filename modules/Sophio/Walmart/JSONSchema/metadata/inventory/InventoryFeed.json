{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"InventoryFeedHeader": {"title": "InventoryFeedHeader", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"businessUnit": {"type": "string", "title": "Mart", "enum": ["WALMART_US"]}, "version": {"type": "string", "title": "Spec Version", "enum": ["2.0.20220829-15_11_50-api"]}, "locale": {"type": "string", "title": "Locale", "enum": ["en"]}, "feedDate": {"type": "string", "title": "Feed Date"}, "feedType": {"enum": ["DSV_INVENTORY"], "title": "Feed Type", "type": "string"}}, "additionalProperties": false}, "Inventory": {"title": "Inventory", "$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"availabilityCode": {"type": "string", "title": "Availability Code", "enum": ["AA", "AC"]}, "quantity": {"type": "integer", "title": "Quantity (On Hand Inventory)", "minimum": 0, "maximum": 10000}, "productId": {"type": "string", "title": "Sellable GTIN", "minLength": 1, "maxLength": 14}, "shipNode": {"type": "string", "title": "Facility ID", "minLength": 1, "maxLength": 50}}, "required": ["shipNode", "productId", "availabilityCode"], "allOf": [{"if": {"properties": {"availabilityCode": {"type": "string", "enum": ["AC"]}}}, "then": {"required": ["quantity"]}, "else": {"required": ["availabilityCode"]}}], "additionalProperties": false}}, "title": "InventoryFeed", "required": ["InventoryFeedHeader", "Inventory"], "additionalProperties": false}