WalmartAPI\Order\ReturnCenterAddressType:
    properties:
        name:
            expose: true
            access_type: public_method
            serialized_name: name
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getName
                setter: setName
            type: string
        address1:
            expose: true
            access_type: public_method
            serialized_name: address1
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getAddress1
                setter: setAddress1
            type: string
        address2:
            expose: true
            access_type: public_method
            serialized_name: address2
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getAddress2
                setter: setAddress2
            type: string
        city:
            expose: true
            access_type: public_method
            serialized_name: city
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCity
                setter: setCity
            type: string
        state:
            expose: true
            access_type: public_method
            serialized_name: state
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getState
                setter: setState
            type: string
        postalCode:
            expose: true
            access_type: public_method
            serialized_name: postalCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getPostalCode
                setter: setPostalCode
            type: string
        country:
            expose: true
            access_type: public_method
            serialized_name: country
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCountry
                setter: setCountry
            type: string
        dayPhone:
            expose: true
            access_type: public_method
            serialized_name: dayPhone
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getDayPhone
                setter: setDayPhone
            type: string
