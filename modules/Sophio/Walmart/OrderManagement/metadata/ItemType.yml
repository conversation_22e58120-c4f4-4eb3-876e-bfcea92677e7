WalmartAPI\Order\ItemType:
    properties:
        productName:
            expose: true
            access_type: public_method
            serialized_name: productName
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getProductName
                setter: setProductName
            type: string
        sku:
            expose: true
            access_type: public_method
            serialized_name: sku
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getSku
                setter: setSku
            type: string
        imageUrl:
            expose: true
            access_type: public_method
            serialized_name: imageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getImageUrl
                setter: setImageUrl
            type: string
        weight:
            expose: true
            access_type: public_method
            serialized_name: weight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getWeight
                setter: setWeight
            type: WalmartAPI\Order\WeightType
