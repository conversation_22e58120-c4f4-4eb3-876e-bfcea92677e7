WalmartAPI\Order\OrderType:
    properties:
        purchaseOrderId:
            expose: true
            access_type: public_method
            serialized_name: purchaseOrderId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getPurchaseOrderId
                setter: setPurchaseOrderId
            type: string
        customerOrderId:
            expose: true
            access_type: public_method
            serialized_name: customerOrderId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCustomerOrderId
                setter: setCustomerOrderId
            type: string
        customerEmailId:
            expose: true
            access_type: public_method
            serialized_name: customerEmailId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCustomerEmailId
                setter: setCustomerEmailId
            type: string
        orderDate:
            expose: true
            access_type: public_method
            serialized_name: orderDate
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getOrderDate
                setter: setOrderDate
            type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\DateTime
        buyerId:
            expose: true
            access_type: public_method
            serialized_name: buyerId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getBuyerId
                setter: setBuyerId
            type: string
        mart:
            expose: true
            access_type: public_method
            serialized_name: mart
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getMart
                setter: setMart
            type: string
        isGuest:
            expose: true
            access_type: public_method
            serialized_name: isGuest
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getIsGuest
                setter: setIsGuest
            type: bool
        shippingInfo:
            expose: true
            access_type: public_method
            serialized_name: shippingInfo
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getShippingInfo
                setter: setShippingInfo
            type: WalmartAPI\Order\ShippingInfoType
        orderLines:
            expose: true
            access_type: public_method
            serialized_name: orderLines
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getOrderLines
                setter: setOrderLines
            type: array<WalmartAPI\Order\OrderLineType>
            xml_list:
                inline: false
                entry_name: orderLine
                skip_when_empty: false
                namespace: 'http://walmart.com/mp/v3/orders'
        paymentTypes:
            expose: true
            access_type: public_method
            serialized_name: paymentTypes
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getPaymentTypes
                setter: setPaymentTypes
            type: array<string>
            xml_list:
                inline: false
                entry_name: paymentType
                skip_when_empty: false
                namespace: 'http://walmart.com/mp/v3/orders'
        orderSummary:
            expose: true
            access_type: public_method
            serialized_name: orderSummary
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getOrderSummary
                setter: setOrderSummary
            type: WalmartAPI\Order\OrderSummaryType
        pickupPersons:
            expose: true
            access_type: public_method
            serialized_name: pickupPersons
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getPickupPersons
                setter: setPickupPersons
            type: array<WalmartAPI\Order\PickupPersonType>
            xml_list:
                inline: false
                entry_name: pickupPerson
                skip_when_empty: false
                namespace: 'http://walmart.com/mp/v3/orders'
        shipNode:
            expose: true
            access_type: public_method
            serialized_name: shipNode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getShipNode
                setter: setShipNode
            type: WalmartAPI\Order\ShipNodesType
