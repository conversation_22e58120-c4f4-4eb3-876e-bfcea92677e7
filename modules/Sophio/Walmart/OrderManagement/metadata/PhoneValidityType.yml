WalmartAPI\Order\PhoneValidityType:
    properties:
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getType
                setter: setType
            type: string
        status:
            expose: true
            access_type: public_method
            serialized_name: status
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getStatus
                setter: setStatus
            type: string
        validatedOn:
            expose: true
            access_type: public_method
            serialized_name: validatedOn
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getValidatedOn
                setter: setValidatedOn
            type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\DateTime
        validatedBy:
            expose: true
            access_type: public_method
            serialized_name: validatedBy
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getValidatedBy
                setter: setValidatedBy
            type: string
