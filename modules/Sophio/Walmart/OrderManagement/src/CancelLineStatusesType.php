<?php

namespace WalmartAPI\Order;

/**
 * Class representing CancelLineStatusesType
 *
 *
 * XSD Type: cancelLineStatusesType
 */
class CancelLineStatusesType
{
    /**
     * @var \WalmartAPI\Order\CancelLineStatusType[] $orderLineStatus
     */
    private $orderLineStatus = [
        
    ];

    /**
     * Adds as orderLineStatus
     *
     * @return self
     * @param \WalmartAPI\Order\CancelLineStatusType $orderLineStatus
     */
    public function addToOrderLineStatus(\WalmartAPI\Order\CancelLineStatusType $orderLineStatus)
    {
        $this->orderLineStatus[] = $orderLineStatus;
        return $this;
    }

    /**
     * isset orderLineStatus
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOrderLineStatus($index)
    {
        return isset($this->orderLineStatus[$index]);
    }

    /**
     * unset orderLineStatus
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOrderLineStatus($index)
    {
        unset($this->orderLineStatus[$index]);
    }

    /**
     * Gets as orderLineStatus
     *
     * @return \WalmartAPI\Order\CancelLineStatusType[]
     */
    public function getOrderLineStatus()
    {
        return $this->orderLineStatus;
    }

    /**
     * Sets a new orderLineStatus
     *
     * @param \WalmartAPI\Order\CancelLineStatusType[] $orderLineStatus
     * @return self
     */
    public function setOrderLineStatus(array $orderLineStatus)
    {
        $this->orderLineStatus = $orderLineStatus;
        return $this;
    }
}

