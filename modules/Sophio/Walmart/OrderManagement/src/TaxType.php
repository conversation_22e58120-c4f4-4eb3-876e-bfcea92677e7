<?php

namespace WalmartAPI\Order;

/**
 * Class representing TaxType
 *
 *
 * XSD Type: taxType
 */
class TaxType
{
    /**
     * @var string $taxName
     */
    private $taxName = null;

    /**
     * @var \WalmartAPI\Order\MoneyType $taxAmount
     */
    private $taxAmount = null;

    /**
     * Gets as taxName
     *
     * @return string
     */
    public function getTaxName()
    {
        return $this->taxName;
    }

    /**
     * Sets a new taxName
     *
     * @param string $taxName
     * @return self
     */
    public function setTaxName($taxName)
    {
        $this->taxName = $taxName;
        return $this;
    }

    /**
     * Gets as taxAmount
     *
     * @return \WalmartAPI\Order\MoneyType
     */
    public function getTaxAmount()
    {
        return $this->taxAmount;
    }

    /**
     * Sets a new taxAmount
     *
     * @param \WalmartAPI\Order\MoneyType $taxAmount
     * @return self
     */
    public function setTaxAmount(\WalmartAPI\Order\MoneyType $taxAmount)
    {
        $this->taxAmount = $taxAmount;
        return $this;
    }
}

