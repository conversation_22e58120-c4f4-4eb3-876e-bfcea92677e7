<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns="http://walmart.com/mp/v3/orders" targetNamespace="http://walmart.com/mp/v3/orders"
	elementFormDefault="qualified" version="3.3">
	 
	<xsd:include schemaLocation="CommonComponentsV3.3.xsd" />
	
	<xsd:element name="orderShipment">
		<xsd:complexType>	
			<xsd:sequence>
				<xsd:element name="orderLines" type="shippingLinesType" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	
       <xsd:complexType name="returnCenterAddressType">
               <xsd:sequence>
                   <xsd:element name="name" type="xsd:string" minOccurs="0" />
                   <xsd:element name="address1" type="xsd:string" minOccurs="0" />
                   <xsd:element name="address2" type="xsd:string" minOccurs="0" />
                   <xsd:element name="city" type="xsd:string" minOccurs="0" />
                   <xsd:element name="state" type="xsd:string" minOccurs="0" />
                   <xsd:element name="postalCode" type="xsd:string" minOccurs="0" />
                   <xsd:element name="country" type="xsd:string" minOccurs="0" />
                   <xsd:element name="dayPhone" type="xsd:string" minOccurs="0" />
              </xsd:sequence>	
	</xsd:complexType>

	<xsd:complexType name="shippingLinesType">
		<xsd:sequence>
			<xsd:element name="orderLine" type="shippingLineType" maxOccurs="unbounded" />
		</xsd:sequence>
	</xsd:complexType>

	<xsd:complexType name="shippingLineType">
		<xsd:annotation>
			<xsd:documentation> Shipping Lines section. Update quantity and tracking info using this
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="lineNumber" type="xsd:string" />
			<xsd:element name="orderLineStatuses" type="shipLineStatusesType" />
		</xsd:sequence>
	</xsd:complexType>
	
	<xsd:complexType name="shipLineStatusesType">
		<xsd:sequence>
			<xsd:element name="orderLineStatus" type="shipLineStatusType"  maxOccurs="unbounded" />
		</xsd:sequence>
	</xsd:complexType>
	
	<xsd:complexType name="shipLineStatusType">
		<xsd:sequence>
			<xsd:element name="status" type="orderLineStatusValueType" />
			<xsd:element name="asn" type="asnType" minOccurs="0" />
			<xsd:element name="statusQuantity" type="quantityType"  />
			<xsd:element name="trackingInfo" type="trackingInfoType"  />
                        <xsd:element name="sellerOrderId" type="xsd:string" minOccurs="0" />
                        <xsd:element name="returnCenterAddress" type="returnCenterAddressType"  minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>

</xsd:schema>