<?php

namespace Sophio\Walmart\Actions;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\HomeDepot\Models\FeedItemCostHistory;

class GetContractPrice
{
    public $error="";
    public function __invoke($marketPlaceItem,Supplier $supplier,Customer $customer,$date)
    {
        $contractprice =0;
        $tenant = config('tenant_db');
        config(['tenant_db'=>$customer->xml['ACESPARTSDB']??'walmart_aces']);
        $ch = \Sophio\Walmart\Models\FeedItemCostHistory::join('walmart.feeds', function($join)use($supplier){
            $join->on('feeds.id', '=', 'feed_items_cost_history.feed_id');
        })->where('marketSellerSku',$marketPlaceItem->marketSellerSku)->where('feed_items_cost_history.contactpk',$supplier->contactpk)
            ->where('created_at','<',$date)->orderBy('created_at','DESC')->first();
        if($ch) {
            if($ch->errors!==null && $ch->errors!=='{}'&& $ch->errors!=='') {
                try{
                   $error = json_decode($ch->errors,true);
                   if(isset($error['errors'])) {
                       if(isset($error['errors']['ingestionError'])) {
                           $this->error = ('Warning! Contact price for '.$marketPlaceItem->mfg_code.' '.$marketPlaceItem->part_number_unformatted.' replied with error: '.($error['errors']['ingestionError']['description']?? json_encode($error['ingestionError']) ))." Feed sent date:".Carbon::parse($ch->created_at);
                       }else{
                           $this->error = "Warning! Contract price for ".$marketPlaceItem->mfg_code.' '.$marketPlaceItem->part_number_unformatted." taken from a failed cost feed."." Feed sent date:".Carbon::parse($ch->created_at);
                       }

                   } else{
                       $this->error = "Warning! Contract price for ".$marketPlaceItem->mfg_code.' '.$marketPlaceItem->part_number_unformatted." replied with error".$ch->errors." Feed sent date:".Carbon::parse($ch->created_at);
                   }


                }catch (\Throwable $e){
                    $this->error = "Warning! Contract price for ".$marketPlaceItem->mfg_code.' '.$marketPlaceItem->part_number_unformatted." reply with error".$ch->errors." Feed sent date:".Carbon::parse($ch->created_at);
                 }
            }
            $contractprice = $ch->price/$marketPlaceItem->pack_qty;
        }

        config(['tenant_db'=>$tenant]);
        return $contractprice;
    }
}