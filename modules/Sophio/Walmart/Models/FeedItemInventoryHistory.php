<?php

namespace Sophio\Walmart\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;
use Sophio\Common\Models\FBS\Supplier;

class FeedItemInventoryHistory extends Model
{
    use \Awobaz\Compoships\Compoships;
    use CrudTrait;
    protected $table = 'walmart.feed_items_inventory_history';
    protected $fillable = ['marketSellerSku', 'qty_avail', 'contactpk', 'feed_id','supplier_pk'];
    public $timestamps = false;
    public function feed()
    {
        return $this->belongsTo(Feed::class,'feed_id','id');
    }
    public function supplier()
    {
        return $this->belongsTo(Supplier::class,'supplier_pk','PK');
    }
    public function marketplaceitem()
    {
        return $this->belongsTo(MarketplaceItem::class,'marketSellerSku','marketSellerSku');
    }
}