<?php

namespace So<PERSON><PERSON>\Walmart\Library\Templates;


use Sophio\Common\Models\FBS\Supplier;
use Sophio\Walmart\Library\RequestBody\DSVInventoryFeed;


class DSVInventoryFeedTemplate
{
    public function __invoke(Supplier $sup, $model,$settings)
    {

        $object = new DSVInventoryFeed();

        $inventory = [];
        foreach ($model as $m) {
            if ($m->pack_qty > 1) {
                if ($m->pack_qty >= $m->wd_min_qty) {
                    $quantity = floor($m->qty_avail / $m->pack_qty);
                    if($quantity<$settings['min_stock']) {
                        $quantity = 0;
                    }
                } else {
                    // the situation here could be pack_qty=4 and supplier wd_min_qty=10
                    $quantity = 0;
                }
            } else {
                if ($m->wd_min_qty > $m->pack_qty) {
                    // If Supplier reports a bigger wd min qty we should set qty = 0 to avoid getting
                    // overcharged by supplier (on their statement they will charge wd_min_qty*price, while we
                    // will collect only pack_qty*price
                    $quantity = 0;
                } else {

                    $quantity = (int)$m->qty_avail;
                    if($quantity<$settings['min_stock']) {
                        $quantity = 0;
                    }
                }

            }
            if(isset($settings['zero']) && $settings['zero']===true) {
                $quantity=0;
            }
            $inventory[] = [
                'availabilityCode' => 'AC',
                'quantity' => $quantity,
                'productId'=> $m->gtin,
                'shipNode'=>$sup->SETTINGS['WALMARTSHIPNODE']
            ];
        }
        $object->setData($inventory);
        return $object;
    }
}