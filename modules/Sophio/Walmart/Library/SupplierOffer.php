<?php

namespace Sophio\Walmart\Library;

use Sophio\Common\Models\PIM\Part;
use WalmartDSV\SupplierOfferType;
use WalmartDSV\SupplierOfferType\ShippingDimensionsDepthAType;
use WalmartDSV\SupplierOfferType\ShippingDimensionsHeightAType;
use WalmartDSV\SupplierOfferType\ShippingDimensionsWidthAType;

class SupplierOffer
{
    protected SupplierOfferType $supplierOffer;
    protected $part;
    public function __construct(Part $part)
    {
        $this->supplierOffer = new SupplierOfferType();
        $this->part = $part;
    }
    public function addDimensions()
    {
        $dimensions = [$this->part->product->width, $this->part->product->length, $this->part->product->height];

        $shippingWeight = new \WalmartDSV\SupplierOfferType\ShippingWeightAType();
        $shippingWeight->setMeasure((int)ceil($this->part->product->weight));
        $shippingWeight->setUnit('lb');
        $this->supplierOffer->setShippingWeight($shippingWeight);

        $shippingDimensionsDepth = new ShippingDimensionsDepthAType();

        $shippingDimensionsDepth->setMeasure($dimensions[2]);
        $shippingDimensionsDepth->setUnit("in");
        $this->supplierOffer->setShippingDimensionsDepth($shippingDimensionsDepth);

        $shippingDimensionsHeight = new ShippingDimensionsHeightAType();

        $shippingDimensionsHeight->setMeasure($dimensions[0]);
        $shippingDimensionsHeight->setUnit("in");

        $this->supplierOffer->setShippingDimensionsHeight($shippingDimensionsHeight);


        $shippingDimensionsWidth = new ShippingDimensionsWidthAType();

        $shippingDimensionsWidth->setMeasure($dimensions[1]);
        $shippingDimensionsWidth->setUnit("in");
        $this->supplierOffer->setShippingDimensionsWidth($shippingDimensionsWidth);

    }
    public function addPrice()
    {
        $this->supplierOffer->setPrice(round($this->part->price * 1.25 * 100) / 100);
    }
    public function addStatic()
    {
        $this->supplierOffer->setMustShipAlone("No");
        $this->supplierOffer->setIsPreorder("No");
        $this->supplierOffer->setShipsInOriginalPackaging("Yes");
    }
    public function setAll()
    {
        $this->addPrice();
        $this->addDimensions();
        $this->addStatic();
    }
    public function getSupplierOffer() : SupplierOfferType
    {
        return $this->supplierOffer;
    }

}