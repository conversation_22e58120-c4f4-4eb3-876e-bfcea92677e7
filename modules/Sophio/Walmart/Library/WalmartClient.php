<?php

namespace Sophio\Walmart\Library;

use App\Exceptions\ExceptionMailAction;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Message;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use phpseclib\Crypt\RSA;

class WalmartClient
{
    private $config;
    private $timestamp;
    private $signature;
    private $endpoint;
    private $method;
    private $responseString;
    private $responseHeaders;
    private $correlationId;
    private $body;
    private $accessToken = null;
    private $headers = [];
    private $tokenbased = true;

    public function __construct()
    {
        $this->tokenbased = config('.sophio.walmart.tokenbased') ?? true;
        $this->config = [
            'consumerId' => config('sophio.walmart.consumerId'),
            'privateKey' => config('sophio.walmart.private_key'),
            'sophio_accountnum' => config('sophio.walmart.sophio_accountnum'),
            'base_url' => config('sophio.walmart.base_url'),
            'channel.type' => config('sophio.walmart.channel_type')

        ];
        $this->timestamp = (int)(microtime(true) * 1000);
    }

    public function getAcccessToken()
    {
        $this->accessToken = Cache::get('walmart_access_token');
        if ($this->accessToken === null) {
            Log::channel('walmart')->error('Aquire new token');
            $url = $this->config['base_url'] . '/v3/token';
            $headers = [
                'Accept' => 'application/xml',
                'WM_QOS.CORRELATION_ID' => (string)$this->getCorrelationid(),

            ];
            $body = ['grant_type' => 'client_credentials'];
            Log::channel('walmart')->error($headers);
            try {
                $response = Http::asForm()->withOptions(['http_errors' => false])
                    ->withHeaders($headers)
                    ->withBasicAuth(config('sophio.walmart.clientId'), config('sophio.walmart.secret'))
                    ->post($url, $body);

            } catch (RequestException $e) {
                (new ExceptionMailAction())(new \Exception('Failure while trying to get a new Walmart token: ' . $e->getMessage()));
                Log::channel('walmart')->error('Failure while trying to get a new Walmart token: ' . $e->getMessage());
                if ($e->hasResponse()) {
                    (new ExceptionMailAction())(new \Exception('Failure while trying to get a new Walmart token: ' . $e->getMessage() . "\n" . $e->getResponse()->getBody()->getContents()));
                    Log::channel('walmart')->error('Failure while trying to get a new Walmart token: ' . $e->getMessage() . "\n" . $e->getResponse()->getBody()->getContents());
                } else {
                    (new ExceptionMailAction())(new \Exception('Failure while trying to get a new Walmart token: ' . $e->getMessage()));
                    Log::channel('walmart')->error('Failure while trying to get a new Walmart token: ' . $e->getMessage());
                }

            }
            $responseString = $response->body();
            $transaction = new \Sophio\Walmart\Models\Transaction();
            $transaction->correlation_id=$this->getCorrelationid();
            $transaction->endpoint = '/v3/token';
            $transaction->object = 'Token';
            $transaction->request_body = json_encode(['headers' => $headers, 'body' => $body]);
            $transaction->response_body = $responseString;
            $transaction->save();
            $xx = simplexml_load_string($responseString, \SimpleXMLElement::class, LIBXML_NOCDATA);
            $this->accessToken = (string)$xx->accessToken;
            if ($this->accessToken !== null && $this->accessToken !== "") {

                Cache::set('walmart_access_token', $this->accessToken, (int)$xx->expiresIn - 10);
            } else {
                Log::channel('walmart')->error('Could not retrieve new Walmart token: ' . $responseString);
                (new ExceptionMailAction())(new \Exception('Could not retrieve new Walmart token: ' . $responseString));
                $this->accessToken = null;
            }
        } else {
            Log::channel('walmart')->error('re use token ' . $this->accessToken);
        }
        return $this->accessToken;
    }

    public
    function calculateSignature($requestUrl, $requestMethod)
    {

        $this->timestamp = (int)(microtime(true) * 1000);
        /**
         * Append values into string for signing
         */
        $message = $this->config['consumerId'] . "\n" . $requestUrl . "\n" . strtoupper($requestMethod) . "\n" . $this->timestamp . "\n";

        /**
         * Get RSA object for signing
         */
        $rsa = new RSA();
        $decodedPrivateKey = base64_decode($this->config['privateKey']);
        $rsa->setPrivateKeyFormat(RSA::PRIVATE_FORMAT_PKCS8);
        $rsa->setPublicKeyFormat(RSA::PRIVATE_FORMAT_PKCS8);

        /**
         * Load private key
         */
        if ($rsa->loadKey($decodedPrivateKey)) {
            /**
             * Make sure we use SHA256 for signing
             */
            $rsa->setHash('sha256');
            $rsa->setSignatureMode(RSA::SIGNATURE_PKCS1);
            $signed = $rsa->sign($message);

            /**
             * Return Base64 Encode generated signature
             */
            $this->signature = base64_encode($signed);
            return $this->signature;

        } else {
            Log::channel('walmart')->error('Unable to load private key ');
            return null;
        }
    }

    public
    function getConfig()
    {
        return $this->config;
    }

    public
    function getSignature()
    {
        return $this->signature;
    }

    public
    function getTimestamp()
    {
        return $this->timestamp;
    }

    public
    function setEndpoint($endpoint)
    {
        $this->endpoint = $endpoint;
    }

    public
    function setMethod($method)
    {
        $this->method = $method;
    }

    public
    function setCorrelationId($correlationId)
    {
        $this->correlationId = $correlationId;
    }

    public
    function getCorrelationid()
    {
        return $this->correlationId;
    }
    public function getHeaders()
    {
        return $this->headers;
    }
    public
    function setBody($body)
    {
        $this->body = $body;
    }

    public function addHeader($key, $value)
    {
        $this->headers[$key] = $value;
    }
    public function getResponseHeaders()
    {
        return $this->responseHeaders;
    }
    public function execute()
    {

        if (!$this->correlationId) {
            $this->setCorrelationId(Str::uuid());
        }
        $url = $this->config['base_url'] . $this->endpoint;
        if ($this->tokenbased == false) {
            if ($this->calculateSignature($url, $this->method) === null) {
                return [''];
            }
            $this->headers = array_merge($this->headers, [
                'WM_CONSUMER.ID' => $this->config['consumerId'],
                'WM_SEC.AUTH_SIGNATURE' => $this->getSignature(),
                'WM_SEC.TIMESTAMP' => $this->getTimestamp(),
                'WM_SVC.NAME' => 'Drop Ship Vendor Services',
                'WM_QOS.CORRELATION_ID' => (string)$this->getCorrelationid(),
                'WM_CONSUMER.CHANNEL.TYPE' => $this->config['channel.type'],

            ]);
        } else {
            $this->headers = array_merge($this->headers, [
                'WM_CONSUMER.ID' => $this->config['consumerId'],
                'WM_SEC.ACCESS_TOKEN' => $this->getAcccessToken(),
                'WM_SEC.TIMESTAMP' => $this->getTimestamp(),
                'WM_SVC.NAME' => 'Drop Ship Vendor Services',
                'WM_QOS.CORRELATION_ID' => (string)$this->getCorrelationid(),
                'WM_CONSUMER.CHANNEL.TYPE' => $this->config['channel.type'],

            ]);
        }
        try {
            Log::channel('walmart')->info([$url, $this->headers]);
            if ($this->method == 'POST') {

                if (isset($this->headers['Content-Type']) && $this->headers['Content-Type'] == 'application/json') {
                    $response = Http::withOptions(['http_errors' => false])->withHeaders($this->headers)->withBody($this->body, 'application/json')->post($url);
                } else {
                    if (!empty($this->body)) {
                        if (isset($this->headers['Content-Type']) && $this->headers['Content-Type'] == 'application/xml') {
                            $response = Http:: withOptions(['http_errors' => false])->withHeaders($this->headers)->withBody($this->body, 'application/xml')->post($url);
                        } else {
                            $response = Http:: withOptions(['http_errors' => false])->withHeaders($this->headers)->attach('file', $this->body)->post($url);
                        }

                    } else {
                        $response = Http:: withOptions(['http_errors' => false])->withHeaders($this->headers)->post($url);
                    }

                }
            } else {
                $response = Http:: withOptions(['http_errors' => false])->withHeaders($this->headers)->get($url);
            }


            if (!$response->successful()) {
                $this->responseString = $response->body();
                //  Log::channel('walmart')->info($response->body());
            } else {
                $this->responseString = $response->body();
                //     Log::channel('walmart')->info($response->getBody()->getContents());
            }
            $this->responseHeaders= $response->headers();

        } catch (\Throwable $e) {

            Log::channel('walmart')->error('exception:' . $e->getMessage());
            if (method_exists($e, 'getResponse')) {

                Log::channel('walmart')->error('response error' . $e->getResponse());
                return [$e->getResponse()];
            } else {
                Log::channel('walmart')->error('exception2:' . $e->getMessage());
                return [$e->getMessage()];
            }
        }
        return [$this->responseString];

    }

}
