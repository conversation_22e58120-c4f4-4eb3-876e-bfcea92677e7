<?php

namespace Sophio\Walmart\Library;

use Sophio\Common\Models\PIM\Part;
use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\BaseTypesHandler;
use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\XmlSchemaDateHandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use JMS\Serializer\Handler\HandlerRegistryInterface;
use JMS\Serializer\SerializerBuilder;
use Sophio\Common\Services\IsoCodesService;
use Sophio\Walmart\Models\FeedItem;
use Sophio\Walmart\Models\MarketplaceItem;
use WalmartDSV\SupplierLogisticsType;


class WalmartBulkFeed
{
    private $settings = ['with_logistics' => true, 'with_offer' => true];
    private $serializerBuilder;
    private $xml;
    private $banned_words = [
        'evaporator',
        'refrigerant',
        'liquid',
        'chemical',
        'chemicals'
    ];
    private $items;
    private $supplierItemFeed;

    public function __construct($settings)
    {

        $this->settings = array_merge($this->settings, $settings);
        $this->serializerBuilder = SerializerBuilder::create();

        $this->serializerBuilder->addMetadataDir(config('sophio.walmart.metadata_folder'), 'WalmartDSV');
        $this->serializerBuilder->setCacheDir(storage_path('cache'));
        $this->serializerBuilder->configureHandlers(
            function (HandlerRegistryInterface $handler) {
                $this->serializerBuilder->addDefaultHandlers();
                $handler->registerSubscribingHandler(new BaseTypesHandler()); // XMLSchema List handling
                $handler->registerSubscribingHandler(new XmlSchemaDateHandler()); // XMLSchema date handling
            }
        );

    }

    public function createSupplierItemFeed()
    {
        $supplierItemFeedHeader = new \WalmartDSV\SupplierItemFeedHeader();
        $supplierItemFeedHeader->setFeedDate(new \DateTime());
        $supplierItemFeedHeader->setVersion('3.2');
        $supplierItemFeedHeader->setRequestId($this->settings['requestId']);
        $supplierItemFeedHeader->setRequestBatchId($this->settings['requestBatchId']);
        $supplierItemFeedHeader->setMart('WALMART_US');
        $supplierItemFeed = new \WalmartDSV\SupplierItemFeed();
        $supplierItemFeed->setSupplierItemFeedHeader($supplierItemFeedHeader);
        return $supplierItemFeed;
    }

    public function createSupplierItem(Part $part,$package=null)
    {
        if (!isset($this->settings['with_offer'])) {
            $with_offer = true;
        } else {
            $with_offer = $this->settings['with_offer'];
        }
        if (!isset($this->settings['with_logistics'])) {
            $with_logistics = true;
        } else {
            $with_logistics = $this->settings['with_logistics'];
        }
        if (!isset($this->settings['product.partial'])) {
            $partial = null;
        } else {
            $partial = $this->settings['product.partial'];
        }



        if($package!=null) {
            $supplierProductFactory = new SupplierProductPack($part);
            $supplierProductFactory->setPackage($package);
        }else{
            $supplierProductFactory = new SupplierProduct($part);
        }
            if ($partial == null) {
                $supplierProductFactory->setAll();
            } else {
                if (is_string($partial)) {
                    $supplierProductFactory->$partial();
                } elseif (is_array($partial)) {
                    foreach ($partial as $p) {
                        $supplierProductFactory->$p();
                    }
                } else {
                    //throw some error?
                }
            }
        $supplierItem = new \WalmartDSV\SupplierItem();
        $supplierItem->setSupplierProduct($supplierProductFactory->getSupplierProduct());
        $supplierItem->setFeedDate(new \DateTime());
        if ($with_offer === true) {
            if($package!=null) {
                $supplierOfferFactory = new SupplierOfferPack($part);
                $supplierOfferFactory->setPackage($package);
            }else{
                $supplierOfferFactory = new SupplierOffer($part);
            }
            $supplierOfferFactory->setAll();
            $supplierItem->setSupplierOffer($supplierOfferFactory->getSupplierOffer());
        }
        if ($with_logistics === true) {
            if($package!=null) {
                $supplierLogisticsFactory = new SupplierLogistsicsPack($part);
                $supplierLogisticsFactory->setPackage($package);
            }else{
                $supplierLogisticsFactory = new SupplierLogistics($part);
            }

            $supplierLogisticsFactory->setAll();
            $supplierItem->setSupplierLogistics($supplierLogisticsFactory->getSupplierLogistics());
        }
        return $supplierItem;
    }
    public function makeFeedItem($part,$feeddb,$simulate,$package=null)
    {
        $added=0;
        $feed_item = new FeedItem();
        $feed_item->feed_id = $feeddb->id;
        $feed_item->data = $part->toJson();
        $feed_item->sku = trim($part->aaiabrandid) . '-' . trim($part->mfgcode) . '-' . trim($part->product->part_number_unformatted);
        $feed_item->mfg_code = trim($part->mfgcode);
        $feed_item->part_number_unformatted = trim($part->product->part_number_unformatted);
        try {

        if($package===null) {
            $m = MarketplaceItem::firstOrNew(['market' => 'WAL', 'marketSellerSku' =>    $feed_item->sku]);
            if(!$m->exists) {
                $feed_item->gtin = IsoCodesService::extractGtin([$part->gtin,$part->upc]);
                $m->mfg_code = $part->product->mfg_code;
                $m->part_number = $part->product->part_number;
                $m->part_number_unformatted = $part->product->part_number_unformatted;
                $m->aaiabrandid = $part->product->aaiabrandid;
                $m->status = "TRANSMIT";
                $m->marketSku = null;
                $m->marketplacefeedspk = 2;
                $m->gtin = IsoCodesService::extractGtin([$part->gtin,$part->upc]);
                $m->upc = IsoCodesService::extractUpc([$part->upc,$part->gtin]);
                $m->save();
                $item = $this->createSupplierItem($part);

            }elseif(isset($this->settings['SkuUpdate']) && $this->settings['SkuUpdate']===true) {
                if($m->gtin==null) {
                    $m->gtin = IsoCodesService::extractGtin([$part->gtin,$part->upc]);
                    $m->upc = IsoCodesService::extractUpc([$part->upc,$part->gtin]);
                    $m->save();
                }
                $item = $this->createSupplierItem($part);
            }

        }else{
            $feed_item->sku = trim($part->aaiabrandid) . '-' . trim($part->mfgcode) . '-' . trim($part->product->part_number_unformatted).'-'.$package->qty;
            $feed_item->gtin = $package->gtin;
            $m = MarketplaceItem::firstOrNew(['market' => 'WAL', 'marketSellerSku' => $feed_item->sku]);
            if(!$m->exists) {
                $m->mfg_code = $part->product->mfg_code;
                $m->part_number = $part->product->part_number;
                $m->part_number_unformatted = $part->product->part_number_unformatted;
                $m->aaiabrandid = $part->product->aaiabrandid;
                $m->gtin = $package->gtin;
                $m->upc = IsoCodesService::extractUpc($package->gtin);
                $m->status = "TRANSMIT";
                $m->marketSku = null;
                $m->marketplacefeedspk = 2;
                $m->pack_qty = $package->qty;
                $m->save();
                $item = $this->createSupplierItem($part, $package);
            }elseif(isset($this->settings['SkuUpdate']) && $this->settings['SkuUpdate']===true) {
                $item = $this->createSupplierItem($part,$package);
            }
        }
        if (isset($item) && $item != false) {
            $feed_item->data = $this->buildItemXML($item);
            $this->supplierItemFeed->addToSupplierItem($item);
            $added++;
            if ($simulate !== true) {
                DB::disableQueryLog();
                $feed_item->save();
                DB::enableQueryLog();
            }
        } else {
            $feed_item->status = "UNBOARDABLE";
            Log::channel('walmart')->info($part->part_number_unformatted . ' excluded');
        }
        } catch (\Exception $e) {
            $feed_item->status = "UNBOARDABLE";
            $feed_item->save();
            Log::channel('walmart')->error($part);
            Log::channel('walmart')->error($e->getMessage());
            Log::channel('walmart')->error($e->getTraceAsString());

        }

        return $added;
    }
    public function processParts($parts, $feeddb, $simulate)
    {
        $items_to_send = 0;
        $this->supplierItemFeed = $this->createSupplierItemFeed();
        foreach ($parts as $part) {
            $part->market = "WAL";
            [$price, $msrp, $azmn, $fallback_warehouse] = WalmartUtils::calculatePrices($part, '288065');
            $part->price = $price;
            $part->msrp = $msrp;
            $part->amazon_landed_price = $azmn;
            $part->fallback_warehouse = $fallback_warehouse;

            if (!isset($this->settings['packages_only']) || (isset($this->settings['packages_only']) && $this->settings['packages_only']==false)) {
                $added = $this->makeFeedItem($part, $feeddb, $simulate);
                $items_to_send+=$added;

            }
            if(isset($this->settings['packages']) && is_array($this->settings['packages']) && count($this->settings['packages'])>0) {

                foreach($part->product->package as $package) {
                    if(in_array($package->uom,$this->settings['packages'])) {
                        $added = $this->makeFeedItem($part, $feeddb, $simulate,$package);
                        $items_to_send+=$added;
                    }
                }
            }

        }
        return $items_to_send;
    }

    public function getXML()
    {


        Log::channel('walmart')->info('building XML...');
        return $this->buildXML($this->supplierItemFeed);
    }


    public function buildItemXML($item)
    {
        $serializer = $this->serializerBuilder->build();

        $xml = $serializer->serialize($item, 'xml');

        //   Log::channel('walmart')->info($xml);
        return $xml;

    }

    public function buildXML($supplierItemFeed)
    {
        $serializer = $this->serializerBuilder->build();
        $this->xml = $serializer->serialize($supplierItemFeed, 'xml');
        return $this->xml;
    }
}