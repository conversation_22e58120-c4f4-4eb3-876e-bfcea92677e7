<?php

namespace Sophio\Walmart\Library\Serializers;

use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\BaseTypesHandler;
use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\XmlSchemaDateHandler;
use <PERSON><PERSON>\Serializer\Handler\HandlerRegistryInterface;
use <PERSON><PERSON>\Serializer\SerializerBuilder;

class Base
{
    public $item;
    protected string $xml;
    protected $serializer;
    protected $class;
    protected $metadata;
    protected $namespace;

    public function __construct()
    {
        $this->metadata =base_path() . '/modules/Sophio/Walmart/OrderManagement/metadata';
        $this->namespace = 'WalmartAPI\Order';

        $serializerBuilder = SerializerBuilder::create();
        $serializerBuilder->addMetadataDir($this->metadata, $this->namespace);
        $serializerBuilder->configureHandlers(
            function (HandlerRegistryInterface $handler) use ($serializerBuilder) {
                $serializerBuilder->addDefaultHandlers();
                $handler->registerSubscribingHandler(new BaseTypesHandler()); // XMLSchema List handling
                $handler->registerSubscribingHandler(new XmlSchemaDateHandler()); // XMLSchema date handling

            }
        );
        $this->serializer = $serializerBuilder->build();
    }
    public function setContent( $request)
    {
        $this->xml = $request;
    }
    public function buildXML()
    {
        return $this->serializer->serialize($this->item, 'xml');

    }
 }