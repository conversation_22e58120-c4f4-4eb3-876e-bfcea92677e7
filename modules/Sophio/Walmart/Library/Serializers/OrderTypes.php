<?php

namespace Sophio\Walmart\Library\Serializers;

use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\BaseTypesHandler;
use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\XmlSchemaDateHandler;
use <PERSON><PERSON>\Serializer\Handler\HandlerRegistryInterface;
use <PERSON><PERSON>\Serializer\SerializerBuilder;
use <PERSON>MS\Serializer\Visitor\Factory\XmlSerializationVisitorFactory;
 use WalmartAPI\Order\OrderType;

class OrderTypes
    extends Base
{
    public function __construct()
    {
        $this->metadata =base_path() . '/modules/Sophio/Walmart/OrderManagement/metadata';
        $this->namespace = 'WalmartAPI\Order';
        parent::__construct();

        $serializerBuilder = SerializerBuilder::create();

        $serializerBuilder->addMetadataDir($this->metadata, $this->namespace);

        $serializerBuilder->configureHandlers(
            function (HandlerRegistryInterface $handler) use ($serializerBuilder) {
                $serializerBuilder->addDefaultHandlers();
                $handler->registerSubscribingHandler(new BaseTypesHandler()); // XMLSchema List handling
                $handler->registerSubscribingHandler(new XmlSchemaDateHandler()); // XMLSchema date handling

            }
        );
        $serializerBuilder->  setPropertyNamingStrategy(
            new \JMS\Serializer\Naming\SerializedNameAnnotationStrategy(
                new \JMS\Serializer\Naming\IdenticalPropertyNamingStrategy()
            )
        );
        $visitor =  new XmlSerializationVisitorFactory();
        $visitor->setDefaultRootName('order','http://walmart.com/mp/v3/orders');
        $serializerBuilder->setSerializationVisitor('xml', $visitor);
        $this->serializer = $serializerBuilder->build();
    }
    public function buildXML()
    {
        return $this->serializer->serialize($this->item, 'xml',null,OrderType::class);

    }
}