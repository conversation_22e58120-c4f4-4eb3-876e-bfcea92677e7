<?php

namespace So<PERSON>o\Walmart\Library;

use App\Library\Walmart\BaseTypesHandler;
use App\Library\Walmart\HandlerRegistryInterface;
use App\Library\Walmart\SerializerBuilder;
use App\Library\Walmart\XmlSchemaDateHandler;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Models\ItemResponse;
use Sophio\Walmart\Models\MarketplaceItem;

class Items

{
    protected $client;
    protected $settings;
    protected $raw_response;
    public function __construct()
    {

        $this->client = new \Sophio\Walmart\Library\WalmartClient();
    }

    public function setSettings($settings)
    {
        $this->settings = $settings;
    }
    public function getInventory($sku,$shipNode)
    {
        $this->client->setMethod('GET');
        $url = '/v3/inventory';
        $url .= "?sku=".$sku.'&shipNode='.$shipNode;
        $this->client->setEndpoint($url);
        $this->client->addHeader('Content-Type', 'application/json');
        try {
            $responseRaw = $this->client->execute();
        } catch (\ErrorException|\Throwable $e) {
            Log::channel('walmart')->error('getItem '.$sku.' '.$e->getMessage());
            return null;
        }
        try {
            $response = simplexml_load_string_nons($responseRaw[0]);

        } catch (\ErrorException $e) {
            Log::channel('walmart')->error($e->getMessage());
            return null;
        }
        return $response;
    }
    public function getItem($sku,$type='GTIN')
    {
        $this->client->setMethod('GET');
        $url = '/v3/items';
        $correlation_id = (string)\Str::uuid();
        $url .= "/$sku?productIdType=".$type;

        $this->client->setEndpoint($url);
        try {
        $responseRaw = $this->client->execute();
        } catch (\ErrorException|\Throwable $e) {
            Log::channel('walmart')->error('getItem '.$sku.' '.$e->getMessage());
            return null;
        }

        $transaction = new \Sophio\Walmart\Models\Transaction();
        $transaction->correlation_id = $correlation_id;
        $transaction->endpoint = $url;
        $transaction->object = 'Item';
        $transaction->object_id = 0;
        $this->raw_response = $transaction->response_body = $responseRaw[0];
        $transaction->save();
        try {
            $response = simplexml_load_string_nons($responseRaw[0]);
            Log::channel('walmart')->error($responseRaw[0]);
        } catch (\ErrorException $e) {
            Log::channel('walmart')->error($e->getMessage());
            return null;
        }


        foreach ($response->ItemResponse as $itemResponse) {

            if (isset($itemResponse->ns2_sku)) {
                $item = ItemResponse::firstOrNew(['itemid' => $itemResponse->ns2_sku]);
                $composedSku = composedSku( $itemResponse->ns2_sku);
                $item->mfg_code = $composedSku[1];
                $item->part_number_unformatted = $composedSku[2];
                $item->sku = $itemResponse->ns2_sku;

                $item->wpid = $itemResponse->ns2_wpid;
                $item->upc = $itemResponse->ns2_upc;
                $item->gtin = $itemResponse->ns2_gtin;
                $item->product_name = $itemResponse->ns2_productName;
                $item->shelf = $itemResponse->ns2_shelf;
                $item->product_type = $itemResponse->ns2_productType;
                $item->price = $itemResponse->ns2_price->ns2_amount;
                $item->publishedStatus = $itemResponse->ns2_publishedStatus;
                $item->lifecycleStatus = $itemResponse->ns2_lifecycleStatus;
                if(isset($itemResponse->ns2_unpublishedReasons)) {
                    $item->unpublishedReasons = json_encode($itemResponse->ns2_unpublishedReasons);

                }

                $item->save();
                return $item;

            }
        }
        return null;
    }
    public function getRawResponse()
    {
        return $this->raw_response;
    }
    public function getAllItems($nextCursor = '*')
    {
        $this->client->setMethod('GET');
        $url = '/v3/items';
        $correlation_id = (string)\Str::uuid();
        $url .= "?nextCursor=$nextCursor";
        $this->client->setEndpoint($url);
        $responseRaw = $this->client->execute();
        $transaction = new \Sophio\Walmart\Models\Transaction();
        $transaction->correlation_id = $correlation_id;
        $transaction->endpoint = '/v3/items';
        $transaction->object = 'AllItems';
        $transaction->object_id = 0;
        $transaction->response_body = $responseRaw[0];
        $transaction->save();
        try {
            $response = simplexml_load_string_nons($responseRaw[0]);
        } catch (\ErrorException $e) {
            return false;
        }


        foreach ($response->ItemResponse as $itemResponse) {


            $item = ItemResponse::firstOrNew(['itemid' => $itemResponse->sku]);
            $item->sku = $itemResponse->ns2_sku;

            $item->wpid = $itemResponse->ns2_wpid;
            $item->upc = $itemResponse->ns2_upc;
            $item->gtin = $itemResponse->ns2_gtin;
            $item->product_name = $itemResponse->ns2_productName;
            $item->shelf = $itemResponse->ns2_shelf;
            $item->product_type = $itemResponse->ns2_productType;
            $item->price = $itemResponse->ns2_price->ns2_amount;
            $item->publishedStatus = $itemResponse->ns2_publishedStatus;
            $item->lifecycleStatus = $itemResponse->ns2_lifecycleStatus;
            $item->unpublishedReasons = json_encode($itemResponse->ns2_unpublishedReasons);
            $item->save();
        }
        return $response->nextCursor ?? false;
    }
}

function simplexml_load_string_nons($xml, $sxclass = 'SimpleXMLElement', $nsattr = false, $flags = null)
{
    // Validate arguments first
    if (!is_string($sxclass) or empty($sxclass) or !class_exists($sxclass)) {
        trigger_error('$sxclass must be a SimpleXMLElement or a derived class.', E_USER_WARNING);
        return false;
    }
    if (!is_string($xml) or empty($xml)) {
        trigger_error('$xml must be a non-empty string.', E_USER_WARNING);
        return false;
    }

    // Load XML if URL is provided as XML
    if (preg_match('~^https?://[^\s]+$~i', $xml) || file_exists($xml)) {
        $xml = file_get_contents($xml);
    }

    // Let's drop namespace definitions
    if (stripos($xml, 'xmlns=') !== false) {
        $xml = preg_replace('~[\s]+xmlns=[\'"].+?[\'"]~i', null, $xml);
    }

    // I know this looks kind of funny but it changes namespaced attributes
    if (preg_match_all('~xmlns:([a-z0-9]+)=~i', $xml, $matches)) {
        foreach (($namespaces = array_unique($matches[1])) as $namespace) {
            $escaped_namespace = preg_quote($namespace, '~');
            $xml = preg_replace('~[\s]xmlns:' . $escaped_namespace . '=[\'].+?[\']~i', null, $xml);
            $xml = preg_replace('~[\s]xmlns:' . $escaped_namespace . '=["].+?["]~i', null, $xml);
            $xml = preg_replace('~([\'"\s])' . $escaped_namespace . ':~i', '$1' . $namespace . '_', $xml);
        }
    }

    // Let's change <namespace:tag to <namespace_tag ns="namespace"
    $regexfrom = sprintf('~<([a-z0-9]+):%s~is', !empty($nsattr) ? '([a-z0-9]+)' : null);
    $regexto = strlen($nsattr) ? '<$1_$2 ' . $nsattr . '="$1"' : '<$1_';
    $xml = preg_replace($regexfrom, $regexto, $xml);
    // Let's change </namespace:tag> to </namespace_tag>
    $xml = preg_replace('~</([a-z0-9]+):~is', '</$1_', $xml);

    // Default flags I use
    if (empty($flags)) $flags = LIBXML_COMPACT | LIBXML_NOBLANKS | LIBXML_NOCDATA;
    // Now load and return (namespaceless)
    return $xml = simplexml_load_string($xml, $sxclass, $flags);
}