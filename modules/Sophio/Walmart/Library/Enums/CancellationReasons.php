<?php

namespace So<PERSON><PERSON>\Walmart\Library\Enums;

enum CancellationReasons:string
{
    case SUPPLIER_CANCEL='SUPPLIER_CANCEL';
    case SUPPLIER_CANCEL_CUSTOMER_REQUEST='SUPPLIER_CANCEL_CUSTOMER_REQUEST';
    case SUPPLIER_CANCEL_BACKORDER='SUPPLIER_CANCEL_BACKORDER';
    case SUPPLIER_CANCEL_DISCONTINUE='SUPPLIER_CANCEL_DISCONTINUE';
    case SUPPLIER_CANCEL_UNRECOGNIZED='SUPPLIER_CANCEL_UNRECOGNIZED';
    public function toString():string
    {
        return match($this) {
            self::SUPPLIER_CANCEL=>'SUPPLIER_CANCEL',
            self::SUPPLIER_CANCEL_CUSTOMER_REQUEST=>'SUPPLIER_CANCEL_CUSTOMER_REQUEST',
            self::SUPPLIER_CANCEL_BACKORDER=>'SUPPLIER_CANCEL_BACKORDER',
            self::S<PERSON><PERSON><PERSON><PERSON>_CANCEL_DISCONTINUE=>'SUPPLIER_<PERSON><PERSON><PERSON>_DISCONTINUE',
            self::SUPPLIER_CANCEL_UNRECOGNIZED=>'SUPPLIER_CANCEL_UNRECOGNIZED'
        };
    }

}
