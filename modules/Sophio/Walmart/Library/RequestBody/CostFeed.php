<?php

namespace Sophio\Walmart\Library\RequestBody;

class CostFeed implements RequestBodyInterface
{
    public $header;
    public $Cost;
    public function __construct()
    {
        $this->header =  new \stdClass;
        $this->header->version = '1';
    }

    public function setData($inventory)
    {
        $this->Cost = $inventory;
    }
    public function getCount()
    {
        return count($this->Cost);
    }
    public function getData()
    {
        return $this->Cost;
    }
    public function getBody()
    {
        return json_encode($this);
    }
}