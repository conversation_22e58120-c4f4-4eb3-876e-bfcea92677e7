<?php

namespace So<PERSON>o\Walmart\Library\Feeds;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\FBS\Supplier;

class InventoryFeeds
{
    protected $settings;

    public function __construct($settings)
    {
        $this->settings = $settings;
    }

    public function getChunkSize(): int
    {
        return 9000;
    }

    public function getAPILimit(): int
    {
        // DVS_INVENTORY allows 50 per hour
        return 40;
    }

    public function modelBySupplierOnlyDiff(Supplier $sup): Builder
    {

        Config::set('tenant_db', $this->settings['buyer_db']);
        Log::error( $this->settings['buyer_db']);
        $news = ProductWarehouse::where('product_warehouse.contactpk', $sup->contactpk)
            ->whereNotNull('product_warehouse.aaiabrandid')
            ->whereNull('product_warehouse_old.pk')
            ->leftJoin('product_warehouse_old', function ($join) {
                $join->on('product_warehouse.mfgcode', '=', 'product_warehouse_old.mfgcode');
                $join->on('product_warehouse.part_number_unformatted', '=', 'product_warehouse_old.part_number_unformatted');
                $join->on('product_warehouse.contactpk', '=', 'product_warehouse_old.contactpk');

            })
            ->join($this->settings['tenant_db'].'.marketplaceitems',function($join){
                $join->on('marketplaceitems.mfg_code', '=', 'product_warehouse.mfgcode');
                $join->on('marketplaceitems.part_number_unformatted', '=', 'product_warehouse.part_number_unformatted');
            })
            ->whereNotNull('marketplaceitems.marketSku')
            ->whereIn('marketplaceitems.status',['PUBLISHED'])
            ->select(DB::raw('product_warehouse.mfgcode,product_warehouse.part_number_unformatted,product_warehouse.aaiabrandid,product_warehouse.qty_avail,marketplaceitems.marketSellerSku,marketplaceitems.pack_qty,product_warehouse.wd_min_qty,marketplaceitems.gtin'))
            ->groupByRaw('product_warehouse.mfgcode,product_warehouse.part_number_unformatted');
        if (isset($this->settings['mfgcode'])) {
            $news = $news->where('product_warehouse.mfgcode', $this->settings['mfgcode']);
        }
        if (isset($this->settings['part_number_unformatted'])) {
            $news = $news->where('product_warehouse.part_number_unformatted', $this->settings['part_number_unformatted']);
        }
        $olds = ProductWarehouse::where('product_warehouse_old.contactpk', $sup->contactpk)
            ->whereNull('product_warehouse.pk')
            ->whereNotNull('product_warehouse_old.aaiabrandid')
            ->rightJoin('product_warehouse_old', function ($join) {
                $join->on('product_warehouse.mfgcode', '=', 'product_warehouse_old.mfgcode');
                $join->on('product_warehouse.part_number_unformatted', '=', 'product_warehouse_old.part_number_unformatted');
                $join->on('product_warehouse.contactpk', '=', 'product_warehouse_old.contactpk');

            })
            ->join($this->settings['tenant_db'].'.marketplaceitems',function($join){
                $join->on('marketplaceitems.mfg_code', '=', 'product_warehouse_old.mfgcode');
                $join->on('marketplaceitems.part_number_unformatted', '=', 'product_warehouse_old.part_number_unformatted');
            })
            ->whereNotNull('marketplaceitems.marketSku')
            ->whereIn('marketplaceitems.status',['PUBLISHED'])
            ->select(DB::raw('product_warehouse_old.mfgcode,product_warehouse_old.part_number_unformatted,product_warehouse_old.aaiabrandid,0 as qty_avail,marketplaceitems.marketSellerSku,marketplaceitems.pack_qty,product_warehouse.wd_min_qty,marketplaceitems.gtin'))
            ->groupByRaw('product_warehouse_old.mfgcode,product_warehouse_old.part_number_unformatted');
        if (isset($this->settings['mfgcode'])) {
            $olds = $olds->where('product_warehouse_old.mfgcode', $this->settings['mfgcode']);
        }
        if (isset($this->settings['part_number_unformatted'])) {
            $olds = $olds->where('product_warehouse.part_number_unformatted', $this->settings['part_number_unformatted']);
        }
        $model = ProductWarehouse::where('product_warehouse.contactpk', $sup->contactpk)
            ->whereNotNull('product_warehouse.aaiabrandid')
            ->join('product_warehouse_old', function ($join) {
                $join->on('product_warehouse.mfgcode', '=', 'product_warehouse_old.mfgcode');
                $join->on('product_warehouse.part_number_unformatted', '=', 'product_warehouse_old.part_number_unformatted');
                $join->on('product_warehouse.contactpk', '=', 'product_warehouse_old.contactpk');
                $join->on('product_warehouse.qty_avail', '<>', 'product_warehouse_old.qty_avail');
            })
            ->join($this->settings['tenant_db'].'.marketplaceitems',function($join){
                $join->on('marketplaceitems.mfg_code', '=', 'product_warehouse.mfgcode');
                $join->on('marketplaceitems.part_number_unformatted', '=', 'product_warehouse.part_number_unformatted');


            })
            ->whereNotNull('marketplaceitems.marketSku')
            ->whereIn('marketplaceitems.status',['PUBLISHED'])
            ->select(DB::raw('product_warehouse.mfgcode,product_warehouse.part_number_unformatted,product_warehouse.aaiabrandid,product_warehouse.qty_avail,marketplaceitems.marketSellerSku,marketplaceitems.pack_qty,product_warehouse.wd_min_qty,marketplaceitems.gtin'))
            ->groupByRaw('product_warehouse.mfgcode,product_warehouse.part_number_unformatted')
            ->union($news)
            ->union($olds);
        if (isset($this->settings['part_number_unformatted'])) {
            $model = $model->where('product_warehouse.part_number_unformatted', $this->settings['part_number_unformatted']);
        }
        if (isset($this->settings['mfgcode'])) {
            $model = $model->where('product_warehouse.mfgcode', $this->settings['mfgcode']);
        }

        return $model;
    }

    public function modelBySupplierFull(Supplier $sup): Builder
    {

        Config::set('tenant_db', $this->settings['buyer_db']);
        $model = ProductWarehouse::where('product_warehouse.contactpk', $sup->contactpk)
            ->join($this->settings['tenant_db'].'.marketplaceitems',function($join){
                $join->on('marketplaceitems.mfg_code', '=', 'product_warehouse.mfgcode');
                $join->on('marketplaceitems.part_number_unformatted', '=', 'product_warehouse.part_number_unformatted');
            })
            ->whereNotNull('marketplaceitems.marketSku')
            ->whereIn('marketplaceitems.status',['PUBLISHED'])
            ->select(DB::raw('product_warehouse.*,marketplaceitems.marketSellerSku,marketplaceitems.pack_qty,marketplaceitems.gtin'));
        if (isset($this->settings['part_number_unformatted'])) {
            $model = $model->where('product_warehouse.part_number_unformatted', $this->settings['part_number_unformatted']);
        }
        if (isset($this->settings['mfgcode'])) {
            $model = $model->where('mfgcode', $this->settings['mfgcode']);
        }

        return $model;
    }

    public function run($settings)
    {

    }
}