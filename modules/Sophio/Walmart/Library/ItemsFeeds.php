<?php

namespace Sophio\Walmart\Library;

use App\Events\AdminQueueMailEvent;
use App\Library\Sophio\Exporters\LesserProductsExporter;
use App\Library\Sophio\PartGrabberByProduct;
use Sophio\Common\Models\WHIACES\ProductFeed;
use Sophio\Common\Models\PIM\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Models\FeedItem;
use Sophio\Walmart\Models\MarketplaceItem;

class ItemsFeeds
{
    protected $client;
    protected $settings;
    public $feed;
    public $transaction;
    public $checkFeed;

    public function __construct()
    {

        $this->client = new \Sophio\Walmart\Library\WalmartClient();
        \Config::set('tenant_db', config('sophio.admin.default_database'));
        $this->settings = $settings = [
            'mfgcode' => '',
            'contactpk' => '',
            'onlynew' => 'Yes',
            'batch_size' => 500,
            'limit' => 500,
            'total_limit' => 500,
            'image_source' => 'hosted',
            'nofeeditem' => 'yes',
            'feed_delay' => 30,
            'mailto' => config('sophio.admin.mail_senders.exporters')
        ];
    }

    public function setSettings($settings)
    {
        $this->settings = $settings;
    }

    public function getProducts()
    {
        $product = new PartGrabberByProduct();

        if (isset($this->settings['onlynew']) && $this->settings['onlynew'] === "Yes") {
            $product->noWalmartId();
        }
        $product->setMarket('WAL');
        $product->requireAaiabrandid();
        $product->requireNoCore();
        $product->requireCost();
        $product->requireEABXPC();
        $product->requireOneQtyOrder();
        $product->requireQtyFeed();
        //$product->requireNoPartStatus();
        if(isset($this->settings['packages_only'])) {
            $product->hasPackage();
        }
        if (isset($this->settings['nofeeditem']) && $this->settings['nofeeditem'] == "yes") {
            $product->noWalmartFeedItem();
        }
        if (isset($this->settings['part_number']) && $this->settings['part_number'] != '') {
            $product->setPartNumberUnformatted(unformatString($this->settings['part_number']));
        }
        if ($this->settings['contactpk'] != "") {
            $product->setContactpk($this->settings['contactpk']);
        }
        if (isset($this->settings['qty_avail']) && $this->settings['qty_avail'] == 'stock') {
            $product->requireQtyAvailWarehouse();
        }
        $product->requireWarehouse();
        if (isset($this->settings['mfgcode']) && $this->settings['mfgcode'] != "") {
            $product->setMfgCode($this->settings['mfgcode']);
        }

        if (isset($this->settings['limit']) && $this->settings['limit'] > 0) {
            $product->setLimit($this->settings['limit']);
        }
        if (isset($this->settings['pt_nums']) && $this->settings['pt_nums'] !='') {
            $product->setPartTypes(explode(',',$this->settings['pt_nums']));
            $product->requireProductFeed();
        }
        if (isset($this->settings['image_source']) && $this->settings['image_source'] == 'hosted') {
            $product->requireHostedImageUrl();
        } else {
            $product->requireImageURL();
        }
        $product->hasGtin();
        $product->hasDimensions();
        $product->requireLongDescription();
        return $product;

    }

    public function getLesserProducts($later = false)
    {
        $product = new PartGrabberByProduct();
        if (isset($this->settings['onlynew']) && $this->settings['onlynew'] === "Yes") {
            $product->noWalmartId();
        }
        $product->setMarket('WAL');
        $product->requireNoCore();
        $product->requireCost();
        //$product->requireNoPartStatus();
        $product->requireOneQtyOrder();
        $product->requireQtyFeed();
        if ($this->settings['contactpk'] != "") {
            $product->setContactpk($this->settings['contactpk']);
        }
        if (isset($this->settings['part_number']) && $this->settings['part_number'] != '') {
            $product->setPartNumberUnformatted(unformatString($this->settings['part_number']));
        }
        if ($later == false) {
            $product->requireWarehouse();
        }

        if ($this->settings['mfgcode'] != "") {
            $product->setMfgCode($this->settings['mfgcode']);
        }
        if(isset($this->settings['packages_only'])) {

            $product->hasPackage();
        }
        if (isset($this->settings['limit']) && $this->settings['limit'] > 0) {
            $product->setLimit($this->settings['limit']);
        }
        return $product;
    }

    public function reportOnLesserCounts()
    {
        $img = $this->getLesserProducts();
        $noimage = $img->denyImageURL()->count();

        $img = $this->getLesserProducts();
        $nohostedimage = $img->denyHostedImageURL()->count();

        $dims = $this->getLesserProducts();
        $nodims = $dims->nothasDimensions()->count();

        $upcgtin = $this->getLesserProducts();
        $noupcgtin = $upcgtin->nothasUpcOrGtin()->count();

        $ea = $this->getLesserProducts();
        $noea = $ea->denyEABXPC()->count();

        $stock = $this->getLesserProducts(true);
        $nostock = $stock->denyQtyAvailWarehouse()->requireWarehouse()->count();

        return [
            'Missing image' => $noimage,
            'Missing hosted image' => $nohostedimage,
            'No dimensions' => $nodims,
            'No UPC or GTIN' => $noupcgtin,
            'UOM different than EA,BX,PC' => $noea,
            'No stock' => $nostock
        ];
    }

    public function reportOnLesser()
    {

        $lesserExp = new LesserProductsExporter('lesser_walmart_' . implode('_', [$this->settings['mfgcode'], $this->settings['contactpk'], date('Ymd')]) . '.xlsx');
        $lesserExp->open();
        $lesserExp->setHeaders(['MFG Code', 'Part Number ', 'Has no image?', 'Has hosted image?', 'Has no dimensions?', 'Has no UPC or GTIN', 'QTY UOM is not EA,BX or PC', 'No stock']);
        $this->getLesserProducts()->execute_callback(function ($p) use ($lesserExp) {
            $warehouses = $p->productwarehouse;
            $hasStock = false;
            foreach ($warehouses as $warehouse) {
                if ($warehouse->qty_avail > 0) {
                    $hasStock = true;
                }
            }
            if (($p->image_url == '') || ((int)$p->has_dimensions == 0) || (( $p->gtin == null)) || (!in_array($p->qty_uom, ['EA', 'BX', 'PC'])) || ($p->image_source != 'FBS') || ($hasStock == false)) {
                $lesserExp->addRow([$p->mfg_code, $p->part_number_unformatted,
                        $p->image_url == '' ? 'Y' : '',
                        $p->image_source == 'FBS' ? 'Y' : '',
                        (int)$p->has_dimensions == 0 ? 'Y' : '',
                        ($p->gtin == null) ? 'Y' : '',
                        !in_array($p->qty_uom, ['EA', 'BX', 'PC']) ? $p->qty_uom : '',
                        $hasStock == false ? 'Y' : ''
                    ]

                );

            }

        });
        $lesserExp->close();
        return $lesserExp->getDownloadLink();
    }

    public function  sendFeed($parts, $simulate = false)
    {

        $correlation_id = (string)\Str::uuid();
        $transaction = new \Sophio\Walmart\Models\Transaction();
        $transaction->correlation_id = $correlation_id;
        $transaction->endpoint = '/v3/feeds';
        $transaction->request_params = 'feedType=SUPPLIER_FULL_ITEM';
        Log::channel('walmart')->info($correlation_id . ' | ' . 'Starting to send a new feed');
        Log::channel('walmart')->info($correlation_id . ' | ' . json_encode($this->settings));
        Log::channel('walmart')->info($correlation_id . ' | ' . 'Collecting parts');
        if (isset($this->settings['request_prefix'])) {
            $requestId = $this->settings['request_prefix'] . date("Ymdh");
        } else {
            $requestId = 'fbs_items_' . date("Ymdh");
        }

        $requestBatchId = $requestId . '_' . $this->settings['offset'];
        $feed_settings = array_merge(['requestId' => $requestId, 'requestBatchId' => $requestBatchId], $this->settings);


        $feeder = new WalmartBulkFeed($feed_settings);

        if (!is_array($parts) || count($parts) <= 0) {
            Log::channel('walmart')->info($correlation_id . ' | ' . 'No suitable parts found');
            return false;
        }
        $feeddb = new \Sophio\Walmart\Models\Feed();
        $feeddb->feed_settings = json_encode($this->settings);
        $feeddb->type = 'Items';
        $feeddb->requestId = $requestId;
        $feeddb->requestBatchId = $requestBatchId;
        $feeddb->no_items = count($parts);
        $feeddb->status = 'NEW';
        if ($simulate !== true) {
            $feeddb->save();
        }
        $items_to_send = $feeder->processParts($parts, $feeddb, $simulate);
        if ($items_to_send === 0) {
            Log::channel('walmart')->info($correlation_id . ' | ' . 'Had ' . count($parts) . ' parts, but none suitable for sending');
            $feeddb->status = 'EMPTY';
            $feeddb->delete();
            return false;
        }
        Log::channel('walmart')->info($correlation_id . ' | ' . 'Generating XML feed for ' . count($parts));
        $xml = $feeder->getXML();


        $transaction->object = 'ItemFeed';
        $transaction->object_id = $feeddb->id;

        Log::channel('walmart')->info($correlation_id . ' | ' . 'Sending XML feed of ' . $items_to_send . ' out of ' . count($parts));
        $this->client->setCorrelationId($correlation_id);
        $this->client->addHeader('Content-Type', 'multipart/form-data');
        $this->client->setMethod('POST');
        $this->client->setEndpoint('/v3/feeds?feedType=SUPPLIER_FULL_ITEM');
        $this->client->setBody($xml);
        $transaction->request_body = $xml;
        if ($simulate !== true) {
            DB::disableQueryLog();
            $transaction->save();
            DB::enableQueryLog();

            $response = $this->client->execute();
            $transaction->response_body = $response[0];
            $transaction->save();
            $xx = simplexml_load_string($response[0], \SimpleXMLElement::class, LIBXML_NOCDATA, 'ns2', true);
            if(isset($xx->feedId)) {
                $feeddb->feedid = (string)$xx->feedId;
            }
            $feeddb->no_items = $items_to_send;
            $feeddb->status = 'SENT';

            $feeddb->save();

            Log::channel('walmart')->info($correlation_id . ' | ' . 'Done.');
            return $feeddb;
        } else {
            echo $xml;
            file_put_contents('/tmp/walmart' . $correlation_id . '.xml', $xml);
        }
    }

    public function getItem($feedItem)
    {
        $this->client->setMethod('GET');
        $url = '/v3/items/' . $feedItem->sku;
        $correlation_id = (string)\Str::uuid();
        $transaction = new \Sophio\Walmart\Models\Transaction();
        $transaction->correlation_id = $correlation_id;
        $transaction->endpoint = $url;
        $transaction->object = 'FeedItem';
        $transaction->object_id = $feedItem->id;

        $this->client->setEndpoint($url);
        $response = $this->client->execute();
        $transaction->response_body = $response[0];
        $transaction->save();
        $this->transaction = $transaction;

        return $response[0];
    }

    public function getFeedStatus(\Sophio\Walmart\Models\Feed $feed, $details = false, $offset = 0)
    {
        $this->client->setMethod('GET');
        $url = '/v3/feeds/' . $feed->feedid;

        $correlation_id = (string)\Str::uuid();
        $transaction = new \Sophio\Walmart\Models\Transaction();
        $transaction->correlation_id = $correlation_id;
        $transaction->endpoint = '/v3/feeds/' . $feed->feedid;
        $transaction->object = 'ItemFeed';
        $transaction->object_id = $feed->id;

        if ($details == true) {
            $url .= "?includeDetails=true&offset=$offset";
            $transaction->request_params = "includeDetails=true&offset=$offset";
        }

        $this->client->setEndpoint($url);
        $response = $this->client->execute();
        $transaction->response_body = $response[0];
        $transaction->save();
        $this->transaction = $transaction;
        $xx = simplexml_load_string($response[0], \SimpleXMLElement::class, LIBXML_NOCDATA, 'ns2', true);
        return $this->processFeedStatus($feed, $xx);

    }

    public function processFeedStatus(\Sophio\Walmart\Models\Feed $feed, $response)
    {
        //  Log::channel('walmart')->error('Respones Check Feed'.json_encode($response));
        if (isset($response->feedStatus)) {
            $feed->status = (string)$response->feedStatus;
            $feed->itemsReceived = (string)($response->itemsReceived ?? '');
            $feed->itemsSucceeded = (string)($response->itemsSucceeded ?? '');
            $feed->itemsFailed = (string)($response->itemsFailed ?? '');
            $feed->itemsProcessing = (string)($response->itemsProcessing ?? '');
            $feed->save();
        }
        if (isset($response->itemDetails)) {
            foreach ($response->itemDetails->itemIngestionStatus as $response_item) {
                $item = FeedItem::where('feed_id', $feed->id)->where('sku', (string)$response_item->sku)->first();
                if (!$item) {
                    Log::channel('walmart')->error(print_r($response_item->ingestionErrors, true));
                } else {

                    $item->status = (string)$response_item->ingestionStatus;
                    $item->itemid = (string)$response_item->itemid;
                    $item->wpid = (string)$response_item->wpid;


                    if (isset($response_item->ingestionErrors)) {
                        $item->errors = json_encode($response_item->ingestionErrors);
                    }
                    $item->save();
                    if ((int)$item->itemid > 0) {

                        $m = $this->saveMarketPlaceItem($item,$response_item);
                        if($m && $m->pack_qty==1) {
                            $this->saveWalmartIdInProductFeed($item->sku, $item->itemid);
                        }

                    }


                }
            }
        } else {
            Log::channel('walmart')->error('Failed to parse response');
            Log::channel('walmart')->error(print_r($response, true));
            // dd($response);
        }
        $this->feed = $feed;
        $this->checkFeed = json_decode(json_encode($response));

        return $response;
    }

    public function saveMarketPlaceItem(FeedItem $item,$response_item)
    {
        $composedSku = composedSku($item->sku);
        try {
            $product = Product::where('part_number_unformatted', $composedSku[2])->where('mfg_code', $composedSku[1])->first();
        } catch (\Exception $e) {
            Log::channel('walmart')->error('Failed to find product for  ' . $item->sku);
            Log::channel('walmart')->error($e->getMessage());
        }
        try {
            $m = MarketplaceItem::firstOrNew(['market' => 'WAL', 'marketSellerSku' => $item->sku]);
            $m->mfg_code = $product->mfg_code;
            $m->part_number = $product->part_number;
            $m->part_number_unformatted = $product->part_number_unformatted;
            $m->aaiabrandid = $product->aaiabrandid;
            foreach($response_item->productIdentifiers as $productIdentifier) {
                if ($productIdentifier->productIdType=='GTIN') {
                    $m->gtin = $productIdentifier->productId;
                }
                if ($productIdentifier->productIdType=='UPC') {
                    $m->upc = $productIdentifier->productId;
                }
            }
            if($m->exists) {
                if($m->status=='TRANSMIT') {
                    $m->status = "PUBLISH_IN_PROGRESS";
                }
            }else{
                $m->status = "PUBLISH_IN_PROGRESS";
            }


            $m->marketSku = $item->itemid;
            $m->marketplacefeedspk = 2;
            $m->save();
            return $m;
        } catch (\Exception $e) {
            Log::channel('walmart')->error('Failed to submit item to marketplaceitem ' . $item->sku);
            Log::channel('walmart')->error($e->getMessage());
        }
    }

    public function saveWalmartIdInProductFeed($sku, $itemid)
    {
        $composedSku = composedSku($sku);
        try {
            ProductFeed::where('part_number_unformatted', $composedSku[2])->where('mfg_code', $composedSku[1])->update(['walmartid' => $itemid]);
        } catch (\Exception $e) {
            Log::channel('walmart')->error('Failed to set walmart id for ' . $composedSku[2] . ' ' . $composedSku[1]);
        }


    }

    public function sendMailFeedStatus()
    {
        if (isset($this->checkFeed->feedStatus)) {
            event(new AdminQueueMailEvent('QueueJobGeneral', $this->settings['mailto'], [
                'subject' =>'Walmart onboard feed status ' . $this->feed->feedid,
                'view' => 'admin.mail.walmartonboardstatus',
              'feed' => $this->feed, 'checkFeed' => $this->checkFeed, 'transaction' => $this->transaction

            ]));

        }

    }
}
