<?php

namespace So<PERSON>o\Walmart\Jobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Library\ItemsFeeds;

class SendFeed implements ShouldQueue
{
    use   Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected  $partsgrabber;
    protected $settings;
    public $timeout = 0;
    public $tries = 1;
    public function __construct($settings=[])
    {

        $this->settings = $settings;
        $this->onQueue('longtasks');

    }
    public function handle()
    {
        \Config::set('tenant_db', 'sophio_fbs');
        config(['logging.default'=>'walmart']);

        $feeds = new ItemsFeeds();

        $feeds->setSettings($this->settings);
        $partgrabber = $feeds->getProducts();
        $partgrabber->setOffset($this->settings['offset']);
        $partgrabber->execute();

        $feed = $feeds->sendFeed($partgrabber->getParts());

        if($feed && $feed->feedid!="" && $feed->feedid!=null) {
            Log::channel('walmart')->info('Dispatching to later check '.$feed->feedid);
            CheckFeed::dispatch($feed)->delay(now()->addMinutes($this->settings['feed_delay']));
        }

    }
    public function tags()
    {
        return ['walmartsendfeed'];
    }
    public function failed(\Exception $ex)
    {
        Log::channel('walmart')->error("SendFeed failed with ".$ex->getMessage());
        $this->delete();
    }
}