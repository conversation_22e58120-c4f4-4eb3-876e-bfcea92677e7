WalmartDSV\HealthAndBeautyType:
    properties:
        medicalAids:
            expose: true
            access_type: public_method
            serialized_name: MedicalAids
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMedicalAids
                setter: setMedicalAids
            type: WalmartDSV\MedicalAidsType
        optical:
            expose: true
            access_type: public_method
            serialized_name: Optical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOptical
                setter: setOptical
            type: WalmartDSV\OpticalType
        medicineAndSupplements:
            expose: true
            access_type: public_method
            serialized_name: MedicineAndSupplements
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMedicineAndSupplements
                setter: setMedicineAndSupplements
            type: WalmartDSV\MedicineAndSupplementsType
        healthAndBeautyElectronics:
            expose: true
            access_type: public_method
            serialized_name: HealthAndBeautyElectronics
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHealthAndBeautyElectronics
                setter: setHealthAndBeautyElectronics
            type: WalmartDSV\HealthAndBeautyElectronicsType
        personalCare:
            expose: true
            access_type: public_method
            serialized_name: PersonalCare
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPersonalCare
                setter: setPersonalCare
            type: WalmartDSV\PersonalCareType
