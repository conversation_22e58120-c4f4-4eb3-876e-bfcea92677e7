WalmartDSV\MusicType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        synopsis:
            expose: true
            access_type: public_method
            serialized_name: synopsis
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSynopsis
                setter: setSynopsis
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        title:
            expose: true
            access_type: public_method
            serialized_name: title
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTitle
                setter: setTitle
            type: string
        physicalMediaFormat:
            expose: true
            access_type: public_method
            serialized_name: physicalMediaFormat
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPhysicalMediaFormat
                setter: setPhysicalMediaFormat
            type: WalmartDSV\MusicType\PhysicalMediaFormatAType
        performer:
            expose: true
            access_type: public_method
            serialized_name: performer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPerformer
                setter: setPerformer
            type: array<string>
            xml_list:
                inline: false
                entry_name: performerValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        songwriter:
            expose: true
            access_type: public_method
            serialized_name: songwriter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSongwriter
                setter: setSongwriter
            type: string
        musicGenre:
            expose: true
            access_type: public_method
            serialized_name: musicGenre
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMusicGenre
                setter: setMusicGenre
            type: string
        musicSubGenre:
            expose: true
            access_type: public_method
            serialized_name: musicSubGenre
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMusicSubGenre
                setter: setMusicSubGenre
            type: string
        targetAudience:
            expose: true
            access_type: public_method
            serialized_name: targetAudience
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTargetAudience
                setter: setTargetAudience
            type: array<string>
            xml_list:
                inline: false
                entry_name: targetAudienceValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        awardsWon:
            expose: true
            access_type: public_method
            serialized_name: awardsWon
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAwardsWon
                setter: setAwardsWon
            type: array<string>
            xml_list:
                inline: false
                entry_name: awardsWonValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        digitalAudioFileFormat:
            expose: true
            access_type: public_method
            serialized_name: digitalAudioFileFormat
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDigitalAudioFileFormat
                setter: setDigitalAudioFileFormat
            type: array<string>
            xml_list:
                inline: false
                entry_name: digitalAudioFileFormatValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recordLabel:
            expose: true
            access_type: public_method
            serialized_name: recordLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecordLabel
                setter: setRecordLabel
            type: string
        releaseDate:
            expose: true
            access_type: public_method
            serialized_name: releaseDate
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getReleaseDate
                setter: setReleaseDate
            type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\Date
        musicReleaseType:
            expose: true
            access_type: public_method
            serialized_name: musicReleaseType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMusicReleaseType
                setter: setMusicReleaseType
            type: string
        trackListings:
            expose: true
            access_type: public_method
            serialized_name: trackListings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTrackListings
                setter: setTrackListings
            type: array<WalmartDSV\TrackListingType>
            xml_list:
                inline: false
                entry_name: trackListing
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        numberOfTracks:
            expose: true
            access_type: public_method
            serialized_name: numberOfTracks
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfTracks
                setter: setNumberOfTracks
            type: int
        musicProducer:
            expose: true
            access_type: public_method
            serialized_name: musicProducer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMusicProducer
                setter: setMusicProducer
            type: string
        seriesTitle:
            expose: true
            access_type: public_method
            serialized_name: seriesTitle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeriesTitle
                setter: setSeriesTitle
            type: string
        numberInSeries:
            expose: true
            access_type: public_method
            serialized_name: numberInSeries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberInSeries
                setter: setNumberInSeries
            type: int
        isEdited:
            expose: true
            access_type: public_method
            serialized_name: isEdited
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsEdited
                setter: setIsEdited
            type: string
        isEnhanced:
            expose: true
            access_type: public_method
            serialized_name: isEnhanced
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsEnhanced
                setter: setIsEnhanced
            type: string
        edition:
            expose: true
            access_type: public_method
            serialized_name: edition
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEdition
                setter: setEdition
            type: string
        hasParentalAdvisoryLabel:
            expose: true
            access_type: public_method
            serialized_name: hasParentalAdvisoryLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasParentalAdvisoryLabel
                setter: setHasParentalAdvisoryLabel
            type: string
        ratingReason:
            expose: true
            access_type: public_method
            serialized_name: ratingReason
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRatingReason
                setter: setRatingReason
            type: string
        parentalAdvisoryLabelURL:
            expose: true
            access_type: public_method
            serialized_name: parentalAdvisoryLabelURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getParentalAdvisoryLabelURL
                setter: setParentalAdvisoryLabelURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: parentalAdvisoryLabelURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        numberOfDiscs:
            expose: true
            access_type: public_method
            serialized_name: numberOfDiscs
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfDiscs
                setter: setNumberOfDiscs
            type: int
        isAdultProduct:
            expose: true
            access_type: public_method
            serialized_name: isAdultProduct
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAdultProduct
                setter: setIsAdultProduct
            type: string
        originalLanguages:
            expose: true
            access_type: public_method
            serialized_name: originalLanguages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOriginalLanguages
                setter: setOriginalLanguages
            type: array<string>
            xml_list:
                inline: false
                entry_name: originalLanguage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        autographedBy:
            expose: true
            access_type: public_method
            serialized_name: autographedBy
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAutographedBy
                setter: setAutographedBy
            type: string
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
