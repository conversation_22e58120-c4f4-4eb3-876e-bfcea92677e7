WalmartDSV\GardenAndPatioType\SwatchImagesAType:
    properties:
        swatchImage:
            expose: true
            access_type: public_method
            serialized_name: swatchImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImage
                setter: setSwatchImage
            xml_list:
                inline: true
                entry_name: swatchImage
                namespace: 'http://walmart.com/'
            type: array<WalmartDSV\GardenAndPatioType\SwatchImagesAType\SwatchImageAType>
