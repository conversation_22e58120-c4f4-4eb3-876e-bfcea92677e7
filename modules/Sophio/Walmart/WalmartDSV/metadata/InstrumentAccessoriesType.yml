WalmartDSV\InstrumentAccessoriesType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        instrument:
            expose: true
            access_type: public_method
            serialized_name: instrument
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInstrument
                setter: setInstrument
            type: array<string>
            xml_list:
                inline: false
                entry_name: instrumentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\InstrumentAccessoriesType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\InstrumentAccessoriesType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\InstrumentAccessoriesType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\InstrumentAccessoriesType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hasIngredientList:
            expose: true
            access_type: public_method
            serialized_name: hasIngredientList
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasIngredientList
                setter: setHasIngredientList
            type: string
        ingredientListImage:
            expose: true
            access_type: public_method
            serialized_name: ingredientListImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredientListImage
                setter: setIngredientListImage
            type: string
        ingredients:
            expose: true
            access_type: public_method
            serialized_name: ingredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredients
                setter: setIngredients
            type: string
        compositeWoodCertificationCode:
            expose: true
            access_type: public_method
            serialized_name: compositeWoodCertificationCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompositeWoodCertificationCode
                setter: setCompositeWoodCertificationCode
            type: int
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        inputsAndOutputs:
            expose: true
            access_type: public_method
            serialized_name: inputsAndOutputs
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInputsAndOutputs
                setter: setInputsAndOutputs
            type: array<WalmartDSV\InputsAndOutputType>
            xml_list:
                inline: false
                entry_name: inputsAndOutput
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        displayTechnology:
            expose: true
            access_type: public_method
            serialized_name: displayTechnology
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDisplayTechnology
                setter: setDisplayTechnology
            type: string
        wirelessTechnologies:
            expose: true
            access_type: public_method
            serialized_name: wirelessTechnologies
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWirelessTechnologies
                setter: setWirelessTechnologies
            type: array<string>
            xml_list:
                inline: false
                entry_name: wirelessTechnology
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hasBluetooth:
            expose: true
            access_type: public_method
            serialized_name: hasBluetooth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBluetooth
                setter: setHasBluetooth
            type: string
        isRemoteControlIncluded:
            expose: true
            access_type: public_method
            serialized_name: isRemoteControlIncluded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsRemoteControlIncluded
                setter: setIsRemoteControlIncluded
            type: string
        batteryLife:
            expose: true
            access_type: public_method
            serialized_name: batteryLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryLife
                setter: setBatteryLife
            type: WalmartDSV\InstrumentAccessoriesType\BatteryLifeAType
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        isPortable:
            expose: true
            access_type: public_method
            serialized_name: isPortable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPortable
                setter: setIsPortable
            type: string
        recommendedUses:
            expose: true
            access_type: public_method
            serialized_name: recommendedUses
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedUses
                setter: setRecommendedUses
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedUse
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recommendedLocations:
            expose: true
            access_type: public_method
            serialized_name: recommendedLocations
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedLocations
                setter: setRecommendedLocations
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedLocation
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        sportsTeam:
            expose: true
            access_type: public_method
            serialized_name: sportsTeam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsTeam
                setter: setSportsTeam
            type: WalmartDSV\SportsTeamType
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        autographedBy:
            expose: true
            access_type: public_method
            serialized_name: autographedBy
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAutographedBy
                setter: setAutographedBy
            type: string
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\InstrumentAccessoriesType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
