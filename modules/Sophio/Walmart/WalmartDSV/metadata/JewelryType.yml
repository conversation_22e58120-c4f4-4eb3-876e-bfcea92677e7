WalmartDSV\JewelryType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        jewelryStyle:
            expose: true
            access_type: public_method
            serialized_name: jewelryStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJewelryStyle
                setter: setJewelryStyle
            type: string
        metal:
            expose: true
            access_type: public_method
            serialized_name: metal
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMetal
                setter: setMetal
            type: string
        metalStamp:
            expose: true
            access_type: public_method
            serialized_name: metalStamp
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMetalStamp
                setter: setMetalStamp
            type: array<string>
            xml_list:
                inline: false
                entry_name: metalStampValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        karats:
            expose: true
            access_type: public_method
            serialized_name: karats
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKarats
                setter: setKarats
            type: WalmartDSV\JewelryType\KaratsAType
        plating:
            expose: true
            access_type: public_method
            serialized_name: plating
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPlating
                setter: setPlating
            type: string
        gender:
            expose: true
            access_type: public_method
            serialized_name: gender
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGender
                setter: setGender
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        occasion:
            expose: true
            access_type: public_method
            serialized_name: occasion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOccasion
                setter: setOccasion
            type: WalmartDSV\OccasionType
        style:
            expose: true
            access_type: public_method
            serialized_name: style
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStyle
                setter: setStyle
            type: string
        personalRelationship:
            expose: true
            access_type: public_method
            serialized_name: personalRelationship
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPersonalRelationship
                setter: setPersonalRelationship
            type: array<string>
            xml_list:
                inline: false
                entry_name: personalRelationshipValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        certificateNumber:
            expose: true
            access_type: public_method
            serialized_name: certificateNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCertificateNumber
                setter: setCertificateNumber
            type: array<string>
            xml_list:
                inline: false
                entry_name: certificateNumberValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        birthstone:
            expose: true
            access_type: public_method
            serialized_name: birthstone
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBirthstone
                setter: setBirthstone
            type: string
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        claspType:
            expose: true
            access_type: public_method
            serialized_name: claspType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClaspType
                setter: setClaspType
            type: array<string>
            xml_list:
                inline: false
                entry_name: claspTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        backFinding:
            expose: true
            access_type: public_method
            serialized_name: backFinding
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBackFinding
                setter: setBackFinding
            type: string
        jewelrySetting:
            expose: true
            access_type: public_method
            serialized_name: jewelrySetting
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJewelrySetting
                setter: setJewelrySetting
            type: array<string>
            xml_list:
                inline: false
                entry_name: jewelrySettingValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        earringStyle:
            expose: true
            access_type: public_method
            serialized_name: earringStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEarringStyle
                setter: setEarringStyle
            type: string
        earringFeature:
            expose: true
            access_type: public_method
            serialized_name: earringFeature
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEarringFeature
                setter: setEarringFeature
            type: array<string>
            xml_list:
                inline: false
                entry_name: earringFeatureValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        braceletStyle:
            expose: true
            access_type: public_method
            serialized_name: braceletStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBraceletStyle
                setter: setBraceletStyle
            type: string
        necklaceStyle:
            expose: true
            access_type: public_method
            serialized_name: necklaceStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNecklaceStyle
                setter: setNecklaceStyle
            type: string
        chainLength:
            expose: true
            access_type: public_method
            serialized_name: chainLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getChainLength
                setter: setChainLength
            type: WalmartDSV\JewelryType\ChainLengthAType
        chainPattern:
            expose: true
            access_type: public_method
            serialized_name: chainPattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getChainPattern
                setter: setChainPattern
            type: array<string>
            xml_list:
                inline: false
                entry_name: chainPatternValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        diamondClarity:
            expose: true
            access_type: public_method
            serialized_name: diamondClarity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDiamondClarity
                setter: setDiamondClarity
            type: string
        diamondCut:
            expose: true
            access_type: public_method
            serialized_name: diamondCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDiamondCut
                setter: setDiamondCut
            type: string
        carats:
            expose: true
            access_type: public_method
            serialized_name: carats
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCarats
                setter: setCarats
            type: WalmartDSV\JewelryType\CaratsAType
        totalDiamondWeight:
            expose: true
            access_type: public_method
            serialized_name: totalDiamondWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTotalDiamondWeight
                setter: setTotalDiamondWeight
            type: WalmartDSV\JewelryType\TotalDiamondWeightAType
        totalGemWeight:
            expose: true
            access_type: public_method
            serialized_name: totalGemWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTotalGemWeight
                setter: setTotalGemWeight
            type: float
        gemstoneCut:
            expose: true
            access_type: public_method
            serialized_name: gemstoneCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGemstoneCut
                setter: setGemstoneCut
            type: string
        gemstone:
            expose: true
            access_type: public_method
            serialized_name: gemstone
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGemstone
                setter: setGemstone
            type: WalmartDSV\GemstoneType
        gemstoneColor:
            expose: true
            access_type: public_method
            serialized_name: gemstoneColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGemstoneColor
                setter: setGemstoneColor
            type: string
        gemstoneClarity:
            expose: true
            access_type: public_method
            serialized_name: gemstoneClarity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGemstoneClarity
                setter: setGemstoneClarity
            type: string
        stoneCreationMethod:
            expose: true
            access_type: public_method
            serialized_name: stoneCreationMethod
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneCreationMethod
                setter: setStoneCreationMethod
            type: string
        stoneTreatment:
            expose: true
            access_type: public_method
            serialized_name: stoneTreatment
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneTreatment
                setter: setStoneTreatment
            type: string
        stoneHeight:
            expose: true
            access_type: public_method
            serialized_name: stoneHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneHeight
                setter: setStoneHeight
            type: WalmartDSV\JewelryType\StoneHeightAType
        stoneLength:
            expose: true
            access_type: public_method
            serialized_name: stoneLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneLength
                setter: setStoneLength
            type: WalmartDSV\JewelryType\StoneLengthAType
        stoneWidth:
            expose: true
            access_type: public_method
            serialized_name: stoneWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneWidth
                setter: setStoneWidth
            type: WalmartDSV\JewelryType\StoneWidthAType
        stoneDepthPercentage:
            expose: true
            access_type: public_method
            serialized_name: stoneDepthPercentage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneDepthPercentage
                setter: setStoneDepthPercentage
            type: float
        stoneTablePercentage:
            expose: true
            access_type: public_method
            serialized_name: stoneTablePercentage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneTablePercentage
                setter: setStoneTablePercentage
            type: float
        stoneSymmetry:
            expose: true
            access_type: public_method
            serialized_name: stoneSymmetry
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneSymmetry
                setter: setStoneSymmetry
            type: string
        stonePolish:
            expose: true
            access_type: public_method
            serialized_name: stonePolish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStonePolish
                setter: setStonePolish
            type: string
        stoneGirdle:
            expose: true
            access_type: public_method
            serialized_name: stoneGirdle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneGirdle
                setter: setStoneGirdle
            type: string
        stoneCulet:
            expose: true
            access_type: public_method
            serialized_name: stoneCulet
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneCulet
                setter: setStoneCulet
            type: string
        stoneFluoresence:
            expose: true
            access_type: public_method
            serialized_name: stoneFluoresence
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStoneFluoresence
                setter: setStoneFluoresence
            type: string
        pearlType:
            expose: true
            access_type: public_method
            serialized_name: pearlType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlType
                setter: setPearlType
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlBodyColor:
            expose: true
            access_type: public_method
            serialized_name: pearlBodyColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlBodyColor
                setter: setPearlBodyColor
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlBodyColorValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlLustre:
            expose: true
            access_type: public_method
            serialized_name: pearlLustre
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlLustre
                setter: setPearlLustre
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlLustreValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlShape:
            expose: true
            access_type: public_method
            serialized_name: pearlShape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlShape
                setter: setPearlShape
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlShapeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlUniformity:
            expose: true
            access_type: public_method
            serialized_name: pearlUniformity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlUniformity
                setter: setPearlUniformity
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlUniformityValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlSurfaceBlemishes:
            expose: true
            access_type: public_method
            serialized_name: pearlSurfaceBlemishes
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlSurfaceBlemishes
                setter: setPearlSurfaceBlemishes
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlSurfaceBlemishesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pearlNacreThickness:
            expose: true
            access_type: public_method
            serialized_name: pearlNacreThickness
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlNacreThickness
                setter: setPearlNacreThickness
            type: WalmartDSV\JewelryType\PearlNacreThicknessAType
        pearlStringingMethod:
            expose: true
            access_type: public_method
            serialized_name: pearlStringingMethod
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPearlStringingMethod
                setter: setPearlStringingMethod
            type: array<string>
            xml_list:
                inline: false
                entry_name: pearlStringingMethodValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        sizePerPearl:
            expose: true
            access_type: public_method
            serialized_name: sizePerPearl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSizePerPearl
                setter: setSizePerPearl
            type: WalmartDSV\JewelryType\SizePerPearlAType
        numberOfPearls:
            expose: true
            access_type: public_method
            serialized_name: numberOfPearls
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfPearls
                setter: setNumberOfPearls
            type: int
        inscription:
            expose: true
            access_type: public_method
            serialized_name: inscription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInscription
                setter: setInscription
            type: string
        isResizable:
            expose: true
            access_type: public_method
            serialized_name: isResizable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsResizable
                setter: setIsResizable
            type: string
        ringSizingLowerRange:
            expose: true
            access_type: public_method
            serialized_name: ringSizingLowerRange
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRingSizingLowerRange
                setter: setRingSizingLowerRange
            type: float
        ringSizingUpperRange:
            expose: true
            access_type: public_method
            serialized_name: ringSizingUpperRange
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRingSizingUpperRange
                setter: setRingSizingUpperRange
            type: float
        ringStyle:
            expose: true
            access_type: public_method
            serialized_name: ringStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRingStyle
                setter: setRingStyle
            type: array<string>
            xml_list:
                inline: false
                entry_name: ringStyleValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        ringSize:
            expose: true
            access_type: public_method
            serialized_name: ringSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRingSize
                setter: setRingSize
            type: float
        circumference:
            expose: true
            access_type: public_method
            serialized_name: circumference
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCircumference
                setter: setCircumference
            type: WalmartDSV\JewelryType\CircumferenceAType
        diameter:
            expose: true
            access_type: public_method
            serialized_name: diameter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDiameter
                setter: setDiameter
            type: WalmartDSV\JewelryType\DiameterAType
        ringSizeStandard:
            expose: true
            access_type: public_method
            serialized_name: ringSizeStandard
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRingSizeStandard
                setter: setRingSizeStandard
            type: string
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        sportsTeam:
            expose: true
            access_type: public_method
            serialized_name: sportsTeam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsTeam
                setter: setSportsTeam
            type: WalmartDSV\SportsTeamType
        theme:
            expose: true
            access_type: public_method
            serialized_name: theme
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTheme
                setter: setTheme
            type: WalmartDSV\ThemeType
        americanWireGuage:
            expose: true
            access_type: public_method
            serialized_name: americanWireGuage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAmericanWireGuage
                setter: setAmericanWireGuage
            type: int
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        isMadeFromRecycledMaterial:
            expose: true
            access_type: public_method
            serialized_name: isMadeFromRecycledMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMadeFromRecycledMaterial
                setter: setIsMadeFromRecycledMaterial
            type: string
        recycledMaterialContent:
            expose: true
            access_type: public_method
            serialized_name: recycledMaterialContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecycledMaterialContent
                setter: setRecycledMaterialContent
            type: array<WalmartDSV\RecycledMaterialContentValueType>
            xml_list:
                inline: false
                entry_name: recycledMaterialContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        bodyParts:
            expose: true
            access_type: public_method
            serialized_name: bodyParts
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBodyParts
                setter: setBodyParts
            type: array<string>
            xml_list:
                inline: false
                entry_name: bodyPart
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        designer:
            expose: true
            access_type: public_method
            serialized_name: designer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDesigner
                setter: setDesigner
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
