WalmartDSV\ComputersType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        screenSize:
            expose: true
            access_type: public_method
            serialized_name: screenSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getScreenSize
                setter: setScreenSize
            type: WalmartDSV\ComputersType\ScreenSizeAType
        resolution:
            expose: true
            access_type: public_method
            serialized_name: resolution
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getResolution
                setter: setResolution
            type: string
        displayTechnology:
            expose: true
            access_type: public_method
            serialized_name: displayTechnology
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDisplayTechnology
                setter: setDisplayTechnology
            type: string
        hardDriveCapacity:
            expose: true
            access_type: public_method
            serialized_name: hardDriveCapacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHardDriveCapacity
                setter: setHardDriveCapacity
            type: WalmartDSV\ComputersType\HardDriveCapacityAType
        ramMemory:
            expose: true
            access_type: public_method
            serialized_name: ramMemory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRamMemory
                setter: setRamMemory
            type: WalmartDSV\ComputersType\RamMemoryAType
        maximumRamSupported:
            expose: true
            access_type: public_method
            serialized_name: maximumRamSupported
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumRamSupported
                setter: setMaximumRamSupported
            type: WalmartDSV\ComputersType\MaximumRamSupportedAType
        internalExternal:
            expose: true
            access_type: public_method
            serialized_name: internalExternal
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInternalExternal
                setter: setInternalExternal
            type: string
        processorSpeed:
            expose: true
            access_type: public_method
            serialized_name: processorSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProcessorSpeed
                setter: setProcessorSpeed
            type: WalmartDSV\ComputersType\ProcessorSpeedAType
        processorType:
            expose: true
            access_type: public_method
            serialized_name: processorType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProcessorType
                setter: setProcessorType
            type: array<string>
            xml_list:
                inline: false
                entry_name: processorTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        computerStyle:
            expose: true
            access_type: public_method
            serialized_name: computerStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getComputerStyle
                setter: setComputerStyle
            type: string
        frontFacingCameraMegapixels:
            expose: true
            access_type: public_method
            serialized_name: frontFacingCameraMegapixels
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFrontFacingCameraMegapixels
                setter: setFrontFacingCameraMegapixels
            type: WalmartDSV\ComputersType\FrontFacingCameraMegapixelsAType
        rearCameraMegapixels:
            expose: true
            access_type: public_method
            serialized_name: rearCameraMegapixels
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRearCameraMegapixels
                setter: setRearCameraMegapixels
            type: WalmartDSV\ComputersType\RearCameraMegapixelsAType
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        operatingSystem:
            expose: true
            access_type: public_method
            serialized_name: operatingSystem
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOperatingSystem
                setter: setOperatingSystem
            type: array<string>
            xml_list:
                inline: false
                entry_name: operatingSystemValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        rAMSpeed:
            expose: true
            access_type: public_method
            serialized_name: RAMSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRAMSpeed
                setter: setRAMSpeed
            type: WalmartDSV\DataRateUnitType
        hasTouchscreen:
            expose: true
            access_type: public_method
            serialized_name: hasTouchscreen
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasTouchscreen
                setter: setHasTouchscreen
            type: string
        connections:
            expose: true
            access_type: public_method
            serialized_name: connections
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getConnections
                setter: setConnections
            type: array<string>
            xml_list:
                inline: false
                entry_name: connection
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        memoryCardType:
            expose: true
            access_type: public_method
            serialized_name: memoryCardType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMemoryCardType
                setter: setMemoryCardType
            type: array<string>
            xml_list:
                inline: false
                entry_name: memoryCardTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        opticalDrive:
            expose: true
            access_type: public_method
            serialized_name: opticalDrive
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOpticalDrive
                setter: setOpticalDrive
            type: string
        graphicsInformation:
            expose: true
            access_type: public_method
            serialized_name: graphicsInformation
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGraphicsInformation
                setter: setGraphicsInformation
            type: string
        formFactor:
            expose: true
            access_type: public_method
            serialized_name: formFactor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFormFactor
                setter: setFormFactor
            type: string
        hasSignalBooster:
            expose: true
            access_type: public_method
            serialized_name: hasSignalBooster
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasSignalBooster
                setter: setHasSignalBooster
            type: string
        wirelessTechnologies:
            expose: true
            access_type: public_method
            serialized_name: wirelessTechnologies
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWirelessTechnologies
                setter: setWirelessTechnologies
            type: array<string>
            xml_list:
                inline: false
                entry_name: wirelessTechnology
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        batteryLife:
            expose: true
            access_type: public_method
            serialized_name: batteryLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryLife
                setter: setBatteryLife
            type: WalmartDSV\ComputersType\BatteryLifeAType
        numberOfKeys:
            expose: true
            access_type: public_method
            serialized_name: numberOfKeys
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfKeys
                setter: setNumberOfKeys
            type: float
        dataIntegrityCheck:
            expose: true
            access_type: public_method
            serialized_name: dataIntegrityCheck
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDataIntegrityCheck
                setter: setDataIntegrityCheck
            type: string
        isPortable:
            expose: true
            access_type: public_method
            serialized_name: isPortable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPortable
                setter: setIsPortable
            type: string
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        numberOfChannels:
            expose: true
            access_type: public_method
            serialized_name: numberOfChannels
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfChannels
                setter: setNumberOfChannels
            type: string
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        configuration:
            expose: true
            access_type: public_method
            serialized_name: configuration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getConfiguration
                setter: setConfiguration
            type: string
        rAIDlevel:
            expose: true
            access_type: public_method
            serialized_name: RAIDlevel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRAIDlevel
                setter: setRAIDlevel
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
