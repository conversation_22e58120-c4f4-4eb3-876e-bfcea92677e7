WalmartDSV\WatercraftType:
    properties:
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        vehicleType:
            expose: true
            access_type: public_method
            serialized_name: vehicleType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVehicleType
                setter: setVehicleType
            type: string
        vehicleYear:
            expose: true
            access_type: public_method
            serialized_name: vehicleYear
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVehicleYear
                setter: setVehicleYear
            type: int
        vehicleMake:
            expose: true
            access_type: public_method
            serialized_name: vehicleMake
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVehicleMake
                setter: setVehicleMake
            type: string
        vehicleModel:
            expose: true
            access_type: public_method
            serialized_name: vehicleModel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVehicleModel
                setter: setVehicleModel
            type: string
        submodel:
            expose: true
            access_type: public_method
            serialized_name: submodel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSubmodel
                setter: setSubmodel
            type: string
        engineLocation:
            expose: true
            access_type: public_method
            serialized_name: engineLocation
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEngineLocation
                setter: setEngineLocation
            type: string
        engineModel:
            expose: true
            access_type: public_method
            serialized_name: engineModel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEngineModel
                setter: setEngineModel
            type: string
        engineDisplacement:
            expose: true
            access_type: public_method
            serialized_name: engineDisplacement
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEngineDisplacement
                setter: setEngineDisplacement
            type: WalmartDSV\WatercraftType\EngineDisplacementAType
        boreStroke:
            expose: true
            access_type: public_method
            serialized_name: boreStroke
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBoreStroke
                setter: setBoreStroke
            type: string
        inductionSystem:
            expose: true
            access_type: public_method
            serialized_name: inductionSystem
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInductionSystem
                setter: setInductionSystem
            type: string
        compressionRatio:
            expose: true
            access_type: public_method
            serialized_name: compressionRatio
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompressionRatio
                setter: setCompressionRatio
            type: string
        maximumEnginePower:
            expose: true
            access_type: public_method
            serialized_name: maximumEnginePower
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumEnginePower
                setter: setMaximumEnginePower
            type: WalmartDSV\PowerUnitType
        propulsionSystem:
            expose: true
            access_type: public_method
            serialized_name: propulsionSystem
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPropulsionSystem
                setter: setPropulsionSystem
            type: string
        coolingSystem:
            expose: true
            access_type: public_method
            serialized_name: coolingSystem
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCoolingSystem
                setter: setCoolingSystem
            type: string
        thrust:
            expose: true
            access_type: public_method
            serialized_name: thrust
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getThrust
                setter: setThrust
            type: WalmartDSV\WatercraftType\ThrustAType
        impellerPropeller:
            expose: true
            access_type: public_method
            serialized_name: impellerPropeller
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getImpellerPropeller
                setter: setImpellerPropeller
            type: string
        topSpeed:
            expose: true
            access_type: public_method
            serialized_name: topSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTopSpeed
                setter: setTopSpeed
            type: WalmartDSV\SpeedUnitType
        fuelRequirement:
            expose: true
            access_type: public_method
            serialized_name: fuelRequirement
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFuelRequirement
                setter: setFuelRequirement
            type: string
        fuelSystem:
            expose: true
            access_type: public_method
            serialized_name: fuelSystem
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFuelSystem
                setter: setFuelSystem
            type: string
        fuelCapacity:
            expose: true
            access_type: public_method
            serialized_name: fuelCapacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFuelCapacity
                setter: setFuelCapacity
            type: WalmartDSV\WatercraftType\FuelCapacityAType
        averageFuelConsumption:
            expose: true
            access_type: public_method
            serialized_name: averageFuelConsumption
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAverageFuelConsumption
                setter: setAverageFuelConsumption
            type: WalmartDSV\WatercraftType\AverageFuelConsumptionAType
        hullLength:
            expose: true
            access_type: public_method
            serialized_name: hullLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHullLength
                setter: setHullLength
            type: WalmartDSV\WatercraftType\HullLengthAType
        beam:
            expose: true
            access_type: public_method
            serialized_name: beam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBeam
                setter: setBeam
            type: WalmartDSV\WatercraftType\BeamAType
        airDraft:
            expose: true
            access_type: public_method
            serialized_name: airDraft
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAirDraft
                setter: setAirDraft
            type: WalmartDSV\WatercraftType\AirDraftAType
        draft:
            expose: true
            access_type: public_method
            serialized_name: draft
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDraft
                setter: setDraft
            type: WalmartDSV\WatercraftType\DraftAType
        dryWeight:
            expose: true
            access_type: public_method
            serialized_name: dryWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDryWeight
                setter: setDryWeight
            type: WalmartDSV\WatercraftType\DryWeightAType
        waterCapacity:
            expose: true
            access_type: public_method
            serialized_name: waterCapacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWaterCapacity
                setter: setWaterCapacity
            type: WalmartDSV\WatercraftType\WaterCapacityAType
        seatingCapacity:
            expose: true
            access_type: public_method
            serialized_name: seatingCapacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeatingCapacity
                setter: setSeatingCapacity
            type: int
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\WatercraftType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\WatercraftType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\WatercraftType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\WatercraftType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\WatercraftType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
