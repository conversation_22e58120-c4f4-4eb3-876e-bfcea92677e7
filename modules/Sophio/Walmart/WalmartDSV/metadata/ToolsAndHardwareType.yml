WalmartDSV\ToolsAndHardwareType:
    properties:
        buildingSupply:
            expose: true
            access_type: public_method
            serialized_name: BuildingSupply
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBuildingSupply
                setter: setBuildingSupply
            type: WalmartDSV\BuildingSupplyType
        electrical:
            expose: true
            access_type: public_method
            serialized_name: Electrical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getElectrical
                setter: setElectrical
            type: WalmartDSV\ElectricalType
        hardware:
            expose: true
            access_type: public_method
            serialized_name: Hardware
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHardware
                setter: setHardware
            type: WalmartDSV\HardwareType
        plumbingAndHVAC:
            expose: true
            access_type: public_method
            serialized_name: PlumbingAndHVAC
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPlumbingAndHVAC
                setter: setPlumbingAndHVAC
            type: WalmartDSV\PlumbingAndHVACType
        tools:
            expose: true
            access_type: public_method
            serialized_name: Tools
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTools
                setter: setTools
            type: WalmartDSV\ToolsType
        toolsAndHardwareOther:
            expose: true
            access_type: public_method
            serialized_name: ToolsAndHardwareOther
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getToolsAndHardwareOther
                setter: setToolsAndHardwareOther
            type: WalmartDSV\ToolsAndHardwareOtherType
