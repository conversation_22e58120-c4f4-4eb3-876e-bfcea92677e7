WalmartDSV\BabyType:
    properties:
        babyFood:
            expose: true
            access_type: public_method
            serialized_name: BabyFood
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBabyFood
                setter: setBabyFood
            type: WalmartDSV\BabyFoodType
        babyOther:
            expose: true
            access_type: public_method
            serialized_name: BabyOther
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBabyOther
                setter: setBabyOther
            type: WalmartDSV\BabyOtherType
        childCarSeats:
            expose: true
            access_type: public_method
            serialized_name: ChildCarSeats
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getChildCarSeats
                setter: setChildCarSeats
            type: WalmartDSV\ChildCarSeatsType
        babyFurniture:
            expose: true
            access_type: public_method
            serialized_name: BabyFurniture
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBabyFurniture
                setter: setBabyFurniture
            type: WalmartDSV\BabyFurnitureType
        babyToys:
            expose: true
            access_type: public_method
            serialized_name: BabyToys
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBabyToys
                setter: setBabyToys
            type: WalmartDSV\BabyToysType
        babyClothing:
            expose: true
            access_type: public_method
            serialized_name: BabyClothing
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBabyClothing
                setter: setBabyClothing
            type: WalmartDSV\BabyClothingType
