WalmartDSV\CleaningAndChemicalType\VariantAttributeNamesAType:
    properties:
        variantAttributeName:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeName
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeName
                setter: setVariantAttributeName
            xml_list:
                inline: true
                entry_name: variantAttributeName
                namespace: 'http://walmart.com/'
            type: array<string>
