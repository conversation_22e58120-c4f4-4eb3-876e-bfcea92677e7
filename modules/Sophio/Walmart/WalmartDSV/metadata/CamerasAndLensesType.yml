WalmartDSV\CamerasAndLensesType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        numberOfMegapixels:
            expose: true
            access_type: public_method
            serialized_name: numberOfMegapixels
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfMegapixels
                setter: setNumberOfMegapixels
            type: WalmartDSV\CamerasAndLensesType\NumberOfMegapixelsAType
        digitalZoom:
            expose: true
            access_type: public_method
            serialized_name: digitalZoom
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDigitalZoom
                setter: setDigitalZoom
            type: string
        opticalZoom:
            expose: true
            access_type: public_method
            serialized_name: opticalZoom
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOpticalZoom
                setter: setOpticalZoom
            type: string
        magnification:
            expose: true
            access_type: public_method
            serialized_name: magnification
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMagnification
                setter: setMagnification
            type: string
        sensorResolution:
            expose: true
            access_type: public_method
            serialized_name: sensorResolution
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSensorResolution
                setter: setSensorResolution
            type: WalmartDSV\ResolutionUnitType
        lensDiameter:
            expose: true
            access_type: public_method
            serialized_name: lensDiameter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLensDiameter
                setter: setLensDiameter
            type: WalmartDSV\CamerasAndLensesType\LensDiameterAType
        lensCoating:
            expose: true
            access_type: public_method
            serialized_name: lensCoating
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLensCoating
                setter: setLensCoating
            type: string
        lensFilterType:
            expose: true
            access_type: public_method
            serialized_name: lensFilterType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLensFilterType
                setter: setLensFilterType
            type: string
        cameraLensType:
            expose: true
            access_type: public_method
            serialized_name: cameraLensType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCameraLensType
                setter: setCameraLensType
            type: string
        hasFlash:
            expose: true
            access_type: public_method
            serialized_name: hasFlash
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasFlash
                setter: setHasFlash
            type: string
        flashType:
            expose: true
            access_type: public_method
            serialized_name: flashType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlashType
                setter: setFlashType
            type: string
        minimumShutterSpeed:
            expose: true
            access_type: public_method
            serialized_name: minimumShutterSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumShutterSpeed
                setter: setMinimumShutterSpeed
            type: WalmartDSV\CamerasAndLensesType\MinimumShutterSpeedAType
        maximumShutterSpeed:
            expose: true
            access_type: public_method
            serialized_name: maximumShutterSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumShutterSpeed
                setter: setMaximumShutterSpeed
            type: WalmartDSV\CamerasAndLensesType\MaximumShutterSpeedAType
        focusType:
            expose: true
            access_type: public_method
            serialized_name: focusType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFocusType
                setter: setFocusType
            type: array<string>
            xml_list:
                inline: false
                entry_name: focusTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        focalLength:
            expose: true
            access_type: public_method
            serialized_name: focalLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFocalLength
                setter: setFocalLength
            type: WalmartDSV\CamerasAndLensesType\FocalLengthAType
        focalRatio:
            expose: true
            access_type: public_method
            serialized_name: focalRatio
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFocalRatio
                setter: setFocalRatio
            type: string
        minimumAperture:
            expose: true
            access_type: public_method
            serialized_name: minimumAperture
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumAperture
                setter: setMinimumAperture
            type: string
        maximumAperture:
            expose: true
            access_type: public_method
            serialized_name: maximumAperture
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumAperture
                setter: setMaximumAperture
            type: string
        exposureModes:
            expose: true
            access_type: public_method
            serialized_name: exposureModes
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getExposureModes
                setter: setExposureModes
            type: array<string>
            xml_list:
                inline: false
                entry_name: exposureMode
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        displayTechnology:
            expose: true
            access_type: public_method
            serialized_name: displayTechnology
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDisplayTechnology
                setter: setDisplayTechnology
            type: string
        displayResolution:
            expose: true
            access_type: public_method
            serialized_name: displayResolution
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDisplayResolution
                setter: setDisplayResolution
            type: WalmartDSV\DisplayResolutionType
        screenSize:
            expose: true
            access_type: public_method
            serialized_name: screenSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getScreenSize
                setter: setScreenSize
            type: WalmartDSV\CamerasAndLensesType\ScreenSizeAType
        diameter:
            expose: true
            access_type: public_method
            serialized_name: diameter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDiameter
                setter: setDiameter
            type: WalmartDSV\CamerasAndLensesType\DiameterAType
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\CamerasAndLensesType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\CamerasAndLensesType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\CamerasAndLensesType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\CamerasAndLensesType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        isAssemblyRequired:
            expose: true
            access_type: public_method
            serialized_name: isAssemblyRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAssemblyRequired
                setter: setIsAssemblyRequired
            type: string
        assemblyInstructions:
            expose: true
            access_type: public_method
            serialized_name: assemblyInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssemblyInstructions
                setter: setAssemblyInstructions
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        accessoriesIncluded:
            expose: true
            access_type: public_method
            serialized_name: accessoriesIncluded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAccessoriesIncluded
                setter: setAccessoriesIncluded
            type: array<string>
            xml_list:
                inline: false
                entry_name: accessoriesIncludedValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hasTouchscreen:
            expose: true
            access_type: public_method
            serialized_name: hasTouchscreen
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasTouchscreen
                setter: setHasTouchscreen
            type: string
        hasMemoryCardSlot:
            expose: true
            access_type: public_method
            serialized_name: hasMemoryCardSlot
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasMemoryCardSlot
                setter: setHasMemoryCardSlot
            type: string
        memoryCardType:
            expose: true
            access_type: public_method
            serialized_name: memoryCardType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMemoryCardType
                setter: setMemoryCardType
            type: array<string>
            xml_list:
                inline: false
                entry_name: memoryCardTypeValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recordableMediaFormats:
            expose: true
            access_type: public_method
            serialized_name: recordableMediaFormats
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecordableMediaFormats
                setter: setRecordableMediaFormats
            type: array<string>
            xml_list:
                inline: false
                entry_name: recordableMediaFormat
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        wirelessTechnologies:
            expose: true
            access_type: public_method
            serialized_name: wirelessTechnologies
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWirelessTechnologies
                setter: setWirelessTechnologies
            type: array<string>
            xml_list:
                inline: false
                entry_name: wirelessTechnology
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        connections:
            expose: true
            access_type: public_method
            serialized_name: connections
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getConnections
                setter: setConnections
            type: array<string>
            xml_list:
                inline: false
                entry_name: connection
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPortable:
            expose: true
            access_type: public_method
            serialized_name: isPortable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPortable
                setter: setIsPortable
            type: string
        isWaterproof:
            expose: true
            access_type: public_method
            serialized_name: isWaterproof
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsWaterproof
                setter: setIsWaterproof
            type: string
        hasSelfTimer:
            expose: true
            access_type: public_method
            serialized_name: hasSelfTimer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasSelfTimer
                setter: setHasSelfTimer
            type: string
        selfTimerDelay:
            expose: true
            access_type: public_method
            serialized_name: selfTimerDelay
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSelfTimerDelay
                setter: setSelfTimerDelay
            type: array<WalmartDSV\TimeUnitType>
            xml_list:
                inline: false
                entry_name: selfTimerDelayValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        standbyTime:
            expose: true
            access_type: public_method
            serialized_name: standbyTime
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStandbyTime
                setter: setStandbyTime
            type: WalmartDSV\CamerasAndLensesType\StandbyTimeAType
        fieldOfView:
            expose: true
            access_type: public_method
            serialized_name: fieldOfView
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFieldOfView
                setter: setFieldOfView
            type: string
        isParfocal:
            expose: true
            access_type: public_method
            serialized_name: isParfocal
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsParfocal
                setter: setIsParfocal
            type: string
        shootingMode:
            expose: true
            access_type: public_method
            serialized_name: shootingMode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShootingMode
                setter: setShootingMode
            type: string
        microphoneIncluded:
            expose: true
            access_type: public_method
            serialized_name: microphoneIncluded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMicrophoneIncluded
                setter: setMicrophoneIncluded
            type: string
        hasHandle:
            expose: true
            access_type: public_method
            serialized_name: hasHandle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasHandle
                setter: setHasHandle
            type: string
        isMulticoated:
            expose: true
            access_type: public_method
            serialized_name: isMulticoated
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMulticoated
                setter: setIsMulticoated
            type: string
        hasRedEyeReduction:
            expose: true
            access_type: public_method
            serialized_name: hasRedEyeReduction
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasRedEyeReduction
                setter: setHasRedEyeReduction
            type: string
        hasNightVision:
            expose: true
            access_type: public_method
            serialized_name: hasNightVision
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasNightVision
                setter: setHasNightVision
            type: string
        isFogResistant:
            expose: true
            access_type: public_method
            serialized_name: isFogResistant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsFogResistant
                setter: setIsFogResistant
            type: string
        attachmentStyle:
            expose: true
            access_type: public_method
            serialized_name: attachmentStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAttachmentStyle
                setter: setAttachmentStyle
            type: string
        hasShoulderStrap:
            expose: true
            access_type: public_method
            serialized_name: hasShoulderStrap
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasShoulderStrap
                setter: setHasShoulderStrap
            type: string
        compatibleBrands:
            expose: true
            access_type: public_method
            serialized_name: compatibleBrands
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompatibleBrands
                setter: setCompatibleBrands
            type: array<string>
            xml_list:
                inline: false
                entry_name: compatibleBrand
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        compatibleDevices:
            expose: true
            access_type: public_method
            serialized_name: compatibleDevices
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompatibleDevices
                setter: setCompatibleDevices
            type: WalmartDSV\CompatibleDevicesType
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        cleaningCareAndMaintenance:
            expose: true
            access_type: public_method
            serialized_name: cleaningCareAndMaintenance
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCleaningCareAndMaintenance
                setter: setCleaningCareAndMaintenance
            type: string
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
