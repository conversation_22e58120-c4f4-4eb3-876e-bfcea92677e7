WalmartDSV\BabyFurnitureType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        gender:
            expose: true
            access_type: public_method
            serialized_name: gender
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGender
                setter: setGender
            type: string
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        ageRange:
            expose: true
            access_type: public_method
            serialized_name: ageRange
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeRange
                setter: setAgeRange
            type: WalmartDSV\AgeRangeType
        minimumWeight:
            expose: true
            access_type: public_method
            serialized_name: minimumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumWeight
                setter: setMinimumWeight
            type: WalmartDSV\BabyFurnitureType\MinimumWeightAType
        maximumWeight:
            expose: true
            access_type: public_method
            serialized_name: maximumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumWeight
                setter: setMaximumWeight
            type: WalmartDSV\BabyFurnitureType\MaximumWeightAType
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        bedSize:
            expose: true
            access_type: public_method
            serialized_name: bedSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBedSize
                setter: setBedSize
            type: string
        mattressFirmness:
            expose: true
            access_type: public_method
            serialized_name: mattressFirmness
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMattressFirmness
                setter: setMattressFirmness
            type: string
        fillMaterial:
            expose: true
            access_type: public_method
            serialized_name: fillMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFillMaterial
                setter: setFillMaterial
            type: array<string>
            xml_list:
                inline: false
                entry_name: fillMaterialValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        finish:
            expose: true
            access_type: public_method
            serialized_name: finish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFinish
                setter: setFinish
            type: string
        shape:
            expose: true
            access_type: public_method
            serialized_name: shape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShape
                setter: setShape
            type: string
        isFoldable:
            expose: true
            access_type: public_method
            serialized_name: isFoldable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsFoldable
                setter: setIsFoldable
            type: string
        isWheeled:
            expose: true
            access_type: public_method
            serialized_name: isWheeled
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsWheeled
                setter: setIsWheeled
            type: string
        homeDecorStyle:
            expose: true
            access_type: public_method
            serialized_name: homeDecorStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHomeDecorStyle
                setter: setHomeDecorStyle
            type: string
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\BabyFurnitureType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\BabyFurnitureType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\BabyFurnitureType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\BabyFurnitureType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        requiresTextileActLabeling:
            expose: true
            access_type: public_method
            serialized_name: requiresTextileActLabeling
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRequiresTextileActLabeling
                setter: setRequiresTextileActLabeling
            type: string
        countryOfOriginTextiles:
            expose: true
            access_type: public_method
            serialized_name: countryOfOriginTextiles
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountryOfOriginTextiles
                setter: setCountryOfOriginTextiles
            type: string
        compositeWoodCertificationCode:
            expose: true
            access_type: public_method
            serialized_name: compositeWoodCertificationCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompositeWoodCertificationCode
                setter: setCompositeWoodCertificationCode
            type: int
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isAssemblyRequired:
            expose: true
            access_type: public_method
            serialized_name: isAssemblyRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAssemblyRequired
                setter: setIsAssemblyRequired
            type: string
        assemblyInstructions:
            expose: true
            access_type: public_method
            serialized_name: assemblyInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssemblyInstructions
                setter: setAssemblyInstructions
            type: string
        fabricContent:
            expose: true
            access_type: public_method
            serialized_name: fabricContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricContent
                setter: setFabricContent
            type: array<WalmartDSV\FabricContentValueType>
            xml_list:
                inline: false
                entry_name: fabricContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        fabricCareInstructions:
            expose: true
            access_type: public_method
            serialized_name: fabricCareInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricCareInstructions
                setter: setFabricCareInstructions
            type: array<string>
            xml_list:
                inline: false
                entry_name: fabricCareInstruction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        sportsTeam:
            expose: true
            access_type: public_method
            serialized_name: sportsTeam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsTeam
                setter: setSportsTeam
            type: WalmartDSV\SportsTeamType
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        collection:
            expose: true
            access_type: public_method
            serialized_name: collection
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCollection
                setter: setCollection
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\BabyFurnitureType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
