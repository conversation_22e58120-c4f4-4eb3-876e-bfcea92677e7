WalmartDSV\AnimalFoodType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        animalType:
            expose: true
            access_type: public_method
            serialized_name: animalType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAnimalType
                setter: setAnimalType
            type: string
        animalBreed:
            expose: true
            access_type: public_method
            serialized_name: animalBreed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAnimalBreed
                setter: setAnimalBreed
            type: string
        animalLifestage:
            expose: true
            access_type: public_method
            serialized_name: animalLifestage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAnimalLifestage
                setter: setAnimalLifestage
            type: string
        minimumWeight:
            expose: true
            access_type: public_method
            serialized_name: minimumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumWeight
                setter: setMinimumWeight
            type: WalmartDSV\AnimalFoodType\MinimumWeightAType
        maximumWeight:
            expose: true
            access_type: public_method
            serialized_name: maximumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumWeight
                setter: setMaximumWeight
            type: WalmartDSV\AnimalFoodType\MaximumWeightAType
        petSize:
            expose: true
            access_type: public_method
            serialized_name: petSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPetSize
                setter: setPetSize
            type: string
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        petFoodForm:
            expose: true
            access_type: public_method
            serialized_name: petFoodForm
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPetFoodForm
                setter: setPetFoodForm
            type: string
        flavor:
            expose: true
            access_type: public_method
            serialized_name: flavor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlavor
                setter: setFlavor
            type: string
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\AnimalFoodType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\AnimalFoodType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\AnimalFoodType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\AnimalFoodType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        hasExpiration:
            expose: true
            access_type: public_method
            serialized_name: hasExpiration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasExpiration
                setter: setHasExpiration
            type: string
        shelfLife:
            expose: true
            access_type: public_method
            serialized_name: shelfLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShelfLife
                setter: setShelfLife
            type: WalmartDSV\AnimalFoodType\ShelfLifeAType
        hasPricePerUnit:
            expose: true
            access_type: public_method
            serialized_name: hasPricePerUnit
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasPricePerUnit
                setter: setHasPricePerUnit
            type: string
        pricePerUnitQuantity:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitQuantity
                setter: setPricePerUnitQuantity
            type: float
        pricePerUnitUom:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitUom
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitUom
                setter: setPricePerUnitUom
            type: string
        hasGMOs:
            expose: true
            access_type: public_method
            serialized_name: hasGMOs
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasGMOs
                setter: setHasGMOs
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        isTemperatureSensitive:
            expose: true
            access_type: public_method
            serialized_name: isTemperatureSensitive
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsTemperatureSensitive
                setter: setIsTemperatureSensitive
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isNutritionFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isNutritionFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsNutritionFactsLabelRequired
                setter: setIsNutritionFactsLabelRequired
            type: string
        nutritionFactsLabel:
            expose: true
            access_type: public_method
            serialized_name: nutritionFactsLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNutritionFactsLabel
                setter: setNutritionFactsLabel
            type: string
        nutritionIngredientsImage:
            expose: true
            access_type: public_method
            serialized_name: nutritionIngredientsImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNutritionIngredientsImage
                setter: setNutritionIngredientsImage
            type: string
        feedingInstructions:
            expose: true
            access_type: public_method
            serialized_name: feedingInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeedingInstructions
                setter: setFeedingInstructions
            type: string
        animalHealthConcern:
            expose: true
            access_type: public_method
            serialized_name: animalHealthConcern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAnimalHealthConcern
                setter: setAnimalHealthConcern
            type: array<string>
            xml_list:
                inline: false
                entry_name: animalHealthConcernValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        instructions:
            expose: true
            access_type: public_method
            serialized_name: instructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInstructions
                setter: setInstructions
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\AnimalFoodType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
