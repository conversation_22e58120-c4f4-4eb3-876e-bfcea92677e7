WalmartDSV\MedicineAndSupplementsType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        flavor:
            expose: true
            access_type: public_method
            serialized_name: flavor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlavor
                setter: setFlavor
            type: string
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        gender:
            expose: true
            access_type: public_method
            serialized_name: gender
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGender
                setter: setGender
            type: string
        bodyParts:
            expose: true
            access_type: public_method
            serialized_name: bodyParts
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBodyParts
                setter: setBodyParts
            type: array<string>
            xml_list:
                inline: false
                entry_name: bodyPart
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        healthConcerns:
            expose: true
            access_type: public_method
            serialized_name: healthConcerns
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHealthConcerns
                setter: setHealthConcerns
            type: array<string>
            xml_list:
                inline: false
                entry_name: healthConcern
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\MedicineAndSupplementsType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\MedicineAndSupplementsType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\MedicineAndSupplementsType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\MedicineAndSupplementsType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        isControlledSubstance:
            expose: true
            access_type: public_method
            serialized_name: isControlledSubstance
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsControlledSubstance
                setter: setIsControlledSubstance
            type: string
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        isPesticide:
            expose: true
            access_type: public_method
            serialized_name: isPesticide
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPesticide
                setter: setIsPesticide
            type: string
        hasPricePerUnit:
            expose: true
            access_type: public_method
            serialized_name: hasPricePerUnit
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasPricePerUnit
                setter: setHasPricePerUnit
            type: string
        pricePerUnitQuantity:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitQuantity
                setter: setPricePerUnitQuantity
            type: float
        pricePerUnitUom:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitUom
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitUom
                setter: setPricePerUnitUom
            type: string
        isDrugFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isDrugFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsDrugFactsLabelRequired
                setter: setIsDrugFactsLabelRequired
            type: string
        drugFactsLabel:
            expose: true
            access_type: public_method
            serialized_name: drugFactsLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDrugFactsLabel
                setter: setDrugFactsLabel
            type: string
        drugDosageInstructionsImage:
            expose: true
            access_type: public_method
            serialized_name: drugDosageInstructionsImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDrugDosageInstructionsImage
                setter: setDrugDosageInstructionsImage
            type: string
        drugActiveInactiveIngredientsImage:
            expose: true
            access_type: public_method
            serialized_name: drugActiveInactiveIngredientsImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDrugActiveInactiveIngredientsImage
                setter: setDrugActiveInactiveIngredientsImage
            type: string
        isSupplementFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isSupplementFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsSupplementFactsLabelRequired
                setter: setIsSupplementFactsLabelRequired
            type: string
        supplementFactsLabel:
            expose: true
            access_type: public_method
            serialized_name: supplementFactsLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSupplementFactsLabel
                setter: setSupplementFactsLabel
            type: string
        supplementDosageInstructionsImage:
            expose: true
            access_type: public_method
            serialized_name: supplementDosageInstructionsImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSupplementDosageInstructionsImage
                setter: setSupplementDosageInstructionsImage
            type: string
        supplementActiveInactiveIngredientsImage:
            expose: true
            access_type: public_method
            serialized_name: supplementActiveInactiveIngredientsImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSupplementActiveInactiveIngredientsImage
                setter: setSupplementActiveInactiveIngredientsImage
            type: string
        isNutritionFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isNutritionFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsNutritionFactsLabelRequired
                setter: setIsNutritionFactsLabelRequired
            type: string
        nutritionFactsLabel:
            expose: true
            access_type: public_method
            serialized_name: nutritionFactsLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNutritionFactsLabel
                setter: setNutritionFactsLabel
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hasIngredientList:
            expose: true
            access_type: public_method
            serialized_name: hasIngredientList
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasIngredientList
                setter: setHasIngredientList
            type: string
        ingredientListImage:
            expose: true
            access_type: public_method
            serialized_name: ingredientListImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredientListImage
                setter: setIngredientListImage
            type: string
        ingredients:
            expose: true
            access_type: public_method
            serialized_name: ingredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredients
                setter: setIngredients
            type: string
        hasGMOs:
            expose: true
            access_type: public_method
            serialized_name: hasGMOs
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasGMOs
                setter: setHasGMOs
            type: string
        isTemperatureSensitive:
            expose: true
            access_type: public_method
            serialized_name: isTemperatureSensitive
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsTemperatureSensitive
                setter: setIsTemperatureSensitive
            type: string
        hasExpiration:
            expose: true
            access_type: public_method
            serialized_name: hasExpiration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasExpiration
                setter: setHasExpiration
            type: string
        shelfLife:
            expose: true
            access_type: public_method
            serialized_name: shelfLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShelfLife
                setter: setShelfLife
            type: WalmartDSV\MedicineAndSupplementsType\ShelfLifeAType
        recommendedUses:
            expose: true
            access_type: public_method
            serialized_name: recommendedUses
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedUses
                setter: setRecommendedUses
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedUse
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        activeIngredients:
            expose: true
            access_type: public_method
            serialized_name: activeIngredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getActiveIngredients
                setter: setActiveIngredients
            type: array<WalmartDSV\ActiveIngredientType>
            xml_list:
                inline: false
                entry_name: activeIngredient
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        inactiveIngredients:
            expose: true
            access_type: public_method
            serialized_name: inactiveIngredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInactiveIngredients
                setter: setInactiveIngredients
            type: array<string>
            xml_list:
                inline: false
                entry_name: inactiveIngredient
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        form:
            expose: true
            access_type: public_method
            serialized_name: form
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getForm
                setter: setForm
            type: string
        instructions:
            expose: true
            access_type: public_method
            serialized_name: instructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInstructions
                setter: setInstructions
            type: string
        dosage:
            expose: true
            access_type: public_method
            serialized_name: dosage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDosage
                setter: setDosage
            type: string
        stopUseIndications:
            expose: true
            access_type: public_method
            serialized_name: stopUseIndications
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStopUseIndications
                setter: setStopUseIndications
            type: array<string>
            xml_list:
                inline: false
                entry_name: stopUseIndication
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        medicineStrength:
            expose: true
            access_type: public_method
            serialized_name: medicineStrength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMedicineStrength
                setter: setMedicineStrength
            type: string
        nationalDrugCode:
            expose: true
            access_type: public_method
            serialized_name: nationalDrugCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNationalDrugCode
                setter: setNationalDrugCode
            type: string
        nutrients:
            expose: true
            access_type: public_method
            serialized_name: nutrients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNutrients
                setter: setNutrients
            type: array<WalmartDSV\NutrientType>
            xml_list:
                inline: false
                entry_name: nutrient
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        servingSize:
            expose: true
            access_type: public_method
            serialized_name: servingSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getServingSize
                setter: setServingSize
            type: string
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\MedicineAndSupplementsType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
