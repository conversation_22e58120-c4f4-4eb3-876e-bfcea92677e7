WalmartDSV\WatchFeaturesType:
    properties:
        watchFeaturesValue:
            expose: true
            access_type: public_method
            serialized_name: watchFeaturesValue
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWatchFeaturesValue
                setter: setWatchFeaturesValue
            xml_list:
                inline: true
                entry_name: watchFeaturesValue
                namespace: 'http://walmart.com/'
            type: array<string>
