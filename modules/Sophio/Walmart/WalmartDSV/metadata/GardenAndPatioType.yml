WalmartDSV\GardenAndPatioType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        inflexKitComponent:
            expose: true
            access_type: public_method
            serialized_name: inflexKitComponent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInflexKitComponent
                setter: setInflexKitComponent
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        finish:
            expose: true
            access_type: public_method
            serialized_name: finish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFinish
                setter: setFinish
            type: string
        shape:
            expose: true
            access_type: public_method
            serialized_name: shape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShape
                setter: setShape
            type: string
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        theme:
            expose: true
            access_type: public_method
            serialized_name: theme
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTheme
                setter: setTheme
            type: WalmartDSV\ThemeType
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\GardenAndPatioType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\GardenAndPatioType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\GardenAndPatioType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\GardenAndPatioType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        requiresTextileActLabeling:
            expose: true
            access_type: public_method
            serialized_name: requiresTextileActLabeling
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRequiresTextileActLabeling
                setter: setRequiresTextileActLabeling
            type: string
        countryOfOriginTextiles:
            expose: true
            access_type: public_method
            serialized_name: countryOfOriginTextiles
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountryOfOriginTextiles
                setter: setCountryOfOriginTextiles
            type: string
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        compositeWoodCertificationCode:
            expose: true
            access_type: public_method
            serialized_name: compositeWoodCertificationCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompositeWoodCertificationCode
                setter: setCompositeWoodCertificationCode
            type: int
        isPesticide:
            expose: true
            access_type: public_method
            serialized_name: isPesticide
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPesticide
                setter: setIsPesticide
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isLightingFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isLightingFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsLightingFactsLabelRequired
                setter: setIsLightingFactsLabelRequired
            type: string
        hasFuelContainer:
            expose: true
            access_type: public_method
            serialized_name: hasFuelContainer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasFuelContainer
                setter: setHasFuelContainer
            type: string
        isAssemblyRequired:
            expose: true
            access_type: public_method
            serialized_name: isAssemblyRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAssemblyRequired
                setter: setIsAssemblyRequired
            type: string
        assemblyInstructions:
            expose: true
            access_type: public_method
            serialized_name: assemblyInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssemblyInstructions
                setter: setAssemblyInstructions
            type: string
        isBulk:
            expose: true
            access_type: public_method
            serialized_name: isBulk
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsBulk
                setter: setIsBulk
            type: string
        maximumWeight:
            expose: true
            access_type: public_method
            serialized_name: maximumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumWeight
                setter: setMaximumWeight
            type: WalmartDSV\GardenAndPatioType\MaximumWeightAType
        coverageArea:
            expose: true
            access_type: public_method
            serialized_name: coverageArea
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCoverageArea
                setter: setCoverageArea
            type: WalmartDSV\GardenAndPatioType\CoverageAreaAType
        occasion:
            expose: true
            access_type: public_method
            serialized_name: occasion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOccasion
                setter: setOccasion
            type: WalmartDSV\OccasionType
        season:
            expose: true
            access_type: public_method
            serialized_name: season
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeason
                setter: setSeason
            type: WalmartDSV\SeasonType
        recommendedUses:
            expose: true
            access_type: public_method
            serialized_name: recommendedUses
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedUses
                setter: setRecommendedUses
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedUse
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recommendedLocations:
            expose: true
            access_type: public_method
            serialized_name: recommendedLocations
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedLocations
                setter: setRecommendedLocations
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedLocation
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        fabricContent:
            expose: true
            access_type: public_method
            serialized_name: fabricContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricContent
                setter: setFabricContent
            type: array<WalmartDSV\FabricContentValueType>
            xml_list:
                inline: false
                entry_name: fabricContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        frameMaterial:
            expose: true
            access_type: public_method
            serialized_name: frameMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFrameMaterial
                setter: setFrameMaterial
            type: array<string>
            xml_list:
                inline: false
                entry_name: frameMaterialValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        baseMaterial:
            expose: true
            access_type: public_method
            serialized_name: baseMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBaseMaterial
                setter: setBaseMaterial
            type: string
        isAntique:
            expose: true
            access_type: public_method
            serialized_name: isAntique
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAntique
                setter: setIsAntique
            type: string
        isFoldable:
            expose: true
            access_type: public_method
            serialized_name: isFoldable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsFoldable
                setter: setIsFoldable
            type: string
        isWheeled:
            expose: true
            access_type: public_method
            serialized_name: isWheeled
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsWheeled
                setter: setIsWheeled
            type: string
        isWeatherResistant:
            expose: true
            access_type: public_method
            serialized_name: isWeatherResistant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsWeatherResistant
                setter: setIsWeatherResistant
            type: string
        isWaterproof:
            expose: true
            access_type: public_method
            serialized_name: isWaterproof
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsWaterproof
                setter: setIsWaterproof
            type: string
        isTearResistant:
            expose: true
            access_type: public_method
            serialized_name: isTearResistant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsTearResistant
                setter: setIsTearResistant
            type: string
        powerType:
            expose: true
            access_type: public_method
            serialized_name: powerType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPowerType
                setter: setPowerType
            type: string
        lightBulbType:
            expose: true
            access_type: public_method
            serialized_name: lightBulbType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLightBulbType
                setter: setLightBulbType
            type: string
        lightBulbColor:
            expose: true
            access_type: public_method
            serialized_name: lightBulbColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLightBulbColor
                setter: setLightBulbColor
            type: string
        volts:
            expose: true
            access_type: public_method
            serialized_name: volts
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVolts
                setter: setVolts
            type: WalmartDSV\GardenAndPatioType\VoltsAType
        watts:
            expose: true
            access_type: public_method
            serialized_name: watts
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWatts
                setter: setWatts
            type: WalmartDSV\GardenAndPatioType\WattsAType
        minimumTemperature:
            expose: true
            access_type: public_method
            serialized_name: minimumTemperature
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumTemperature
                setter: setMinimumTemperature
            type: WalmartDSV\GardenAndPatioType\MinimumTemperatureAType
        plantCategory:
            expose: true
            access_type: public_method
            serialized_name: plantCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPlantCategory
                setter: setPlantCategory
            type: string
        fuelType:
            expose: true
            access_type: public_method
            serialized_name: fuelType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFuelType
                setter: setFuelType
            type: string
        cuttingWidth:
            expose: true
            access_type: public_method
            serialized_name: cuttingWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCuttingWidth
                setter: setCuttingWidth
            type: WalmartDSV\LengthUnitType
        clearingWidth:
            expose: true
            access_type: public_method
            serialized_name: clearingWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClearingWidth
                setter: setClearingWidth
            type: WalmartDSV\GardenAndPatioType\ClearingWidthAType
        sprayPatterns:
            expose: true
            access_type: public_method
            serialized_name: sprayPatterns
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSprayPatterns
                setter: setSprayPatterns
            type: string
        diameter:
            expose: true
            access_type: public_method
            serialized_name: diameter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDiameter
                setter: setDiameter
            type: WalmartDSV\GardenAndPatioType\DiameterAType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        installationType:
            expose: true
            access_type: public_method
            serialized_name: installationType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInstallationType
                setter: setInstallationType
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        homeDecorStyle:
            expose: true
            access_type: public_method
            serialized_name: homeDecorStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHomeDecorStyle
                setter: setHomeDecorStyle
            type: string
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        isIndustrial:
            expose: true
            access_type: public_method
            serialized_name: isIndustrial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsIndustrial
                setter: setIsIndustrial
            type: string
        isEnergyStarCertified:
            expose: true
            access_type: public_method
            serialized_name: isEnergyStarCertified
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsEnergyStarCertified
                setter: setIsEnergyStarCertified
            type: string
        hasRadiantHeat:
            expose: true
            access_type: public_method
            serialized_name: hasRadiantHeat
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasRadiantHeat
                setter: setHasRadiantHeat
            type: string
        flowRate:
            expose: true
            access_type: public_method
            serialized_name: flowRate
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlowRate
                setter: setFlowRate
            type: WalmartDSV\GardenAndPatioType\FlowRateAType
        hasAutomaticShutoff:
            expose: true
            access_type: public_method
            serialized_name: hasAutomaticShutoff
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasAutomaticShutoff
                setter: setHasAutomaticShutoff
            type: string
        capacity:
            expose: true
            access_type: public_method
            serialized_name: capacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCapacity
                setter: setCapacity
            type: string
        productVolume:
            expose: true
            access_type: public_method
            serialized_name: productVolume
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductVolume
                setter: setProductVolume
            type: string
        cleaningCareAndMaintenance:
            expose: true
            access_type: public_method
            serialized_name: cleaningCareAndMaintenance
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCleaningCareAndMaintenance
                setter: setCleaningCareAndMaintenance
            type: string
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        flooringMaterial:
            expose: true
            access_type: public_method
            serialized_name: flooringMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlooringMaterial
                setter: setFlooringMaterial
            type: string
        minimumClearance:
            expose: true
            access_type: public_method
            serialized_name: minimumClearance
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumClearance
                setter: setMinimumClearance
            type: WalmartDSV\GardenAndPatioType\MinimumClearanceAType
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\GardenAndPatioType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
