WalmartDSV\SportAndRecreationType:
    properties:
        cycling:
            expose: true
            access_type: public_method
            serialized_name: Cycling
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCycling
                setter: setCycling
            type: WalmartDSV\CyclingType
        weapons:
            expose: true
            access_type: public_method
            serialized_name: Weapons
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWeapons
                setter: setWeapons
            type: WalmartDSV\WeaponsType
        optics:
            expose: true
            access_type: public_method
            serialized_name: Optics
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOptics
                setter: setOptics
            type: WalmartDSV\OpticsType
        sportAndRecreationOther:
            expose: true
            access_type: public_method
            serialized_name: SportAndRecreationOther
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportAndRecreationOther
                setter: setSportAndRecreationOther
            type: WalmartDSV\SportAndRecreationOtherType
