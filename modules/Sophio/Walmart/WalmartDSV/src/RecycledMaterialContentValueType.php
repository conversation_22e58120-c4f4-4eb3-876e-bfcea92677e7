<?php

namespace WalmartDSV;

/**
 * Class representing RecycledMaterialContentValueType
 *
 *
 * XSD Type: recycledMaterialContentValue
 */
class RecycledMaterialContentValueType
{

    /**
     * Type of recycled material used to create the item.
     *
     * @var string $recycledMaterial
     */
    private $recycledMaterial = null;

    /**
     * Corresponding percentage of the recycled material used to create the item. Used in conjunction with Recycled Material attribute.
     *
     * @var float $percentageOfRecycledMaterial
     */
    private $percentageOfRecycledMaterial = null;

    /**
     * Gets as recycledMaterial
     *
     * Type of recycled material used to create the item.
     *
     * @return string
     */
    public function getRecycledMaterial()
    {
        return $this->recycledMaterial;
    }

    /**
     * Sets a new recycledMaterial
     *
     * Type of recycled material used to create the item.
     *
     * @param string $recycledMaterial
     * @return self
     */
    public function setRecycledMaterial($recycledMaterial)
    {
        $this->recycledMaterial = $recycledMaterial;
        return $this;
    }

    /**
     * Gets as percentageOfRecycledMaterial
     *
     * Corresponding percentage of the recycled material used to create the item. Used in conjunction with Recycled Material attribute.
     *
     * @return float
     */
    public function getPercentageOfRecycledMaterial()
    {
        return $this->percentageOfRecycledMaterial;
    }

    /**
     * Sets a new percentageOfRecycledMaterial
     *
     * Corresponding percentage of the recycled material used to create the item. Used in conjunction with Recycled Material attribute.
     *
     * @param float $percentageOfRecycledMaterial
     * @return self
     */
    public function setPercentageOfRecycledMaterial($percentageOfRecycledMaterial)
    {
        $this->percentageOfRecycledMaterial = $percentageOfRecycledMaterial;
        return $this;
    }


}

