<?php

namespace WalmartDSV;

/**
 * Class representing MemoryCardFormatType
 *
 * The memory card format applicable to the product.
 * XSD Type: MemoryCardFormat
 */
class MemoryCardFormatType
{

    /**
     * @var string[] $memoryCardTypeValue
     */
    private $memoryCardTypeValue = [
        
    ];

    /**
     * Adds as memoryCardTypeValue
     *
     * @return self
     * @param string $memoryCardTypeValue
     */
    public function addToMemoryCardTypeValue($memoryCardTypeValue)
    {
        $this->memoryCardTypeValue[] = $memoryCardTypeValue;
        return $this;
    }

    /**
     * isset memoryCardTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetMemoryCardTypeValue($index)
    {
        return isset($this->memoryCardTypeValue[$index]);
    }

    /**
     * unset memoryCardTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetMemoryCardTypeValue($index)
    {
        unset($this->memoryCardTypeValue[$index]);
    }

    /**
     * Gets as memoryCardTypeValue
     *
     * @return string[]
     */
    public function getMemoryCardTypeValue()
    {
        return $this->memoryCardTypeValue;
    }

    /**
     * Sets a new memoryCardTypeValue
     *
     * @param string $memoryCardTypeValue
     * @return self
     */
    public function setMemoryCardTypeValue(array $memoryCardTypeValue)
    {
        $this->memoryCardTypeValue = $memoryCardTypeValue;
        return $this;
    }


}

