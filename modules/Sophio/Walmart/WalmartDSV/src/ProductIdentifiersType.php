<?php

namespace WalmartDSV;

/**
 * Class representing ProductIdentifiersType
 *
 * Specify at least one Product ID and its ID Type.
 * XSD Type: ProductIdentifiers
 */
class ProductIdentifiersType
{

    /**
     * @var \WalmartDSV\ProductIdentifierType[] $productIdentifier
     */
    private $productIdentifier = [
        
    ];

    /**
     * Adds as productIdentifier
     *
     * @param \WalmartDSV\ProductIdentifierType $productIdentifier
     *@return self
     */
    public function addToProductIdentifier(\WalmartDSV\ProductIdentifierType $productIdentifier)
    {
        $this->productIdentifier[] = $productIdentifier;
        return $this;
    }

    /**
     * isset productIdentifier
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductIdentifier($index)
    {
        return isset($this->productIdentifier[$index]);
    }

    /**
     * unset productIdentifier
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductIdentifier($index)
    {
        unset($this->productIdentifier[$index]);
    }

    /**
     * Gets as productIdentifier
     *
     * @return \WalmartDSV\ProductIdentifierType[]
     */
    public function getProductIdentifier()
    {
        return $this->productIdentifier;
    }

    /**
     * Sets a new productIdentifier
     *
     * @param \WalmartDSV\ProductIdentifierType[] $productIdentifier
     * @return self
     */
    public function setProductIdentifier(array $productIdentifier)
    {
        $this->productIdentifier = $productIdentifier;
        return $this;
    }


}

