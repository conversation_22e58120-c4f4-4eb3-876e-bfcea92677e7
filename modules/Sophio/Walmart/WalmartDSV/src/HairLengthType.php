<?php

namespace WalmartDSV;

/**
 * Class representing HairLengthType
 *
 * The length of hair that grooming product is intended for.
 * XSD Type: HairLength
 */
class HairLengthType
{

    /**
     * @var string[] $hairLengthValue
     */
    private $hairLengthValue = [
        
    ];

    /**
     * Adds as hairLengthValue
     *
     * @return self
     * @param string $hairLengthValue
     */
    public function addToHairLengthValue($hairLengthValue)
    {
        $this->hairLengthValue[] = $hairLengthValue;
        return $this;
    }

    /**
     * isset hairLengthValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetHairLengthValue($index)
    {
        return isset($this->hairLengthValue[$index]);
    }

    /**
     * unset hairLengthValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetHairLengthValue($index)
    {
        unset($this->hairLengthValue[$index]);
    }

    /**
     * Gets as hairLengthValue
     *
     * @return string[]
     */
    public function getHairLengthValue()
    {
        return $this->hairLengthValue;
    }

    /**
     * Sets a new hairLengthValue
     *
     * @param string $hairLengthValue
     * @return self
     */
    public function setHairLengthValue(array $hairLengthValue)
    {
        $this->hairLengthValue = $hairLengthValue;
        return $this;
    }


}

