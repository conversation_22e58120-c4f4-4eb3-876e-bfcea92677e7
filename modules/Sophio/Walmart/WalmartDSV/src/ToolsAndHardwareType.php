<?php

namespace WalmartDSV;

/**
 * Class representing ToolsAndHardwareType
 *
 *
 * XSD Type: ToolsAndHardware
 */
class ToolsAndHardwareType
{

    /**
     * @var \WalmartDSV\BuildingSupplyType $buildingSupply
     */
    private $buildingSupply = null;

    /**
     * @var \WalmartDSV\ElectricalType $electrical
     */
    private $electrical = null;

    /**
     * @var \WalmartDSV\HardwareType $hardware
     */
    private $hardware = null;

    /**
     * @var \WalmartDSV\PlumbingAndHVACType $plumbingAndHVAC
     */
    private $plumbingAndHVAC = null;

    /**
     * @var \WalmartDSV\ToolsType $tools
     */
    private $tools = null;

    /**
     * @var \WalmartDSV\ToolsAndHardwareOtherType $toolsAndHardwareOther
     */
    private $toolsAndHardwareOther = null;

    /**
     * Gets as buildingSupply
     *
     * @return \WalmartDSV\BuildingSupplyType
     */
    public function getBuildingSupply()
    {
        return $this->buildingSupply;
    }

    /**
     * Sets a new buildingSupply
     *
     * @param \WalmartDSV\BuildingSupplyType $buildingSupply
     * @return self
     */
    public function setBuildingSupply(\WalmartDSV\BuildingSupplyType $buildingSupply)
    {
        $this->buildingSupply = $buildingSupply;
        return $this;
    }

    /**
     * Gets as electrical
     *
     * @return \WalmartDSV\ElectricalType
     */
    public function getElectrical()
    {
        return $this->electrical;
    }

    /**
     * Sets a new electrical
     *
     * @param \WalmartDSV\ElectricalType $electrical
     * @return self
     */
    public function setElectrical(\WalmartDSV\ElectricalType $electrical)
    {
        $this->electrical = $electrical;
        return $this;
    }

    /**
     * Gets as hardware
     *
     * @return \WalmartDSV\HardwareType
     */
    public function getHardware()
    {
        return $this->hardware;
    }

    /**
     * Sets a new hardware
     *
     * @param \WalmartDSV\HardwareType $hardware
     * @return self
     */
    public function setHardware(\WalmartDSV\HardwareType $hardware)
    {
        $this->hardware = $hardware;
        return $this;
    }

    /**
     * Gets as plumbingAndHVAC
     *
     * @return \WalmartDSV\PlumbingAndHVACType
     */
    public function getPlumbingAndHVAC()
    {
        return $this->plumbingAndHVAC;
    }

    /**
     * Sets a new plumbingAndHVAC
     *
     * @param \WalmartDSV\PlumbingAndHVACType $plumbingAndHVAC
     * @return self
     */
    public function setPlumbingAndHVAC(\WalmartDSV\PlumbingAndHVACType $plumbingAndHVAC)
    {
        $this->plumbingAndHVAC = $plumbingAndHVAC;
        return $this;
    }

    /**
     * Gets as tools
     *
     * @return \WalmartDSV\ToolsType
     */
    public function getTools()
    {
        return $this->tools;
    }

    /**
     * Sets a new tools
     *
     * @param \WalmartDSV\ToolsType $tools
     * @return self
     */
    public function setTools(\WalmartDSV\ToolsType $tools)
    {
        $this->tools = $tools;
        return $this;
    }

    /**
     * Gets as toolsAndHardwareOther
     *
     * @return \WalmartDSV\ToolsAndHardwareOtherType
     */
    public function getToolsAndHardwareOther()
    {
        return $this->toolsAndHardwareOther;
    }

    /**
     * Sets a new toolsAndHardwareOther
     *
     * @param \WalmartDSV\ToolsAndHardwareOtherType $toolsAndHardwareOther
     * @return self
     */
    public function setToolsAndHardwareOther(\WalmartDSV\ToolsAndHardwareOtherType $toolsAndHardwareOther)
    {
        $this->toolsAndHardwareOther = $toolsAndHardwareOther;
        return $this;
    }


}

