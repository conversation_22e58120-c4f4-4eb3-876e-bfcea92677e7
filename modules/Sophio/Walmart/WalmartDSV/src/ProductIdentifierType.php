<?php

namespace WalmartDSV;

/**
 * Class representing ProductIdentifierType
 *
 *
 * XSD Type: productIdentifier
 */
class ProductIdentifierType
{

    /**
     * UPC: GTIN-12, the 12-digit number including check-digit. If less than 12-digits, such as UPC-E which is 8-digits, add leading zeros up to 12-digits.; GTIN: GTIN-14, the 14-digit number including check-digit. If less than 14-digits add leading zeros up to 14-digits. ISBN: International Standard Book Number, the 10 or 13-digit number including check-digit.; EAN: GTIN-13, the 13-digit number including check-digit. If less than 13-digits add leading zeros up to 13-digits.
     *
     * @var string $productIdType
     */
    private $productIdType = null;

    /**
     * Alphanumeric ID that uniquely identifies the product. Used in conjunction with "Product ID Type". NOTE: For GTIN-14 or UPC, include the check digit.
     *
     * @var string $productId
     */
    private $productId = null;

    /**
     * Gets as productIdType
     *
     * UPC: GTIN-12, the 12-digit number including check-digit. If less than 12-digits, such as UPC-E which is 8-digits, add leading zeros up to 12-digits.; GTIN: GTIN-14, the 14-digit number including check-digit. If less than 14-digits add leading zeros up to 14-digits. ISBN: International Standard Book Number, the 10 or 13-digit number including check-digit.; EAN: GTIN-13, the 13-digit number including check-digit. If less than 13-digits add leading zeros up to 13-digits.
     *
     * @return string
     */
    public function getProductIdType()
    {
        return $this->productIdType;
    }

    /**
     * Sets a new productIdType
     *
     * UPC: GTIN-12, the 12-digit number including check-digit. If less than 12-digits, such as UPC-E which is 8-digits, add leading zeros up to 12-digits.; GTIN: GTIN-14, the 14-digit number including check-digit. If less than 14-digits add leading zeros up to 14-digits. ISBN: International Standard Book Number, the 10 or 13-digit number including check-digit.; EAN: GTIN-13, the 13-digit number including check-digit. If less than 13-digits add leading zeros up to 13-digits.
     *
     * @param string $productIdType
     * @return self
     */
    public function setProductIdType($productIdType)
    {
        $this->productIdType = $productIdType;
        return $this;
    }

    /**
     * Gets as productId
     *
     * Alphanumeric ID that uniquely identifies the product. Used in conjunction with "Product ID Type". NOTE: For GTIN-14 or UPC, include the check digit.
     *
     * @return string
     */
    public function getProductId()
    {
        return $this->productId;
    }

    /**
     * Sets a new productId
     *
     * Alphanumeric ID that uniquely identifies the product. Used in conjunction with "Product ID Type". NOTE: For GTIN-14 or UPC, include the check digit.
     *
     * @param string $productId
     * @return self
     */
    public function setProductId($productId)
    {
        $this->productId = $productId;
        return $this;
    }


}

