<?php

namespace WalmartDSV;

/**
 * Class representing FactoryIdType
 *
 * For items requiring factory disclosure to Walmart, enter the Walmart Factory ID of the factory where this item will be manufactured. Factory ID’s can be obtained by disclosing factory information in Supplier Profile on Retail Link.
 * XSD Type: FactoryId
 */
class FactoryIdType
{

    /**
     * @var string[] $factoryIdValue
     */
    private $factoryIdValue = [
        
    ];

    /**
     * Adds as factoryIdValue
     *
     * @return self
     * @param string $factoryIdValue
     */
    public function addToFactoryIdValue($factoryIdValue)
    {
        $this->factoryIdValue[] = $factoryIdValue;
        return $this;
    }

    /**
     * isset factoryIdValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFactoryIdValue($index)
    {
        return isset($this->factoryIdValue[$index]);
    }

    /**
     * unset factoryIdValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFactoryIdValue($index)
    {
        unset($this->factoryIdValue[$index]);
    }

    /**
     * Gets as factoryIdValue
     *
     * @return string[]
     */
    public function getFactoryIdValue()
    {
        return $this->factoryIdValue;
    }

    /**
     * Sets a new factoryIdValue
     *
     * @param string $factoryIdValue
     * @return self
     */
    public function setFactoryIdValue(array $factoryIdValue)
    {
        $this->factoryIdValue = $factoryIdValue;
        return $this;
    }


}

