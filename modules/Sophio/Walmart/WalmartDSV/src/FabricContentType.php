<?php

namespace WalmartDSV;

/**
 * Class representing FabricContentType
 *
 * Material makeup of the item.
 * XSD Type: FabricContent
 */
class FabricContentType
{

    /**
     * @var \WalmartDSV\FabricContentValueType[] $fabricContentValue
     */
    private $fabricContentValue = [
        
    ];

    /**
     * Adds as fabricContentValue
     *
     * @param \WalmartDSV\FabricContentValueType $fabricContentValue
     *@return self
     */
    public function addToFabricContentValue(\WalmartDSV\FabricContentValueType $fabricContentValue)
    {
        $this->fabricContentValue[] = $fabricContentValue;
        return $this;
    }

    /**
     * isset fabricContentValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricContentValue($index)
    {
        return isset($this->fabricContentValue[$index]);
    }

    /**
     * unset fabricContentValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricContentValue($index)
    {
        unset($this->fabricContentValue[$index]);
    }

    /**
     * Gets as fabricContentValue
     *
     * @return \WalmartDSV\FabricContentValueType[]
     */
    public function getFabricContentValue()
    {
        return $this->fabricContentValue;
    }

    /**
     * Sets a new fabricContentValue
     *
     * @param \WalmartDSV\FabricContentValueType[] $fabricContentValue
     * @return self
     */
    public function setFabricContentValue(array $fabricContentValue)
    {
        $this->fabricContentValue = $fabricContentValue;
        return $this;
    }


}

