<?php

namespace WalmartDSV;

/**
 * Class representing FoodStorageTipsType
 *
 * Helpful information related to food storage.
 * XSD Type: FoodStorageTips
 */
class FoodStorageTipsType
{

    /**
     * @var string[] $foodStorageTipsValue
     */
    private $foodStorageTipsValue = [
        
    ];

    /**
     * Adds as foodStorageTipsValue
     *
     * @return self
     * @param string $foodStorageTipsValue
     */
    public function addToFoodStorageTipsValue($foodStorageTipsValue)
    {
        $this->foodStorageTipsValue[] = $foodStorageTipsValue;
        return $this;
    }

    /**
     * isset foodStorageTipsValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFoodStorageTipsValue($index)
    {
        return isset($this->foodStorageTipsValue[$index]);
    }

    /**
     * unset foodStorageTipsValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFoodStorageTipsValue($index)
    {
        unset($this->foodStorageTipsValue[$index]);
    }

    /**
     * Gets as foodStorageTipsValue
     *
     * @return string[]
     */
    public function getFoodStorageTipsValue()
    {
        return $this->foodStorageTipsValue;
    }

    /**
     * Sets a new foodStorageTipsValue
     *
     * @param string $foodStorageTipsValue
     * @return self
     */
    public function setFoodStorageTipsValue(array $foodStorageTipsValue)
    {
        $this->foodStorageTipsValue = $foodStorageTipsValue;
        return $this;
    }


}

