<?php

namespace WalmartDSV;

/**
 * Class representing ProcessorType
 *
 * Commonly used retail name for the central processing unit.
 * XSD Type: ProcessorType
 */
class ProcessorType
{

    /**
     * @var string[] $processorTypeValue
     */
    private $processorTypeValue = [
        
    ];

    /**
     * Adds as processorTypeValue
     *
     * @return self
     * @param string $processorTypeValue
     */
    public function addToProcessorTypeValue($processorTypeValue)
    {
        $this->processorTypeValue[] = $processorTypeValue;
        return $this;
    }

    /**
     * isset processorTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProcessorTypeValue($index)
    {
        return isset($this->processorTypeValue[$index]);
    }

    /**
     * unset processorTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProcessorTypeValue($index)
    {
        unset($this->processorTypeValue[$index]);
    }

    /**
     * Gets as processorTypeValue
     *
     * @return string[]
     */
    public function getProcessorTypeValue()
    {
        return $this->processorTypeValue;
    }

    /**
     * Sets a new processorTypeValue
     *
     * @param string $processorTypeValue
     * @return self
     */
    public function setProcessorTypeValue(array $processorTypeValue)
    {
        $this->processorTypeValue = $processorTypeValue;
        return $this;
    }


}

