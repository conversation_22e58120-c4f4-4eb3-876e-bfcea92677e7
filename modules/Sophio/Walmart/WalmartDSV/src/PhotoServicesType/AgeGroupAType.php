<?php

namespace WalmartDSV\PhotoServicesType;

/**
 * Class representing AgeGroupAType
 */
class AgeGroupAType
{

    /**
     * @var string[] $ageGroupValue
     */
    private $ageGroupValue = [
        
    ];

    /**
     * Adds as ageGroupValue
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroupValue($ageGroupValue)
    {
        $this->ageGroupValue[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroupValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroupValue($index)
    {
        return isset($this->ageGroupValue[$index]);
    }

    /**
     * unset ageGroupValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroupValue($index)
    {
        unset($this->ageGroupValue[$index]);
    }

    /**
     * Gets as ageGroupValue
     *
     * @return string[]
     */
    public function getAgeGroupValue()
    {
        return $this->ageGroupValue;
    }

    /**
     * Sets a new ageGroupValue
     *
     * @param string $ageGroupValue
     * @return self
     */
    public function setAgeGroupValue(array $ageGroupValue)
    {
        $this->ageGroupValue = $ageGroupValue;
        return $this;
    }


}

