<?php

namespace WalmartDSV;

/**
 * Class representing AnimalHealthConcernType
 *
 * Type of condition that the item is intended to address.
 * XSD Type: AnimalHealthConcern
 */
class AnimalHealthConcernType
{

    /**
     * @var string[] $animalHealthConcernValue
     */
    private $animalHealthConcernValue = [
        
    ];

    /**
     * Adds as animalHealthConcernValue
     *
     * @return self
     * @param string $animalHealthConcernValue
     */
    public function addToAnimalHealthConcernValue($animalHealthConcernValue)
    {
        $this->animalHealthConcernValue[] = $animalHealthConcernValue;
        return $this;
    }

    /**
     * isset animalHealthConcernValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAnimalHealthConcernValue($index)
    {
        return isset($this->animalHealthConcernValue[$index]);
    }

    /**
     * unset animalHealthConcernValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAnimalHealthConcernValue($index)
    {
        unset($this->animalHealthConcernValue[$index]);
    }

    /**
     * Gets as animalHealthConcernValue
     *
     * @return string[]
     */
    public function getAnimalHealthConcernValue()
    {
        return $this->animalHealthConcernValue;
    }

    /**
     * Sets a new animalHealthConcernValue
     *
     * @param string $animalHealthConcernValue
     * @return self
     */
    public function setAnimalHealthConcernValue(array $animalHealthConcernValue)
    {
        $this->animalHealthConcernValue = $animalHealthConcernValue;
        return $this;
    }


}

