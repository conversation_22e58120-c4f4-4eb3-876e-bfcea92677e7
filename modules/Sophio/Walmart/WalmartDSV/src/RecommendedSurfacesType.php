<?php

namespace WalmartDSV;

/**
 * Class representing RecommendedSurfacesType
 *
 * The surfaces that a product is marketed for, or meant to be compatible with.
 * XSD Type: RecommendedSurfaces
 */
class RecommendedSurfacesType
{

    /**
     * @var string[] $recommendedSurface
     */
    private $recommendedSurface = [
        
    ];

    /**
     * Adds as recommendedSurface
     *
     * @return self
     * @param string $recommendedSurface
     */
    public function addToRecommendedSurface($recommendedSurface)
    {
        $this->recommendedSurface[] = $recommendedSurface;
        return $this;
    }

    /**
     * isset recommendedSurface
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedSurface($index)
    {
        return isset($this->recommendedSurface[$index]);
    }

    /**
     * unset recommendedSurface
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedSurface($index)
    {
        unset($this->recommendedSurface[$index]);
    }

    /**
     * Gets as recommendedSurface
     *
     * @return string[]
     */
    public function getRecommendedSurface()
    {
        return $this->recommendedSurface;
    }

    /**
     * Sets a new recommendedSurface
     *
     * @param string $recommendedSurface
     * @return self
     */
    public function setRecommendedSurface(array $recommendedSurface)
    {
        $this->recommendedSurface = $recommendedSurface;
        return $this;
    }


}

