<?php

namespace WalmartDSV;

/**
 * Class representing DigitalAudioFileFormatType
 *
 * The file format of the digital audio file.
 * XSD Type: DigitalAudioFileFormat
 */
class DigitalAudioFileFormatType
{

    /**
     * @var string[] $digitalAudioFileFormatValue
     */
    private $digitalAudioFileFormatValue = [
        
    ];

    /**
     * Adds as digitalAudioFileFormatValue
     *
     * @return self
     * @param string $digitalAudioFileFormatValue
     */
    public function addToDigitalAudioFileFormatValue($digitalAudioFileFormatValue)
    {
        $this->digitalAudioFileFormatValue[] = $digitalAudioFileFormatValue;
        return $this;
    }

    /**
     * isset digitalAudioFileFormatValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetDigitalAudioFileFormatValue($index)
    {
        return isset($this->digitalAudioFileFormatValue[$index]);
    }

    /**
     * unset digitalAudioFileFormatValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetDigitalAudioFileFormatValue($index)
    {
        unset($this->digitalAudioFileFormatValue[$index]);
    }

    /**
     * Gets as digitalAudioFileFormatValue
     *
     * @return string[]
     */
    public function getDigitalAudioFileFormatValue()
    {
        return $this->digitalAudioFileFormatValue;
    }

    /**
     * Sets a new digitalAudioFileFormatValue
     *
     * @param string $digitalAudioFileFormatValue
     * @return self
     */
    public function setDigitalAudioFileFormatValue(array $digitalAudioFileFormatValue)
    {
        $this->digitalAudioFileFormatValue = $digitalAudioFileFormatValue;
        return $this;
    }


}

