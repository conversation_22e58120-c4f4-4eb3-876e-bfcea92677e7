<?php

namespace WalmartDSV;

/**
 * Class representing HealthAndBeautyType
 *
 *
 * XSD Type: HealthAndBeauty
 */
class HealthAndBeautyType
{

    /**
     * @var \WalmartDSV\MedicalAidsType $medicalAids
     */
    private $medicalAids = null;

    /**
     * @var \WalmartDSV\OpticalType $optical
     */
    private $optical = null;

    /**
     * @var \WalmartDSV\MedicineAndSupplementsType $medicineAndSupplements
     */
    private $medicineAndSupplements = null;

    /**
     * @var \WalmartDSV\HealthAndBeautyElectronicsType $healthAndBeautyElectronics
     */
    private $healthAndBeautyElectronics = null;

    /**
     * @var \WalmartDSV\PersonalCareType $personalCare
     */
    private $personalCare = null;

    /**
     * Gets as medicalAids
     *
     * @return \WalmartDSV\MedicalAidsType
     */
    public function getMedicalAids()
    {
        return $this->medicalAids;
    }

    /**
     * Sets a new medicalAids
     *
     * @param \WalmartDSV\MedicalAidsType $medicalAids
     * @return self
     */
    public function setMedicalAids(\WalmartDSV\MedicalAidsType $medicalAids)
    {
        $this->medicalAids = $medicalAids;
        return $this;
    }

    /**
     * Gets as optical
     *
     * @return \WalmartDSV\OpticalType
     */
    public function getOptical()
    {
        return $this->optical;
    }

    /**
     * Sets a new optical
     *
     * @param \WalmartDSV\OpticalType $optical
     * @return self
     */
    public function setOptical(\WalmartDSV\OpticalType $optical)
    {
        $this->optical = $optical;
        return $this;
    }

    /**
     * Gets as medicineAndSupplements
     *
     * @return \WalmartDSV\MedicineAndSupplementsType
     */
    public function getMedicineAndSupplements()
    {
        return $this->medicineAndSupplements;
    }

    /**
     * Sets a new medicineAndSupplements
     *
     * @param \WalmartDSV\MedicineAndSupplementsType $medicineAndSupplements
     * @return self
     */
    public function setMedicineAndSupplements(\WalmartDSV\MedicineAndSupplementsType $medicineAndSupplements)
    {
        $this->medicineAndSupplements = $medicineAndSupplements;
        return $this;
    }

    /**
     * Gets as healthAndBeautyElectronics
     *
     * @return \WalmartDSV\HealthAndBeautyElectronicsType
     */
    public function getHealthAndBeautyElectronics()
    {
        return $this->healthAndBeautyElectronics;
    }

    /**
     * Sets a new healthAndBeautyElectronics
     *
     * @param \WalmartDSV\HealthAndBeautyElectronicsType $healthAndBeautyElectronics
     * @return self
     */
    public function setHealthAndBeautyElectronics(\WalmartDSV\HealthAndBeautyElectronicsType $healthAndBeautyElectronics)
    {
        $this->healthAndBeautyElectronics = $healthAndBeautyElectronics;
        return $this;
    }

    /**
     * Gets as personalCare
     *
     * @return \WalmartDSV\PersonalCareType
     */
    public function getPersonalCare()
    {
        return $this->personalCare;
    }

    /**
     * Sets a new personalCare
     *
     * @param \WalmartDSV\PersonalCareType $personalCare
     * @return self
     */
    public function setPersonalCare(\WalmartDSV\PersonalCareType $personalCare)
    {
        $this->personalCare = $personalCare;
        return $this;
    }


}

