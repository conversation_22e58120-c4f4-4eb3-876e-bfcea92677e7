<?php

namespace WalmartDSV;

/**
 * Class representing ArtAndCraftCategoryType
 *
 *
 * XSD Type: ArtAndCraftCategory
 */
class ArtAndCraftCategoryType
{

    /**
     * @var \WalmartDSV\ArtAndCraftType $artAndCraft
     */
    private $artAndCraft = null;

    /**
     * Gets as artAndCraft
     *
     * @return \WalmartDSV\ArtAndCraftType
     */
    public function getArtAndCraft()
    {
        return $this->artAndCraft;
    }

    /**
     * Sets a new artAndCraft
     *
     * @param \WalmartDSV\ArtAndCraftType $artAndCraft
     * @return self
     */
    public function setArtAndCraft(\WalmartDSV\ArtAndCraftType $artAndCraft)
    {
        $this->artAndCraft = $artAndCraft;
        return $this;
    }


}

