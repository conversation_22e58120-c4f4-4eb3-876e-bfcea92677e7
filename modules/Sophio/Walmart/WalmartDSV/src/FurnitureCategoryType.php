<?php

namespace WalmartDSV;

/**
 * Class representing FurnitureCategoryType
 *
 *
 * XSD Type: FurnitureCategory
 */
class FurnitureCategoryType
{

    /**
     * @var \WalmartDSV\FurnitureType $furniture
     */
    private $furniture = null;

    /**
     * Gets as furniture
     *
     * @return \WalmartDSV\FurnitureType
     */
    public function getFurniture()
    {
        return $this->furniture;
    }

    /**
     * Sets a new furniture
     *
     * @param \WalmartDSV\FurnitureType $furniture
     * @return self
     */
    public function setFurniture(\WalmartDSV\FurnitureType $furniture)
    {
        $this->furniture = $furniture;
        return $this;
    }


}

