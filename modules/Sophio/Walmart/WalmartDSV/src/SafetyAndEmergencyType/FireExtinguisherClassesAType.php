<?php

namespace WalmartDSV\SafetyAndEmergencyType;

/**
 * Class representing FireExtinguisherClassesAType
 */
class FireExtinguisherClassesAType
{

    /**
     * @var string[] $fireExtinguisherClassesValue
     */
    private $fireExtinguisherClassesValue = [
        
    ];

    /**
     * Adds as fireExtinguisherClassesValue
     *
     * @return self
     * @param string $fireExtinguisherClassesValue
     */
    public function addToFireExtinguisherClassesValue($fireExtinguisherClassesValue)
    {
        $this->fireExtinguisherClassesValue[] = $fireExtinguisherClassesValue;
        return $this;
    }

    /**
     * isset fireExtinguisherClassesValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFireExtinguisherClassesValue($index)
    {
        return isset($this->fireExtinguisherClassesValue[$index]);
    }

    /**
     * unset fireExtinguisherClassesValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFireExtinguisherClassesValue($index)
    {
        unset($this->fireExtinguisherClassesValue[$index]);
    }

    /**
     * Gets as fireExtinguisherClassesValue
     *
     * @return string[]
     */
    public function getFireExtinguisherClassesValue()
    {
        return $this->fireExtinguisherClassesValue;
    }

    /**
     * Sets a new fireExtinguisherClassesValue
     *
     * @param string $fireExtinguisherClassesValue
     * @return self
     */
    public function setFireExtinguisherClassesValue(array $fireExtinguisherClassesValue)
    {
        $this->fireExtinguisherClassesValue = $fireExtinguisherClassesValue;
        return $this;
    }


}

