<?php

namespace WalmartDSV;

/**
 * Class representing MusicType
 *
 *
 * XSD Type: Music
 */
class MusicType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * A summary of narrative content.
     *
     * @var string $synopsis
     */
    private $synopsis = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @var string $title
     */
    private $title = null;

    /**
     * The physical form in which the customer receives the product. For digital file formats use "Digital Audio File Format" (for mp3, aiff, etc), "Digital Video File Format" (for mp4, mov, etc), or "Digital File Format" (for exe, pdf, zip, etc).
     *
     * @var \WalmartDSV\MusicType\PhysicalMediaFormatAType $physicalMediaFormat
     */
    private $physicalMediaFormat = null;

    /**
     * The performer/s or name of group on the album or single.
     *
     * @var string[] $performer
     */
    private $performer = null;

    /**
     * A person credited with authorship of tracks on a music product.
     *
     * @var string $songwriter
     */
    private $songwriter = null;

    /**
     * General music category.
     *
     * @var string $musicGenre
     */
    private $musicGenre = null;

    /**
     * Specific music category.
     *
     * @var string $musicSubGenre
     */
    private $musicSubGenre = null;

    /**
     * The demographic for which the item is targeted.
     *
     * @var string[] $targetAudience
     */
    private $targetAudience = null;

    /**
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @var string[] $awardsWon
     */
    private $awardsWon = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * The file format of the digital audio file.
     *
     * @var string[] $digitalAudioFileFormat
     */
    private $digitalAudioFileFormat = null;

    /**
     * The brand or publishing company associated with the item.
     *
     * @var string $recordLabel
     */
    private $recordLabel = null;

    /**
     * Indicates the release date of a product from the manufacturer, in the format yyyy-mm-dd. This will be the date on which distribution of the product will be initiated.
     *
     * @var \DateTime $releaseDate
     */
    private $releaseDate = null;

    /**
     * A distinguishing feature of the release, such as number of tracks or type of performance.
     *
     * @var string $musicReleaseType
     */
    private $musicReleaseType = null;

    /**
     * List each track on the album with track name, number, and duration.
     *
     * @var \WalmartDSV\TrackListingType[] $trackListings
     */
    private $trackListings = null;

    /**
     * Number of tracks included in the item.
     *
     * @var int $numberOfTracks
     */
    private $numberOfTracks = null;

    /**
     * Person or entity credited with producing the album or single.
     *
     * @var string $musicProducer
     */
    private $musicProducer = null;

    /**
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @var string $seriesTitle
     */
    private $seriesTitle = null;

    /**
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @var int $numberInSeries
     */
    private $numberInSeries = null;

    /**
     * Y indicates the content has been altered as compared to its original release. For example, a song that has been edited to delete explicate language or to reduce the length of play.
     *
     * @var string $isEdited
     */
    private $isEdited = null;

    /**
     * Y indicates the content has undergone a specific process to improve a quality, or add a feature as compared to its original form. For example, a music CD that has tracks added to enable consumers to view details about the song’s title and performer on their TV screen.
     *
     * @var string $isEnhanced
     */
    private $isEnhanced = null;

    /**
     * The specific edition of the item.
     *
     * @var string $edition
     */
    private $edition = null;

    /**
     * Indicates whether a music album has been labeled with a Parental Advisory label by the Recording Industry Association of America.
     *
     * @var string $hasParentalAdvisoryLabel
     */
    private $hasParentalAdvisoryLabel = null;

    /**
     * The reason for the rating of an entertainment product, such as a TV show, Movie, or Musical Album. Reasons for suggesting that content is not appropriate for a general audience include Profanity, Drug Use, Violence, Nudity, and Sexual Content.
     *
     * @var string $ratingReason
     */
    private $ratingReason = null;

    /**
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @var string[] $parentalAdvisoryLabelURL
     */
    private $parentalAdvisoryLabelURL = null;

    /**
     * Number of discs included in the item.
     *
     * @var int $numberOfDiscs
     */
    private $numberOfDiscs = null;

    /**
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @var string $isAdultProduct
     */
    private $isAdultProduct = null;

    /**
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @var string[] $originalLanguages
     */
    private $originalLanguages = null;

    /**
     * The full name of the person who has autographed this copy.
     *
     * @var string $autographedBy
     */
    private $autographedBy = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * @var \WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as synopsis
     *
     * A summary of narrative content.
     *
     * @return string
     */
    public function getSynopsis()
    {
        return $this->synopsis;
    }

    /**
     * Sets a new synopsis
     *
     * A summary of narrative content.
     *
     * @param string $synopsis
     * @return self
     */
    public function setSynopsis($synopsis)
    {
        $this->synopsis = $synopsis;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as title
     *
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Sets a new title
     *
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @param string $title
     * @return self
     */
    public function setTitle($title)
    {
        $this->title = $title;
        return $this;
    }

    /**
     * Gets as physicalMediaFormat
     *
     * The physical form in which the customer receives the product. For digital file formats use "Digital Audio File Format" (for mp3, aiff, etc), "Digital Video File Format" (for mp4, mov, etc), or "Digital File Format" (for exe, pdf, zip, etc).
     *
     * @return \WalmartDSV\MusicType\PhysicalMediaFormatAType
     */
    public function getPhysicalMediaFormat()
    {
        return $this->physicalMediaFormat;
    }

    /**
     * Sets a new physicalMediaFormat
     *
     * The physical form in which the customer receives the product. For digital file formats use "Digital Audio File Format" (for mp3, aiff, etc), "Digital Video File Format" (for mp4, mov, etc), or "Digital File Format" (for exe, pdf, zip, etc).
     *
     * @param \WalmartDSV\MusicType\PhysicalMediaFormatAType $physicalMediaFormat
     * @return self
     */
    public function setPhysicalMediaFormat(\WalmartDSV\MusicType\PhysicalMediaFormatAType $physicalMediaFormat)
    {
        $this->physicalMediaFormat = $physicalMediaFormat;
        return $this;
    }

    /**
     * Adds as performerValue
     *
     * The performer/s or name of group on the album or single.
     *
     * @return self
     * @param string $performerValue
     */
    public function addToPerformer($performerValue)
    {
        $this->performer[] = $performerValue;
        return $this;
    }

    /**
     * isset performer
     *
     * The performer/s or name of group on the album or single.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPerformer($index)
    {
        return isset($this->performer[$index]);
    }

    /**
     * unset performer
     *
     * The performer/s or name of group on the album or single.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPerformer($index)
    {
        unset($this->performer[$index]);
    }

    /**
     * Gets as performer
     *
     * The performer/s or name of group on the album or single.
     *
     * @return string[]
     */
    public function getPerformer()
    {
        return $this->performer;
    }

    /**
     * Sets a new performer
     *
     * The performer/s or name of group on the album or single.
     *
     * @param string $performer
     * @return self
     */
    public function setPerformer(array $performer)
    {
        $this->performer = $performer;
        return $this;
    }

    /**
     * Gets as songwriter
     *
     * A person credited with authorship of tracks on a music product.
     *
     * @return string
     */
    public function getSongwriter()
    {
        return $this->songwriter;
    }

    /**
     * Sets a new songwriter
     *
     * A person credited with authorship of tracks on a music product.
     *
     * @param string $songwriter
     * @return self
     */
    public function setSongwriter($songwriter)
    {
        $this->songwriter = $songwriter;
        return $this;
    }

    /**
     * Gets as musicGenre
     *
     * General music category.
     *
     * @return string
     */
    public function getMusicGenre()
    {
        return $this->musicGenre;
    }

    /**
     * Sets a new musicGenre
     *
     * General music category.
     *
     * @param string $musicGenre
     * @return self
     */
    public function setMusicGenre($musicGenre)
    {
        $this->musicGenre = $musicGenre;
        return $this;
    }

    /**
     * Gets as musicSubGenre
     *
     * Specific music category.
     *
     * @return string
     */
    public function getMusicSubGenre()
    {
        return $this->musicSubGenre;
    }

    /**
     * Sets a new musicSubGenre
     *
     * Specific music category.
     *
     * @param string $musicSubGenre
     * @return self
     */
    public function setMusicSubGenre($musicSubGenre)
    {
        $this->musicSubGenre = $musicSubGenre;
        return $this;
    }

    /**
     * Adds as targetAudienceValue
     *
     * The demographic for which the item is targeted.
     *
     * @return self
     * @param string $targetAudienceValue
     */
    public function addToTargetAudience($targetAudienceValue)
    {
        $this->targetAudience[] = $targetAudienceValue;
        return $this;
    }

    /**
     * isset targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetTargetAudience($index)
    {
        return isset($this->targetAudience[$index]);
    }

    /**
     * unset targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetTargetAudience($index)
    {
        unset($this->targetAudience[$index]);
    }

    /**
     * Gets as targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @return string[]
     */
    public function getTargetAudience()
    {
        return $this->targetAudience;
    }

    /**
     * Sets a new targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param string $targetAudience
     * @return self
     */
    public function setTargetAudience(array $targetAudience)
    {
        $this->targetAudience = $targetAudience;
        return $this;
    }

    /**
     * Adds as awardsWonValue
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @return self
     * @param string $awardsWonValue
     */
    public function addToAwardsWon($awardsWonValue)
    {
        $this->awardsWon[] = $awardsWonValue;
        return $this;
    }

    /**
     * isset awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAwardsWon($index)
    {
        return isset($this->awardsWon[$index]);
    }

    /**
     * unset awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAwardsWon($index)
    {
        unset($this->awardsWon[$index]);
    }

    /**
     * Gets as awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @return string[]
     */
    public function getAwardsWon()
    {
        return $this->awardsWon;
    }

    /**
     * Sets a new awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param string $awardsWon
     * @return self
     */
    public function setAwardsWon(array $awardsWon)
    {
        $this->awardsWon = $awardsWon;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Adds as digitalAudioFileFormatValue
     *
     * The file format of the digital audio file.
     *
     * @return self
     * @param string $digitalAudioFileFormatValue
     */
    public function addToDigitalAudioFileFormat($digitalAudioFileFormatValue)
    {
        $this->digitalAudioFileFormat[] = $digitalAudioFileFormatValue;
        return $this;
    }

    /**
     * isset digitalAudioFileFormat
     *
     * The file format of the digital audio file.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetDigitalAudioFileFormat($index)
    {
        return isset($this->digitalAudioFileFormat[$index]);
    }

    /**
     * unset digitalAudioFileFormat
     *
     * The file format of the digital audio file.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetDigitalAudioFileFormat($index)
    {
        unset($this->digitalAudioFileFormat[$index]);
    }

    /**
     * Gets as digitalAudioFileFormat
     *
     * The file format of the digital audio file.
     *
     * @return string[]
     */
    public function getDigitalAudioFileFormat()
    {
        return $this->digitalAudioFileFormat;
    }

    /**
     * Sets a new digitalAudioFileFormat
     *
     * The file format of the digital audio file.
     *
     * @param string $digitalAudioFileFormat
     * @return self
     */
    public function setDigitalAudioFileFormat(array $digitalAudioFileFormat)
    {
        $this->digitalAudioFileFormat = $digitalAudioFileFormat;
        return $this;
    }

    /**
     * Gets as recordLabel
     *
     * The brand or publishing company associated with the item.
     *
     * @return string
     */
    public function getRecordLabel()
    {
        return $this->recordLabel;
    }

    /**
     * Sets a new recordLabel
     *
     * The brand or publishing company associated with the item.
     *
     * @param string $recordLabel
     * @return self
     */
    public function setRecordLabel($recordLabel)
    {
        $this->recordLabel = $recordLabel;
        return $this;
    }

    /**
     * Gets as releaseDate
     *
     * Indicates the release date of a product from the manufacturer, in the format yyyy-mm-dd. This will be the date on which distribution of the product will be initiated.
     *
     * @return \DateTime
     */
    public function getReleaseDate()
    {
        return $this->releaseDate;
    }

    /**
     * Sets a new releaseDate
     *
     * Indicates the release date of a product from the manufacturer, in the format yyyy-mm-dd. This will be the date on which distribution of the product will be initiated.
     *
     * @param \DateTime $releaseDate
     * @return self
     */
    public function setReleaseDate(\DateTime $releaseDate)
    {
        $this->releaseDate = $releaseDate;
        return $this;
    }

    /**
     * Gets as musicReleaseType
     *
     * A distinguishing feature of the release, such as number of tracks or type of performance.
     *
     * @return string
     */
    public function getMusicReleaseType()
    {
        return $this->musicReleaseType;
    }

    /**
     * Sets a new musicReleaseType
     *
     * A distinguishing feature of the release, such as number of tracks or type of performance.
     *
     * @param string $musicReleaseType
     * @return self
     */
    public function setMusicReleaseType($musicReleaseType)
    {
        $this->musicReleaseType = $musicReleaseType;
        return $this;
    }

    /**
     * Adds as trackListing
     *
     * List each track on the album with track name, number, and duration.
     *
     * @param \WalmartDSV\TrackListingType $trackListing
     *@return self
     */
    public function addToTrackListings(\WalmartDSV\TrackListingType $trackListing)
    {
        $this->trackListings[] = $trackListing;
        return $this;
    }

    /**
     * isset trackListings
     *
     * List each track on the album with track name, number, and duration.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetTrackListings($index)
    {
        return isset($this->trackListings[$index]);
    }

    /**
     * unset trackListings
     *
     * List each track on the album with track name, number, and duration.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetTrackListings($index)
    {
        unset($this->trackListings[$index]);
    }

    /**
     * Gets as trackListings
     *
     * List each track on the album with track name, number, and duration.
     *
     * @return \WalmartDSV\TrackListingType[]
     */
    public function getTrackListings()
    {
        return $this->trackListings;
    }

    /**
     * Sets a new trackListings
     *
     * List each track on the album with track name, number, and duration.
     *
     * @param \WalmartDSV\TrackListingType[] $trackListings
     * @return self
     */
    public function setTrackListings(array $trackListings)
    {
        $this->trackListings = $trackListings;
        return $this;
    }

    /**
     * Gets as numberOfTracks
     *
     * Number of tracks included in the item.
     *
     * @return int
     */
    public function getNumberOfTracks()
    {
        return $this->numberOfTracks;
    }

    /**
     * Sets a new numberOfTracks
     *
     * Number of tracks included in the item.
     *
     * @param int $numberOfTracks
     * @return self
     */
    public function setNumberOfTracks($numberOfTracks)
    {
        $this->numberOfTracks = $numberOfTracks;
        return $this;
    }

    /**
     * Gets as musicProducer
     *
     * Person or entity credited with producing the album or single.
     *
     * @return string
     */
    public function getMusicProducer()
    {
        return $this->musicProducer;
    }

    /**
     * Sets a new musicProducer
     *
     * Person or entity credited with producing the album or single.
     *
     * @param string $musicProducer
     * @return self
     */
    public function setMusicProducer($musicProducer)
    {
        $this->musicProducer = $musicProducer;
        return $this;
    }

    /**
     * Gets as seriesTitle
     *
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @return string
     */
    public function getSeriesTitle()
    {
        return $this->seriesTitle;
    }

    /**
     * Sets a new seriesTitle
     *
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @param string $seriesTitle
     * @return self
     */
    public function setSeriesTitle($seriesTitle)
    {
        $this->seriesTitle = $seriesTitle;
        return $this;
    }

    /**
     * Gets as numberInSeries
     *
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @return int
     */
    public function getNumberInSeries()
    {
        return $this->numberInSeries;
    }

    /**
     * Sets a new numberInSeries
     *
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @param int $numberInSeries
     * @return self
     */
    public function setNumberInSeries($numberInSeries)
    {
        $this->numberInSeries = $numberInSeries;
        return $this;
    }

    /**
     * Gets as isEdited
     *
     * Y indicates the content has been altered as compared to its original release. For example, a song that has been edited to delete explicate language or to reduce the length of play.
     *
     * @return string
     */
    public function getIsEdited()
    {
        return $this->isEdited;
    }

    /**
     * Sets a new isEdited
     *
     * Y indicates the content has been altered as compared to its original release. For example, a song that has been edited to delete explicate language or to reduce the length of play.
     *
     * @param string $isEdited
     * @return self
     */
    public function setIsEdited($isEdited)
    {
        $this->isEdited = $isEdited;
        return $this;
    }

    /**
     * Gets as isEnhanced
     *
     * Y indicates the content has undergone a specific process to improve a quality, or add a feature as compared to its original form. For example, a music CD that has tracks added to enable consumers to view details about the song’s title and performer on their TV screen.
     *
     * @return string
     */
    public function getIsEnhanced()
    {
        return $this->isEnhanced;
    }

    /**
     * Sets a new isEnhanced
     *
     * Y indicates the content has undergone a specific process to improve a quality, or add a feature as compared to its original form. For example, a music CD that has tracks added to enable consumers to view details about the song’s title and performer on their TV screen.
     *
     * @param string $isEnhanced
     * @return self
     */
    public function setIsEnhanced($isEnhanced)
    {
        $this->isEnhanced = $isEnhanced;
        return $this;
    }

    /**
     * Gets as edition
     *
     * The specific edition of the item.
     *
     * @return string
     */
    public function getEdition()
    {
        return $this->edition;
    }

    /**
     * Sets a new edition
     *
     * The specific edition of the item.
     *
     * @param string $edition
     * @return self
     */
    public function setEdition($edition)
    {
        $this->edition = $edition;
        return $this;
    }

    /**
     * Gets as hasParentalAdvisoryLabel
     *
     * Indicates whether a music album has been labeled with a Parental Advisory label by the Recording Industry Association of America.
     *
     * @return string
     */
    public function getHasParentalAdvisoryLabel()
    {
        return $this->hasParentalAdvisoryLabel;
    }

    /**
     * Sets a new hasParentalAdvisoryLabel
     *
     * Indicates whether a music album has been labeled with a Parental Advisory label by the Recording Industry Association of America.
     *
     * @param string $hasParentalAdvisoryLabel
     * @return self
     */
    public function setHasParentalAdvisoryLabel($hasParentalAdvisoryLabel)
    {
        $this->hasParentalAdvisoryLabel = $hasParentalAdvisoryLabel;
        return $this;
    }

    /**
     * Gets as ratingReason
     *
     * The reason for the rating of an entertainment product, such as a TV show, Movie, or Musical Album. Reasons for suggesting that content is not appropriate for a general audience include Profanity, Drug Use, Violence, Nudity, and Sexual Content.
     *
     * @return string
     */
    public function getRatingReason()
    {
        return $this->ratingReason;
    }

    /**
     * Sets a new ratingReason
     *
     * The reason for the rating of an entertainment product, such as a TV show, Movie, or Musical Album. Reasons for suggesting that content is not appropriate for a general audience include Profanity, Drug Use, Violence, Nudity, and Sexual Content.
     *
     * @param string $ratingReason
     * @return self
     */
    public function setRatingReason($ratingReason)
    {
        $this->ratingReason = $ratingReason;
        return $this;
    }

    /**
     * Adds as parentalAdvisoryLabelURLValue
     *
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @return self
     * @param string $parentalAdvisoryLabelURLValue
     */
    public function addToParentalAdvisoryLabelURL($parentalAdvisoryLabelURLValue)
    {
        $this->parentalAdvisoryLabelURL[] = $parentalAdvisoryLabelURLValue;
        return $this;
    }

    /**
     * isset parentalAdvisoryLabelURL
     *
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetParentalAdvisoryLabelURL($index)
    {
        return isset($this->parentalAdvisoryLabelURL[$index]);
    }

    /**
     * unset parentalAdvisoryLabelURL
     *
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetParentalAdvisoryLabelURL($index)
    {
        unset($this->parentalAdvisoryLabelURL[$index]);
    }

    /**
     * Gets as parentalAdvisoryLabelURL
     *
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @return string[]
     */
    public function getParentalAdvisoryLabelURL()
    {
        return $this->parentalAdvisoryLabelURL;
    }

    /**
     * Sets a new parentalAdvisoryLabelURL
     *
     * If Has Parental Advisory Label=Y, pdf or image of label must be provided. URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2GB. (Please enter text with 2000 character max.)
     *
     * @param string $parentalAdvisoryLabelURL
     * @return self
     */
    public function setParentalAdvisoryLabelURL(array $parentalAdvisoryLabelURL)
    {
        $this->parentalAdvisoryLabelURL = $parentalAdvisoryLabelURL;
        return $this;
    }

    /**
     * Gets as numberOfDiscs
     *
     * Number of discs included in the item.
     *
     * @return int
     */
    public function getNumberOfDiscs()
    {
        return $this->numberOfDiscs;
    }

    /**
     * Sets a new numberOfDiscs
     *
     * Number of discs included in the item.
     *
     * @param int $numberOfDiscs
     * @return self
     */
    public function setNumberOfDiscs($numberOfDiscs)
    {
        $this->numberOfDiscs = $numberOfDiscs;
        return $this;
    }

    /**
     * Gets as isAdultProduct
     *
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @return string
     */
    public function getIsAdultProduct()
    {
        return $this->isAdultProduct;
    }

    /**
     * Sets a new isAdultProduct
     *
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @param string $isAdultProduct
     * @return self
     */
    public function setIsAdultProduct($isAdultProduct)
    {
        $this->isAdultProduct = $isAdultProduct;
        return $this;
    }

    /**
     * Adds as originalLanguage
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @return self
     * @param string $originalLanguage
     */
    public function addToOriginalLanguages($originalLanguage)
    {
        $this->originalLanguages[] = $originalLanguage;
        return $this;
    }

    /**
     * isset originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOriginalLanguages($index)
    {
        return isset($this->originalLanguages[$index]);
    }

    /**
     * unset originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOriginalLanguages($index)
    {
        unset($this->originalLanguages[$index]);
    }

    /**
     * Gets as originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @return string[]
     */
    public function getOriginalLanguages()
    {
        return $this->originalLanguages;
    }

    /**
     * Sets a new originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param string $originalLanguages
     * @return self
     */
    public function setOriginalLanguages(array $originalLanguages)
    {
        $this->originalLanguages = $originalLanguages;
        return $this;
    }

    /**
     * Gets as autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @return string
     */
    public function getAutographedBy()
    {
        return $this->autographedBy;
    }

    /**
     * Sets a new autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @param string $autographedBy
     * @return self
     */
    public function setAutographedBy($autographedBy)
    {
        $this->autographedBy = $autographedBy;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\MusicType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

