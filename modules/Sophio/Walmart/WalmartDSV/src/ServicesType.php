<?php

namespace WalmartDSV;

/**
 * Class representing ServicesType
 *
 *
 * XSD Type: Services
 */
class ServicesType
{

    /**
     * @var \WalmartDSV\PhotoServicesType $photoServices
     */
    private $photoServices = null;

    /**
     * Gets as photoServices
     *
     * @return \WalmartDSV\PhotoServicesType
     */
    public function getPhotoServices()
    {
        return $this->photoServices;
    }

    /**
     * Sets a new photoServices
     *
     * @param \WalmartDSV\PhotoServicesType $photoServices
     * @return self
     */
    public function setPhotoServices(\WalmartDSV\PhotoServicesType $photoServices)
    {
        $this->photoServices = $photoServices;
        return $this;
    }


}

