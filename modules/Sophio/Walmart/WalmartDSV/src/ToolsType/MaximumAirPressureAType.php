<?php

namespace WalmartDSV\ToolsType;

/**
 * Class representing MaximumAirPressureAType
 */
class MaximumAirPressureAType
{

    /**
     * @var float $measure
     */
    private $measure = null;

    /**
     * @var string $unit
     */
    private $unit = null;

    /**
     * Gets as measure
     *
     * @return float
     */
    public function getMeasure()
    {
        return $this->measure;
    }

    /**
     * Sets a new measure
     *
     * @param float $measure
     * @return self
     */
    public function setMeasure($measure)
    {
        $this->measure = $measure;
        return $this;
    }

    /**
     * Gets as unit
     *
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * Sets a new unit
     *
     * @param string $unit
     * @return self
     */
    public function setUnit($unit)
    {
        $this->unit = $unit;
        return $this;
    }


}

