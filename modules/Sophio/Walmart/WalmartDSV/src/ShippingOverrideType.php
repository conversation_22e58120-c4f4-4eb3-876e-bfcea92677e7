<?php

namespace WalmartDSV;

/**
 * Class representing ShippingOverrideType
 *
 *
 * XSD Type: shippingOverride
 */
class ShippingOverrideType
{

    /**
     * @var \WalmartDSV\ShippingOverrideType\ShippingOverrideShipMethodAType $shippingOverrideShipMethod
     */
    private $shippingOverrideShipMethod = null;

    /**
     * Fixed shipping price value for a ship method.
     *
     * @var \WalmartDSV\CurrencyUnitType $shippingOverrideFixedPrice
     */
    private $shippingOverrideFixedPrice = null;

    /**
     * Variable shipping price value for a ship method.
     *
     * @var \WalmartDSV\CurrencyUnitType $shippingOverrideVariablePrice
     */
    private $shippingOverrideVariablePrice = null;

    /**
     * Gets as shippingOverrideShipMethod
     *
     * @return \WalmartDSV\ShippingOverrideType\ShippingOverrideShipMethodAType
     */
    public function getShippingOverrideShipMethod()
    {
        return $this->shippingOverrideShipMethod;
    }

    /**
     * Sets a new shippingOverrideShipMethod
     *
     * @param \WalmartDSV\ShippingOverrideType\ShippingOverrideShipMethodAType $shippingOverrideShipMethod
     * @return self
     */
    public function setShippingOverrideShipMethod(\WalmartDSV\ShippingOverrideType\ShippingOverrideShipMethodAType $shippingOverrideShipMethod)
    {
        $this->shippingOverrideShipMethod = $shippingOverrideShipMethod;
        return $this;
    }

    /**
     * Gets as shippingOverrideFixedPrice
     *
     * Fixed shipping price value for a ship method.
     *
     * @return \WalmartDSV\CurrencyUnitType
     */
    public function getShippingOverrideFixedPrice()
    {
        return $this->shippingOverrideFixedPrice;
    }

    /**
     * Sets a new shippingOverrideFixedPrice
     *
     * Fixed shipping price value for a ship method.
     *
     * @param \WalmartDSV\CurrencyUnitType $shippingOverrideFixedPrice
     * @return self
     */
    public function setShippingOverrideFixedPrice(\WalmartDSV\CurrencyUnitType $shippingOverrideFixedPrice)
    {
        $this->shippingOverrideFixedPrice = $shippingOverrideFixedPrice;
        return $this;
    }

    /**
     * Gets as shippingOverrideVariablePrice
     *
     * Variable shipping price value for a ship method.
     *
     * @return \WalmartDSV\CurrencyUnitType
     */
    public function getShippingOverrideVariablePrice()
    {
        return $this->shippingOverrideVariablePrice;
    }

    /**
     * Sets a new shippingOverrideVariablePrice
     *
     * Variable shipping price value for a ship method.
     *
     * @param \WalmartDSV\CurrencyUnitType $shippingOverrideVariablePrice
     * @return self
     */
    public function setShippingOverrideVariablePrice(\WalmartDSV\CurrencyUnitType $shippingOverrideVariablePrice)
    {
        $this->shippingOverrideVariablePrice = $shippingOverrideVariablePrice;
        return $this;
    }


}

