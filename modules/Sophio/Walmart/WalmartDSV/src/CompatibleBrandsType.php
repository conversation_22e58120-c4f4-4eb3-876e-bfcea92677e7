<?php

namespace WalmartDSV;

/**
 * Class representing CompatibleBrandsType
 *
 * A list of the brands most commonly compatible with the item.
 * XSD Type: CompatibleBrands
 */
class CompatibleBrandsType
{

    /**
     * @var string[] $compatibleBrand
     */
    private $compatibleBrand = [
        
    ];

    /**
     * Adds as compatibleBrand
     *
     * @return self
     * @param string $compatibleBrand
     */
    public function addToCompatibleBrand($compatibleBrand)
    {
        $this->compatibleBrand[] = $compatibleBrand;
        return $this;
    }

    /**
     * isset compatibleBrand
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCompatibleBrand($index)
    {
        return isset($this->compatibleBrand[$index]);
    }

    /**
     * unset compatibleBrand
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCompatibleBrand($index)
    {
        unset($this->compatibleBrand[$index]);
    }

    /**
     * Gets as compatibleBrand
     *
     * @return string[]
     */
    public function getCompatibleBrand()
    {
        return $this->compatibleBrand;
    }

    /**
     * Sets a new compatibleBrand
     *
     * @param string $compatibleBrand
     * @return self
     */
    public function setCompatibleBrand(array $compatibleBrand)
    {
        $this->compatibleBrand = $compatibleBrand;
        return $this;
    }


}

