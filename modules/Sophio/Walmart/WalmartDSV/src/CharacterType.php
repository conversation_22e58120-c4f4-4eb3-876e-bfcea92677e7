<?php

namespace WalmartDSV;

/**
 * Class representing CharacterType
 *
 * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
 * XSD Type: Character
 */
class CharacterType
{

    /**
     * @var string $characterValue
     */
    private $characterValue = null;

    /**
     * Gets as characterValue
     *
     * @return string
     */
    public function getCharacterValue()
    {
        return $this->characterValue;
    }

    /**
     * Sets a new characterValue
     *
     * @param string $characterValue
     * @return self
     */
    public function setCharacterValue($characterValue)
    {
        $this->characterValue = $characterValue;
        return $this;
    }


}

