<?php

namespace WalmartDSV;

/**
 * Class representing CamerasAndLensesType
 *
 *
 * XSD Type: CamerasAndLenses
 */
class CamerasAndLensesType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The resolution at which this item records images.
     *
     * @var \WalmartDSV\CamerasAndLensesType\NumberOfMegapixelsAType $numberOfMegapixels
     */
    private $numberOfMegapixels = null;

    /**
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @var string $digitalZoom
     */
    private $digitalZoom = null;

    /**
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @var string $opticalZoom
     */
    private $opticalZoom = null;

    /**
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @var string $magnification
     */
    private $magnification = null;

    /**
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @var \WalmartDSV\ResolutionUnitType $sensorResolution
     */
    private $sensorResolution = null;

    /**
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @var \WalmartDSV\CamerasAndLensesType\LensDiameterAType $lensDiameter
     */
    private $lensDiameter = null;

    /**
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @var string $lensCoating
     */
    private $lensCoating = null;

    /**
     * Terms describing the kind of filter attached to a lens. Typically camera lens filters affect the amount and type of light that enters the lens.
     *
     * @var string $lensFilterType
     */
    private $lensFilterType = null;

    /**
     * Description of the lens' focal length, optical characteristics or special features.
     *
     * @var string $cameraLensType
     */
    private $cameraLensType = null;

    /**
     * Y indicates the device incorporates a camera flash.
     *
     * @var string $hasFlash
     */
    private $hasFlash = null;

    /**
     * The structure of an artificial light unit that a camera includes or can accommodate.
     *
     * @var string $flashType
     */
    private $flashType = null;

    /**
     * Measured in seconds.
     *
     * @var \WalmartDSV\CamerasAndLensesType\MinimumShutterSpeedAType $minimumShutterSpeed
     */
    private $minimumShutterSpeed = null;

    /**
     * Measured in seconds.
     *
     * @var \WalmartDSV\CamerasAndLensesType\MaximumShutterSpeedAType $maximumShutterSpeed
     */
    private $maximumShutterSpeed = null;

    /**
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @var string[] $focusType
     */
    private $focusType = null;

    /**
     * On a camera or lens, the distance between the image sensor and the lens when the subject is in focus, usually stated as a range in millimeters.
     *
     * @var \WalmartDSV\CamerasAndLensesType\FocalLengthAType $focalLength
     */
    private $focalLength = null;

    /**
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @var string $focalRatio
     */
    private $focalRatio = null;

    /**
     * The smallest aperture this item accommodates, indicating the how much light can pass through the lens and typically expressed in f-numbers.
     *
     * @var string $minimumAperture
     */
    private $minimumAperture = null;

    /**
     * Size of the largest aperture this item accommodates. For products such as cameras, this is the f-stop (f-number) of the widest opening the lens mechanism can produce. Important selection factor for consumers who look for a low f/stop value. This indicates more light can enter the lens and allow a faster shutter speed; especially in low light conditions.
     *
     * @var string $maximumAperture
     */
    private $maximumAperture = null;

    /**
     * Available settings to control shutter speed and lens aperture.
     *
     * @var string[] $exposureModes
     */
    private $exposureModes = null;

    /**
     * The primary technology used for the item's display.
     *
     * @var string $displayTechnology
     */
    private $displayTechnology = null;

    /**
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @var \WalmartDSV\DisplayResolutionType $displayResolution
     */
    private $displayResolution = null;

    /**
     * Typically measured on the diagonal in inches.
     *
     * @var \WalmartDSV\CamerasAndLensesType\ScreenSizeAType $screenSize
     */
    private $screenSize = null;

    /**
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @var \WalmartDSV\CamerasAndLensesType\DiameterAType $diameter
     */
    private $diameter = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\CamerasAndLensesType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\CamerasAndLensesType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\CamerasAndLensesType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\CamerasAndLensesType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Is product unassembled and must be put together before use?
     *
     * @var string $isAssemblyRequired
     */
    private $isAssemblyRequired = null;

    /**
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @var string $assemblyInstructions
     */
    private $assemblyInstructions = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @var string[] $accessoriesIncluded
     */
    private $accessoriesIncluded = null;

    /**
     * Does the display have touchscreen capabilities?
     *
     * @var string $hasTouchscreen
     */
    private $hasTouchscreen = null;

    /**
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @var string $hasMemoryCardSlot
     */
    private $hasMemoryCardSlot = null;

    /**
     * The memory card format applicable to the product.
     *
     * @var string[] $memoryCardType
     */
    private $memoryCardType = null;

    /**
     * The recording technologies compatible with the item.
     *
     * @var string[] $recordableMediaFormats
     */
    private $recordableMediaFormats = null;

    /**
     * Any wireless communications standard used within or by the item.
     *
     * @var string[] $wirelessTechnologies
     */
    private $wirelessTechnologies = null;

    /**
     * The standardized connections provided on the item.
     *
     * @var string[] $connections
     */
    private $connections = null;

    /**
     * Is the item designed to be easily moved?
     *
     * @var string $isPortable
     */
    private $isPortable = null;

    /**
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @var string $isWaterproof
     */
    private $isWaterproof = null;

    /**
     * Y indicates the device has a feature that allows a countdown, or a timed delay before taking an action, such as taking a photo.
     *
     * @var string $hasSelfTimer
     */
    private $hasSelfTimer = null;

    /**
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @var \WalmartDSV\TimeUnitType[] $selfTimerDelay
     */
    private $selfTimerDelay = null;

    /**
     * The number of continuous hours the device may remain in standby mode before shutting down.
     *
     * @var \WalmartDSV\CamerasAndLensesType\StandbyTimeAType $standbyTime
     */
    private $standbyTime = null;

    /**
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @var string $fieldOfView
     */
    private $fieldOfView = null;

    /**
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @var string $isParfocal
     */
    private $isParfocal = null;

    /**
     * Available settings designed to accommodate different photographic situations.
     *
     * @var string $shootingMode
     */
    private $shootingMode = null;

    /**
     * Y indicates the item contains a microphone to record sound, either internally or externally.
     *
     * @var string $microphoneIncluded
     */
    private $microphoneIncluded = null;

    /**
     * Y indicates the item contains a grip to make the item easier to hold, carry, or control.
     *
     * @var string $hasHandle
     */
    private $hasHandle = null;

    /**
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @var string $isMulticoated
     */
    private $isMulticoated = null;

    /**
     * Y indicates the item has a feature to reduce the appearance of red pupils in photos due to the red-eye effect.
     *
     * @var string $hasRedEyeReduction
     */
    private $hasRedEyeReduction = null;

    /**
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @var string $hasNightVision
     */
    private $hasNightVision = null;

    /**
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @var string $isFogResistant
     */
    private $isFogResistant = null;

    /**
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @var string $attachmentStyle
     */
    private $attachmentStyle = null;

    /**
     * Y indicates the item comes with a strap that allows a user to suspend the item from the shoulders.
     *
     * @var string $hasShoulderStrap
     */
    private $hasShoulderStrap = null;

    /**
     * A list of the brands most commonly compatible with the item.
     *
     * @var string[] $compatibleBrands
     */
    private $compatibleBrands = null;

    /**
     * A list of the devices compatible with the item.
     *
     * @var \WalmartDSV\CompatibleDevicesType $compatibleDevices
     */
    private $compatibleDevices = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Description of how the item should be cleaned and maintained.
     *
     * @var string $cleaningCareAndMaintenance
     */
    private $cleaningCareAndMaintenance = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * @var \WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as numberOfMegapixels
     *
     * The resolution at which this item records images.
     *
     * @return \WalmartDSV\CamerasAndLensesType\NumberOfMegapixelsAType
     */
    public function getNumberOfMegapixels()
    {
        return $this->numberOfMegapixels;
    }

    /**
     * Sets a new numberOfMegapixels
     *
     * The resolution at which this item records images.
     *
     * @param \WalmartDSV\CamerasAndLensesType\NumberOfMegapixelsAType $numberOfMegapixels
     * @return self
     */
    public function setNumberOfMegapixels(\WalmartDSV\CamerasAndLensesType\NumberOfMegapixelsAType $numberOfMegapixels)
    {
        $this->numberOfMegapixels = $numberOfMegapixels;
        return $this;
    }

    /**
     * Gets as digitalZoom
     *
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @return string
     */
    public function getDigitalZoom()
    {
        return $this->digitalZoom;
    }

    /**
     * Sets a new digitalZoom
     *
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @param string $digitalZoom
     * @return self
     */
    public function setDigitalZoom($digitalZoom)
    {
        $this->digitalZoom = $digitalZoom;
        return $this;
    }

    /**
     * Gets as opticalZoom
     *
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @return string
     */
    public function getOpticalZoom()
    {
        return $this->opticalZoom;
    }

    /**
     * Sets a new opticalZoom
     *
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @param string $opticalZoom
     * @return self
     */
    public function setOpticalZoom($opticalZoom)
    {
        $this->opticalZoom = $opticalZoom;
        return $this;
    }

    /**
     * Gets as magnification
     *
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @return string
     */
    public function getMagnification()
    {
        return $this->magnification;
    }

    /**
     * Sets a new magnification
     *
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @param string $magnification
     * @return self
     */
    public function setMagnification($magnification)
    {
        $this->magnification = $magnification;
        return $this;
    }

    /**
     * Gets as sensorResolution
     *
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @return \WalmartDSV\ResolutionUnitType
     */
    public function getSensorResolution()
    {
        return $this->sensorResolution;
    }

    /**
     * Sets a new sensorResolution
     *
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @param \WalmartDSV\ResolutionUnitType $sensorResolution
     * @return self
     */
    public function setSensorResolution(\WalmartDSV\ResolutionUnitType $sensorResolution)
    {
        $this->sensorResolution = $sensorResolution;
        return $this;
    }

    /**
     * Gets as lensDiameter
     *
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @return \WalmartDSV\CamerasAndLensesType\LensDiameterAType
     */
    public function getLensDiameter()
    {
        return $this->lensDiameter;
    }

    /**
     * Sets a new lensDiameter
     *
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @param \WalmartDSV\CamerasAndLensesType\LensDiameterAType $lensDiameter
     * @return self
     */
    public function setLensDiameter(\WalmartDSV\CamerasAndLensesType\LensDiameterAType $lensDiameter)
    {
        $this->lensDiameter = $lensDiameter;
        return $this;
    }

    /**
     * Gets as lensCoating
     *
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @return string
     */
    public function getLensCoating()
    {
        return $this->lensCoating;
    }

    /**
     * Sets a new lensCoating
     *
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @param string $lensCoating
     * @return self
     */
    public function setLensCoating($lensCoating)
    {
        $this->lensCoating = $lensCoating;
        return $this;
    }

    /**
     * Gets as lensFilterType
     *
     * Terms describing the kind of filter attached to a lens. Typically camera lens filters affect the amount and type of light that enters the lens.
     *
     * @return string
     */
    public function getLensFilterType()
    {
        return $this->lensFilterType;
    }

    /**
     * Sets a new lensFilterType
     *
     * Terms describing the kind of filter attached to a lens. Typically camera lens filters affect the amount and type of light that enters the lens.
     *
     * @param string $lensFilterType
     * @return self
     */
    public function setLensFilterType($lensFilterType)
    {
        $this->lensFilterType = $lensFilterType;
        return $this;
    }

    /**
     * Gets as cameraLensType
     *
     * Description of the lens' focal length, optical characteristics or special features.
     *
     * @return string
     */
    public function getCameraLensType()
    {
        return $this->cameraLensType;
    }

    /**
     * Sets a new cameraLensType
     *
     * Description of the lens' focal length, optical characteristics or special features.
     *
     * @param string $cameraLensType
     * @return self
     */
    public function setCameraLensType($cameraLensType)
    {
        $this->cameraLensType = $cameraLensType;
        return $this;
    }

    /**
     * Gets as hasFlash
     *
     * Y indicates the device incorporates a camera flash.
     *
     * @return string
     */
    public function getHasFlash()
    {
        return $this->hasFlash;
    }

    /**
     * Sets a new hasFlash
     *
     * Y indicates the device incorporates a camera flash.
     *
     * @param string $hasFlash
     * @return self
     */
    public function setHasFlash($hasFlash)
    {
        $this->hasFlash = $hasFlash;
        return $this;
    }

    /**
     * Gets as flashType
     *
     * The structure of an artificial light unit that a camera includes or can accommodate.
     *
     * @return string
     */
    public function getFlashType()
    {
        return $this->flashType;
    }

    /**
     * Sets a new flashType
     *
     * The structure of an artificial light unit that a camera includes or can accommodate.
     *
     * @param string $flashType
     * @return self
     */
    public function setFlashType($flashType)
    {
        $this->flashType = $flashType;
        return $this;
    }

    /**
     * Gets as minimumShutterSpeed
     *
     * Measured in seconds.
     *
     * @return \WalmartDSV\CamerasAndLensesType\MinimumShutterSpeedAType
     */
    public function getMinimumShutterSpeed()
    {
        return $this->minimumShutterSpeed;
    }

    /**
     * Sets a new minimumShutterSpeed
     *
     * Measured in seconds.
     *
     * @param \WalmartDSV\CamerasAndLensesType\MinimumShutterSpeedAType $minimumShutterSpeed
     * @return self
     */
    public function setMinimumShutterSpeed(\WalmartDSV\CamerasAndLensesType\MinimumShutterSpeedAType $minimumShutterSpeed)
    {
        $this->minimumShutterSpeed = $minimumShutterSpeed;
        return $this;
    }

    /**
     * Gets as maximumShutterSpeed
     *
     * Measured in seconds.
     *
     * @return \WalmartDSV\CamerasAndLensesType\MaximumShutterSpeedAType
     */
    public function getMaximumShutterSpeed()
    {
        return $this->maximumShutterSpeed;
    }

    /**
     * Sets a new maximumShutterSpeed
     *
     * Measured in seconds.
     *
     * @param \WalmartDSV\CamerasAndLensesType\MaximumShutterSpeedAType $maximumShutterSpeed
     * @return self
     */
    public function setMaximumShutterSpeed(\WalmartDSV\CamerasAndLensesType\MaximumShutterSpeedAType $maximumShutterSpeed)
    {
        $this->maximumShutterSpeed = $maximumShutterSpeed;
        return $this;
    }

    /**
     * Adds as focusTypeValue
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @return self
     * @param string $focusTypeValue
     */
    public function addToFocusType($focusTypeValue)
    {
        $this->focusType[] = $focusTypeValue;
        return $this;
    }

    /**
     * isset focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFocusType($index)
    {
        return isset($this->focusType[$index]);
    }

    /**
     * unset focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFocusType($index)
    {
        unset($this->focusType[$index]);
    }

    /**
     * Gets as focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @return string[]
     */
    public function getFocusType()
    {
        return $this->focusType;
    }

    /**
     * Sets a new focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param string $focusType
     * @return self
     */
    public function setFocusType(array $focusType)
    {
        $this->focusType = $focusType;
        return $this;
    }

    /**
     * Gets as focalLength
     *
     * On a camera or lens, the distance between the image sensor and the lens when the subject is in focus, usually stated as a range in millimeters.
     *
     * @return \WalmartDSV\CamerasAndLensesType\FocalLengthAType
     */
    public function getFocalLength()
    {
        return $this->focalLength;
    }

    /**
     * Sets a new focalLength
     *
     * On a camera or lens, the distance between the image sensor and the lens when the subject is in focus, usually stated as a range in millimeters.
     *
     * @param \WalmartDSV\CamerasAndLensesType\FocalLengthAType $focalLength
     * @return self
     */
    public function setFocalLength(\WalmartDSV\CamerasAndLensesType\FocalLengthAType $focalLength)
    {
        $this->focalLength = $focalLength;
        return $this;
    }

    /**
     * Gets as focalRatio
     *
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @return string
     */
    public function getFocalRatio()
    {
        return $this->focalRatio;
    }

    /**
     * Sets a new focalRatio
     *
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @param string $focalRatio
     * @return self
     */
    public function setFocalRatio($focalRatio)
    {
        $this->focalRatio = $focalRatio;
        return $this;
    }

    /**
     * Gets as minimumAperture
     *
     * The smallest aperture this item accommodates, indicating the how much light can pass through the lens and typically expressed in f-numbers.
     *
     * @return string
     */
    public function getMinimumAperture()
    {
        return $this->minimumAperture;
    }

    /**
     * Sets a new minimumAperture
     *
     * The smallest aperture this item accommodates, indicating the how much light can pass through the lens and typically expressed in f-numbers.
     *
     * @param string $minimumAperture
     * @return self
     */
    public function setMinimumAperture($minimumAperture)
    {
        $this->minimumAperture = $minimumAperture;
        return $this;
    }

    /**
     * Gets as maximumAperture
     *
     * Size of the largest aperture this item accommodates. For products such as cameras, this is the f-stop (f-number) of the widest opening the lens mechanism can produce. Important selection factor for consumers who look for a low f/stop value. This indicates more light can enter the lens and allow a faster shutter speed; especially in low light conditions.
     *
     * @return string
     */
    public function getMaximumAperture()
    {
        return $this->maximumAperture;
    }

    /**
     * Sets a new maximumAperture
     *
     * Size of the largest aperture this item accommodates. For products such as cameras, this is the f-stop (f-number) of the widest opening the lens mechanism can produce. Important selection factor for consumers who look for a low f/stop value. This indicates more light can enter the lens and allow a faster shutter speed; especially in low light conditions.
     *
     * @param string $maximumAperture
     * @return self
     */
    public function setMaximumAperture($maximumAperture)
    {
        $this->maximumAperture = $maximumAperture;
        return $this;
    }

    /**
     * Adds as exposureMode
     *
     * Available settings to control shutter speed and lens aperture.
     *
     * @return self
     * @param string $exposureMode
     */
    public function addToExposureModes($exposureMode)
    {
        $this->exposureModes[] = $exposureMode;
        return $this;
    }

    /**
     * isset exposureModes
     *
     * Available settings to control shutter speed and lens aperture.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetExposureModes($index)
    {
        return isset($this->exposureModes[$index]);
    }

    /**
     * unset exposureModes
     *
     * Available settings to control shutter speed and lens aperture.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetExposureModes($index)
    {
        unset($this->exposureModes[$index]);
    }

    /**
     * Gets as exposureModes
     *
     * Available settings to control shutter speed and lens aperture.
     *
     * @return string[]
     */
    public function getExposureModes()
    {
        return $this->exposureModes;
    }

    /**
     * Sets a new exposureModes
     *
     * Available settings to control shutter speed and lens aperture.
     *
     * @param string $exposureModes
     * @return self
     */
    public function setExposureModes(array $exposureModes)
    {
        $this->exposureModes = $exposureModes;
        return $this;
    }

    /**
     * Gets as displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @return string
     */
    public function getDisplayTechnology()
    {
        return $this->displayTechnology;
    }

    /**
     * Sets a new displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @param string $displayTechnology
     * @return self
     */
    public function setDisplayTechnology($displayTechnology)
    {
        $this->displayTechnology = $displayTechnology;
        return $this;
    }

    /**
     * Gets as displayResolution
     *
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @return \WalmartDSV\DisplayResolutionType
     */
    public function getDisplayResolution()
    {
        return $this->displayResolution;
    }

    /**
     * Sets a new displayResolution
     *
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @param \WalmartDSV\DisplayResolutionType $displayResolution
     * @return self
     */
    public function setDisplayResolution(\WalmartDSV\DisplayResolutionType $displayResolution)
    {
        $this->displayResolution = $displayResolution;
        return $this;
    }

    /**
     * Gets as screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @return \WalmartDSV\CamerasAndLensesType\ScreenSizeAType
     */
    public function getScreenSize()
    {
        return $this->screenSize;
    }

    /**
     * Sets a new screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @param \WalmartDSV\CamerasAndLensesType\ScreenSizeAType $screenSize
     * @return self
     */
    public function setScreenSize(\WalmartDSV\CamerasAndLensesType\ScreenSizeAType $screenSize)
    {
        $this->screenSize = $screenSize;
        return $this;
    }

    /**
     * Gets as diameter
     *
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @return \WalmartDSV\CamerasAndLensesType\DiameterAType
     */
    public function getDiameter()
    {
        return $this->diameter;
    }

    /**
     * Sets a new diameter
     *
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @param \WalmartDSV\CamerasAndLensesType\DiameterAType $diameter
     * @return self
     */
    public function setDiameter(\WalmartDSV\CamerasAndLensesType\DiameterAType $diameter)
    {
        $this->diameter = $diameter;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\CamerasAndLensesType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\CamerasAndLensesType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\CamerasAndLensesType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\CamerasAndLensesType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\CamerasAndLensesType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\CamerasAndLensesType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\CamerasAndLensesType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\CamerasAndLensesType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\CamerasAndLensesType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\CamerasAndLensesType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\CamerasAndLensesType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\CamerasAndLensesType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @return string
     */
    public function getIsAssemblyRequired()
    {
        return $this->isAssemblyRequired;
    }

    /**
     * Sets a new isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @param string $isAssemblyRequired
     * @return self
     */
    public function setIsAssemblyRequired($isAssemblyRequired)
    {
        $this->isAssemblyRequired = $isAssemblyRequired;
        return $this;
    }

    /**
     * Gets as assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @return string
     */
    public function getAssemblyInstructions()
    {
        return $this->assemblyInstructions;
    }

    /**
     * Sets a new assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @param string $assemblyInstructions
     * @return self
     */
    public function setAssemblyInstructions($assemblyInstructions)
    {
        $this->assemblyInstructions = $assemblyInstructions;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Adds as accessoriesIncludedValue
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return self
     * @param string $accessoriesIncludedValue
     */
    public function addToAccessoriesIncluded($accessoriesIncludedValue)
    {
        $this->accessoriesIncluded[] = $accessoriesIncludedValue;
        return $this;
    }

    /**
     * isset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAccessoriesIncluded($index)
    {
        return isset($this->accessoriesIncluded[$index]);
    }

    /**
     * unset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAccessoriesIncluded($index)
    {
        unset($this->accessoriesIncluded[$index]);
    }

    /**
     * Gets as accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return string[]
     */
    public function getAccessoriesIncluded()
    {
        return $this->accessoriesIncluded;
    }

    /**
     * Sets a new accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param string $accessoriesIncluded
     * @return self
     */
    public function setAccessoriesIncluded(array $accessoriesIncluded)
    {
        $this->accessoriesIncluded = $accessoriesIncluded;
        return $this;
    }

    /**
     * Gets as hasTouchscreen
     *
     * Does the display have touchscreen capabilities?
     *
     * @return string
     */
    public function getHasTouchscreen()
    {
        return $this->hasTouchscreen;
    }

    /**
     * Sets a new hasTouchscreen
     *
     * Does the display have touchscreen capabilities?
     *
     * @param string $hasTouchscreen
     * @return self
     */
    public function setHasTouchscreen($hasTouchscreen)
    {
        $this->hasTouchscreen = $hasTouchscreen;
        return $this;
    }

    /**
     * Gets as hasMemoryCardSlot
     *
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @return string
     */
    public function getHasMemoryCardSlot()
    {
        return $this->hasMemoryCardSlot;
    }

    /**
     * Sets a new hasMemoryCardSlot
     *
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @param string $hasMemoryCardSlot
     * @return self
     */
    public function setHasMemoryCardSlot($hasMemoryCardSlot)
    {
        $this->hasMemoryCardSlot = $hasMemoryCardSlot;
        return $this;
    }

    /**
     * Adds as memoryCardTypeValue
     *
     * The memory card format applicable to the product.
     *
     * @return self
     * @param string $memoryCardTypeValue
     */
    public function addToMemoryCardType($memoryCardTypeValue)
    {
        $this->memoryCardType[] = $memoryCardTypeValue;
        return $this;
    }

    /**
     * isset memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetMemoryCardType($index)
    {
        return isset($this->memoryCardType[$index]);
    }

    /**
     * unset memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetMemoryCardType($index)
    {
        unset($this->memoryCardType[$index]);
    }

    /**
     * Gets as memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @return string[]
     */
    public function getMemoryCardType()
    {
        return $this->memoryCardType;
    }

    /**
     * Sets a new memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param string $memoryCardType
     * @return self
     */
    public function setMemoryCardType(array $memoryCardType)
    {
        $this->memoryCardType = $memoryCardType;
        return $this;
    }

    /**
     * Adds as recordableMediaFormat
     *
     * The recording technologies compatible with the item.
     *
     * @return self
     * @param string $recordableMediaFormat
     */
    public function addToRecordableMediaFormats($recordableMediaFormat)
    {
        $this->recordableMediaFormats[] = $recordableMediaFormat;
        return $this;
    }

    /**
     * isset recordableMediaFormats
     *
     * The recording technologies compatible with the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecordableMediaFormats($index)
    {
        return isset($this->recordableMediaFormats[$index]);
    }

    /**
     * unset recordableMediaFormats
     *
     * The recording technologies compatible with the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecordableMediaFormats($index)
    {
        unset($this->recordableMediaFormats[$index]);
    }

    /**
     * Gets as recordableMediaFormats
     *
     * The recording technologies compatible with the item.
     *
     * @return string[]
     */
    public function getRecordableMediaFormats()
    {
        return $this->recordableMediaFormats;
    }

    /**
     * Sets a new recordableMediaFormats
     *
     * The recording technologies compatible with the item.
     *
     * @param string $recordableMediaFormats
     * @return self
     */
    public function setRecordableMediaFormats(array $recordableMediaFormats)
    {
        $this->recordableMediaFormats = $recordableMediaFormats;
        return $this;
    }

    /**
     * Adds as wirelessTechnology
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return self
     * @param string $wirelessTechnology
     */
    public function addToWirelessTechnologies($wirelessTechnology)
    {
        $this->wirelessTechnologies[] = $wirelessTechnology;
        return $this;
    }

    /**
     * isset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetWirelessTechnologies($index)
    {
        return isset($this->wirelessTechnologies[$index]);
    }

    /**
     * unset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetWirelessTechnologies($index)
    {
        unset($this->wirelessTechnologies[$index]);
    }

    /**
     * Gets as wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return string[]
     */
    public function getWirelessTechnologies()
    {
        return $this->wirelessTechnologies;
    }

    /**
     * Sets a new wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param string $wirelessTechnologies
     * @return self
     */
    public function setWirelessTechnologies(array $wirelessTechnologies)
    {
        $this->wirelessTechnologies = $wirelessTechnologies;
        return $this;
    }

    /**
     * Adds as connection
     *
     * The standardized connections provided on the item.
     *
     * @return self
     * @param string $connection
     */
    public function addToConnections($connection)
    {
        $this->connections[] = $connection;
        return $this;
    }

    /**
     * isset connections
     *
     * The standardized connections provided on the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetConnections($index)
    {
        return isset($this->connections[$index]);
    }

    /**
     * unset connections
     *
     * The standardized connections provided on the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetConnections($index)
    {
        unset($this->connections[$index]);
    }

    /**
     * Gets as connections
     *
     * The standardized connections provided on the item.
     *
     * @return string[]
     */
    public function getConnections()
    {
        return $this->connections;
    }

    /**
     * Sets a new connections
     *
     * The standardized connections provided on the item.
     *
     * @param string $connections
     * @return self
     */
    public function setConnections(array $connections)
    {
        $this->connections = $connections;
        return $this;
    }

    /**
     * Gets as isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @return string
     */
    public function getIsPortable()
    {
        return $this->isPortable;
    }

    /**
     * Sets a new isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @param string $isPortable
     * @return self
     */
    public function setIsPortable($isPortable)
    {
        $this->isPortable = $isPortable;
        return $this;
    }

    /**
     * Gets as isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @return string
     */
    public function getIsWaterproof()
    {
        return $this->isWaterproof;
    }

    /**
     * Sets a new isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @param string $isWaterproof
     * @return self
     */
    public function setIsWaterproof($isWaterproof)
    {
        $this->isWaterproof = $isWaterproof;
        return $this;
    }

    /**
     * Gets as hasSelfTimer
     *
     * Y indicates the device has a feature that allows a countdown, or a timed delay before taking an action, such as taking a photo.
     *
     * @return string
     */
    public function getHasSelfTimer()
    {
        return $this->hasSelfTimer;
    }

    /**
     * Sets a new hasSelfTimer
     *
     * Y indicates the device has a feature that allows a countdown, or a timed delay before taking an action, such as taking a photo.
     *
     * @param string $hasSelfTimer
     * @return self
     */
    public function setHasSelfTimer($hasSelfTimer)
    {
        $this->hasSelfTimer = $hasSelfTimer;
        return $this;
    }

    /**
     * Adds as selfTimerDelayValue
     *
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @param \WalmartDSV\TimeUnitType $selfTimerDelayValue
     *@return self
     */
    public function addToSelfTimerDelay(\WalmartDSV\TimeUnitType $selfTimerDelayValue)
    {
        $this->selfTimerDelay[] = $selfTimerDelayValue;
        return $this;
    }

    /**
     * isset selfTimerDelay
     *
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSelfTimerDelay($index)
    {
        return isset($this->selfTimerDelay[$index]);
    }

    /**
     * unset selfTimerDelay
     *
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSelfTimerDelay($index)
    {
        unset($this->selfTimerDelay[$index]);
    }

    /**
     * Gets as selfTimerDelay
     *
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @return \WalmartDSV\TimeUnitType[]
     */
    public function getSelfTimerDelay()
    {
        return $this->selfTimerDelay;
    }

    /**
     * Sets a new selfTimerDelay
     *
     * Length of time the self-timer will allow before it takes an action, such as taking a photo.
     *
     * @param \WalmartDSV\TimeUnitType[] $selfTimerDelay
     * @return self
     */
    public function setSelfTimerDelay(array $selfTimerDelay)
    {
        $this->selfTimerDelay = $selfTimerDelay;
        return $this;
    }

    /**
     * Gets as standbyTime
     *
     * The number of continuous hours the device may remain in standby mode before shutting down.
     *
     * @return \WalmartDSV\CamerasAndLensesType\StandbyTimeAType
     */
    public function getStandbyTime()
    {
        return $this->standbyTime;
    }

    /**
     * Sets a new standbyTime
     *
     * The number of continuous hours the device may remain in standby mode before shutting down.
     *
     * @param \WalmartDSV\CamerasAndLensesType\StandbyTimeAType $standbyTime
     * @return self
     */
    public function setStandbyTime(\WalmartDSV\CamerasAndLensesType\StandbyTimeAType $standbyTime)
    {
        $this->standbyTime = $standbyTime;
        return $this;
    }

    /**
     * Gets as fieldOfView
     *
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @return string
     */
    public function getFieldOfView()
    {
        return $this->fieldOfView;
    }

    /**
     * Sets a new fieldOfView
     *
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @param string $fieldOfView
     * @return self
     */
    public function setFieldOfView($fieldOfView)
    {
        $this->fieldOfView = $fieldOfView;
        return $this;
    }

    /**
     * Gets as isParfocal
     *
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @return string
     */
    public function getIsParfocal()
    {
        return $this->isParfocal;
    }

    /**
     * Sets a new isParfocal
     *
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @param string $isParfocal
     * @return self
     */
    public function setIsParfocal($isParfocal)
    {
        $this->isParfocal = $isParfocal;
        return $this;
    }

    /**
     * Gets as shootingMode
     *
     * Available settings designed to accommodate different photographic situations.
     *
     * @return string
     */
    public function getShootingMode()
    {
        return $this->shootingMode;
    }

    /**
     * Sets a new shootingMode
     *
     * Available settings designed to accommodate different photographic situations.
     *
     * @param string $shootingMode
     * @return self
     */
    public function setShootingMode($shootingMode)
    {
        $this->shootingMode = $shootingMode;
        return $this;
    }

    /**
     * Gets as microphoneIncluded
     *
     * Y indicates the item contains a microphone to record sound, either internally or externally.
     *
     * @return string
     */
    public function getMicrophoneIncluded()
    {
        return $this->microphoneIncluded;
    }

    /**
     * Sets a new microphoneIncluded
     *
     * Y indicates the item contains a microphone to record sound, either internally or externally.
     *
     * @param string $microphoneIncluded
     * @return self
     */
    public function setMicrophoneIncluded($microphoneIncluded)
    {
        $this->microphoneIncluded = $microphoneIncluded;
        return $this;
    }

    /**
     * Gets as hasHandle
     *
     * Y indicates the item contains a grip to make the item easier to hold, carry, or control.
     *
     * @return string
     */
    public function getHasHandle()
    {
        return $this->hasHandle;
    }

    /**
     * Sets a new hasHandle
     *
     * Y indicates the item contains a grip to make the item easier to hold, carry, or control.
     *
     * @param string $hasHandle
     * @return self
     */
    public function setHasHandle($hasHandle)
    {
        $this->hasHandle = $hasHandle;
        return $this;
    }

    /**
     * Gets as isMulticoated
     *
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @return string
     */
    public function getIsMulticoated()
    {
        return $this->isMulticoated;
    }

    /**
     * Sets a new isMulticoated
     *
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @param string $isMulticoated
     * @return self
     */
    public function setIsMulticoated($isMulticoated)
    {
        $this->isMulticoated = $isMulticoated;
        return $this;
    }

    /**
     * Gets as hasRedEyeReduction
     *
     * Y indicates the item has a feature to reduce the appearance of red pupils in photos due to the red-eye effect.
     *
     * @return string
     */
    public function getHasRedEyeReduction()
    {
        return $this->hasRedEyeReduction;
    }

    /**
     * Sets a new hasRedEyeReduction
     *
     * Y indicates the item has a feature to reduce the appearance of red pupils in photos due to the red-eye effect.
     *
     * @param string $hasRedEyeReduction
     * @return self
     */
    public function setHasRedEyeReduction($hasRedEyeReduction)
    {
        $this->hasRedEyeReduction = $hasRedEyeReduction;
        return $this;
    }

    /**
     * Gets as hasNightVision
     *
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @return string
     */
    public function getHasNightVision()
    {
        return $this->hasNightVision;
    }

    /**
     * Sets a new hasNightVision
     *
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @param string $hasNightVision
     * @return self
     */
    public function setHasNightVision($hasNightVision)
    {
        $this->hasNightVision = $hasNightVision;
        return $this;
    }

    /**
     * Gets as isFogResistant
     *
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @return string
     */
    public function getIsFogResistant()
    {
        return $this->isFogResistant;
    }

    /**
     * Sets a new isFogResistant
     *
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @param string $isFogResistant
     * @return self
     */
    public function setIsFogResistant($isFogResistant)
    {
        $this->isFogResistant = $isFogResistant;
        return $this;
    }

    /**
     * Gets as attachmentStyle
     *
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @return string
     */
    public function getAttachmentStyle()
    {
        return $this->attachmentStyle;
    }

    /**
     * Sets a new attachmentStyle
     *
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @param string $attachmentStyle
     * @return self
     */
    public function setAttachmentStyle($attachmentStyle)
    {
        $this->attachmentStyle = $attachmentStyle;
        return $this;
    }

    /**
     * Gets as hasShoulderStrap
     *
     * Y indicates the item comes with a strap that allows a user to suspend the item from the shoulders.
     *
     * @return string
     */
    public function getHasShoulderStrap()
    {
        return $this->hasShoulderStrap;
    }

    /**
     * Sets a new hasShoulderStrap
     *
     * Y indicates the item comes with a strap that allows a user to suspend the item from the shoulders.
     *
     * @param string $hasShoulderStrap
     * @return self
     */
    public function setHasShoulderStrap($hasShoulderStrap)
    {
        $this->hasShoulderStrap = $hasShoulderStrap;
        return $this;
    }

    /**
     * Adds as compatibleBrand
     *
     * A list of the brands most commonly compatible with the item.
     *
     * @return self
     * @param string $compatibleBrand
     */
    public function addToCompatibleBrands($compatibleBrand)
    {
        $this->compatibleBrands[] = $compatibleBrand;
        return $this;
    }

    /**
     * isset compatibleBrands
     *
     * A list of the brands most commonly compatible with the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCompatibleBrands($index)
    {
        return isset($this->compatibleBrands[$index]);
    }

    /**
     * unset compatibleBrands
     *
     * A list of the brands most commonly compatible with the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCompatibleBrands($index)
    {
        unset($this->compatibleBrands[$index]);
    }

    /**
     * Gets as compatibleBrands
     *
     * A list of the brands most commonly compatible with the item.
     *
     * @return string[]
     */
    public function getCompatibleBrands()
    {
        return $this->compatibleBrands;
    }

    /**
     * Sets a new compatibleBrands
     *
     * A list of the brands most commonly compatible with the item.
     *
     * @param string $compatibleBrands
     * @return self
     */
    public function setCompatibleBrands(array $compatibleBrands)
    {
        $this->compatibleBrands = $compatibleBrands;
        return $this;
    }

    /**
     * Gets as compatibleDevices
     *
     * A list of the devices compatible with the item.
     *
     * @return \WalmartDSV\CompatibleDevicesType
     */
    public function getCompatibleDevices()
    {
        return $this->compatibleDevices;
    }

    /**
     * Sets a new compatibleDevices
     *
     * A list of the devices compatible with the item.
     *
     * @param \WalmartDSV\CompatibleDevicesType $compatibleDevices
     * @return self
     */
    public function setCompatibleDevices(\WalmartDSV\CompatibleDevicesType $compatibleDevices)
    {
        $this->compatibleDevices = $compatibleDevices;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @return string
     */
    public function getCleaningCareAndMaintenance()
    {
        return $this->cleaningCareAndMaintenance;
    }

    /**
     * Sets a new cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @param string $cleaningCareAndMaintenance
     * @return self
     */
    public function setCleaningCareAndMaintenance($cleaningCareAndMaintenance)
    {
        $this->cleaningCareAndMaintenance = $cleaningCareAndMaintenance;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\CamerasAndLensesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

