<?php

namespace WalmartDSV;

/**
 * Class representing BooksAndMagazinesType
 *
 *
 * XSD Type: BooksAndMagazines
 */
class BooksAndMagazinesType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * A summary of narrative content.
     *
     * @var string $synopsis
     */
    private $synopsis = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @var string $title
     */
    private $title = null;

    /**
     * Formats specific to books. If the book format is "eBook," also use the Digital File Format attribute to capture file type (exe, pdf, zip, etc). If the book format is "Audiobook," also use "Digital Audio File Format" (for mp3, aiff, etc). If your audio or eBook will not be delivered online (perhaps it will come on an audio CD, USB stick, etc.), fill in the Physical Media Format attribute to capture that information.
     *
     * @var string $bookFormat
     */
    private $bookFormat = null;

    /**
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @var string[] $author
     */
    private $author = null;

    /**
     * Publishing company as printed or displayed on the cover or title page.
     *
     * @var string $publisher
     */
    private $publisher = null;

    /**
     * Date of publication for the current edition, in the format yyyy-mm-dd.
     *
     * @var \DateTime $publicationDate
     */
    private $publicationDate = null;

    /**
     * Date that a printed work was first published in the format yyyy-mm-dd, if different from the publication date of the current edition. If current edition is the original publication, leave this blank.
     *
     * @var \DateTime $originalPublicationDate
     */
    private $originalPublicationDate = null;

    /**
     * The demographic for which the item is targeted.
     *
     * @var string[] $targetAudience
     */
    private $targetAudience = null;

    /**
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @var string[] $awardsWon
     */
    private $awardsWon = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * Basic category of literature, typically in the form of books. For example, fiction books are stories created by an author’s imagination and types of fiction include mystery, science fiction, romance. Non-fiction books present real events, people, and places and include reference books, cookbooks, and biographies.
     *
     * @var string $fictionNonfiction
     */
    private $fictionNonfiction = null;

    /**
     * The general book or magazine category.
     *
     * @var string $genre
     */
    private $genre = null;

    /**
     * The more specific book or magazine subcategory.
     *
     * @var string $subgenre
     */
    private $subgenre = null;

    /**
     * The "aboutness" of an item, distinct from the genre. It may be the subject of a documentary, nonfiction book, or art print.
     *
     * @var string $subject
     */
    private $subject = null;

    /**
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @var string $seriesTitle
     */
    private $seriesTitle = null;

    /**
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @var int $numberInSeries
     */
    private $numberInSeries = null;

    /**
     * For an ongoing serial publication, the specific issue, as named by the publication, usually either month and year, or issue and volume number.
     *
     * @var string $issue
     */
    private $issue = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\BooksAndMagazinesType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\BooksAndMagazinesType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\BooksAndMagazinesType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\BooksAndMagazinesType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @var int[] $smallPartsWarnings
     */
    private $smallPartsWarnings = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @var string $isAdultProduct
     */
    private $isAdultProduct = null;

    /**
     * The specific edition of the item.
     *
     * @var string $edition
     */
    private $edition = null;

    /**
     * Number of discs included in the item.
     *
     * @var int $numberOfDiscs
     */
    private $numberOfDiscs = null;

    /**
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @var string[] $originalLanguages
     */
    private $originalLanguages = null;

    /**
     * Number of pages within a work. May refer to numbered pages, for a pre-printed book, or blank pages, as in a notebook or journal.
     *
     * @var int $numberOfPages
     */
    private $numberOfPages = null;

    /**
     * Indicates that the item is the complete original work with no omissions.
     *
     * @var string $isUnabridged
     */
    private $isUnabridged = null;

    /**
     * Indicates that the item has been especially printed in a large font to accommodate those with special eyesight needs.
     *
     * @var string $isLargePrint
     */
    private $isLargePrint = null;

    /**
     * The intended age or grade-level for a published work.
     *
     * @var string $readingLevel
     */
    private $readingLevel = null;

    /**
     * The person or entity responsible for choosing the collection of stories or articles in a book or magazine, as printed on the title page, or a magazine masthead
     *
     * @var string $editor
     */
    private $editor = null;

    /**
     * The person credited with translating the book from the original language into the language of the current edition.
     *
     * @var string $translator
     */
    private $translator = null;

    /**
     * The original language that a work was translated from.
     *
     * @var string $translatedFrom
     */
    private $translatedFrom = null;

    /**
     * The person credited with drawing illustrations within a printed work
     *
     * @var string $illustrator
     */
    private $illustrator = null;

    /**
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @var string[] $bisacSubjectCodes
     */
    private $bisacSubjectCodes = null;

    /**
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsLeagueType $sportsLeague
     */
    private $sportsLeague = null;

    /**
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsTeamType $sportsTeam
     */
    private $sportsTeam = null;

    /**
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @var \WalmartDSV\AthleteType $athlete
     */
    private $athlete = null;

    /**
     * The full name of the person who has autographed this copy.
     *
     * @var string $autographedBy
     */
    private $autographedBy = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * @var \WalmartDSV\BooksAndMagazinesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as synopsis
     *
     * A summary of narrative content.
     *
     * @return string
     */
    public function getSynopsis()
    {
        return $this->synopsis;
    }

    /**
     * Sets a new synopsis
     *
     * A summary of narrative content.
     *
     * @param string $synopsis
     * @return self
     */
    public function setSynopsis($synopsis)
    {
        $this->synopsis = $synopsis;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as title
     *
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Sets a new title
     *
     * The name given to the work. Does not include any marketing adjectives outside of the given name.
     *
     * @param string $title
     * @return self
     */
    public function setTitle($title)
    {
        $this->title = $title;
        return $this;
    }

    /**
     * Gets as bookFormat
     *
     * Formats specific to books. If the book format is "eBook," also use the Digital File Format attribute to capture file type (exe, pdf, zip, etc). If the book format is "Audiobook," also use "Digital Audio File Format" (for mp3, aiff, etc). If your audio or eBook will not be delivered online (perhaps it will come on an audio CD, USB stick, etc.), fill in the Physical Media Format attribute to capture that information.
     *
     * @return string
     */
    public function getBookFormat()
    {
        return $this->bookFormat;
    }

    /**
     * Sets a new bookFormat
     *
     * Formats specific to books. If the book format is "eBook," also use the Digital File Format attribute to capture file type (exe, pdf, zip, etc). If the book format is "Audiobook," also use "Digital Audio File Format" (for mp3, aiff, etc). If your audio or eBook will not be delivered online (perhaps it will come on an audio CD, USB stick, etc.), fill in the Physical Media Format attribute to capture that information.
     *
     * @param string $bookFormat
     * @return self
     */
    public function setBookFormat($bookFormat)
    {
        $this->bookFormat = $bookFormat;
        return $this;
    }

    /**
     * Adds as authorValue
     *
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @return self
     * @param string $authorValue
     */
    public function addToAuthor($authorValue)
    {
        $this->author[] = $authorValue;
        return $this;
    }

    /**
     * isset author
     *
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAuthor($index)
    {
        return isset($this->author[$index]);
    }

    /**
     * unset author
     *
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAuthor($index)
    {
        unset($this->author[$index]);
    }

    /**
     * Gets as author
     *
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @return string[]
     */
    public function getAuthor()
    {
        return $this->author;
    }

    /**
     * Sets a new author
     *
     * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
     *
     * @param string $author
     * @return self
     */
    public function setAuthor(array $author)
    {
        $this->author = $author;
        return $this;
    }

    /**
     * Gets as publisher
     *
     * Publishing company as printed or displayed on the cover or title page.
     *
     * @return string
     */
    public function getPublisher()
    {
        return $this->publisher;
    }

    /**
     * Sets a new publisher
     *
     * Publishing company as printed or displayed on the cover or title page.
     *
     * @param string $publisher
     * @return self
     */
    public function setPublisher($publisher)
    {
        $this->publisher = $publisher;
        return $this;
    }

    /**
     * Gets as publicationDate
     *
     * Date of publication for the current edition, in the format yyyy-mm-dd.
     *
     * @return \DateTime
     */
    public function getPublicationDate()
    {
        return $this->publicationDate;
    }

    /**
     * Sets a new publicationDate
     *
     * Date of publication for the current edition, in the format yyyy-mm-dd.
     *
     * @param \DateTime $publicationDate
     * @return self
     */
    public function setPublicationDate(\DateTime $publicationDate)
    {
        $this->publicationDate = $publicationDate;
        return $this;
    }

    /**
     * Gets as originalPublicationDate
     *
     * Date that a printed work was first published in the format yyyy-mm-dd, if different from the publication date of the current edition. If current edition is the original publication, leave this blank.
     *
     * @return \DateTime
     */
    public function getOriginalPublicationDate()
    {
        return $this->originalPublicationDate;
    }

    /**
     * Sets a new originalPublicationDate
     *
     * Date that a printed work was first published in the format yyyy-mm-dd, if different from the publication date of the current edition. If current edition is the original publication, leave this blank.
     *
     * @param \DateTime $originalPublicationDate
     * @return self
     */
    public function setOriginalPublicationDate(\DateTime $originalPublicationDate)
    {
        $this->originalPublicationDate = $originalPublicationDate;
        return $this;
    }

    /**
     * Adds as targetAudienceValue
     *
     * The demographic for which the item is targeted.
     *
     * @return self
     * @param string $targetAudienceValue
     */
    public function addToTargetAudience($targetAudienceValue)
    {
        $this->targetAudience[] = $targetAudienceValue;
        return $this;
    }

    /**
     * isset targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetTargetAudience($index)
    {
        return isset($this->targetAudience[$index]);
    }

    /**
     * unset targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetTargetAudience($index)
    {
        unset($this->targetAudience[$index]);
    }

    /**
     * Gets as targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @return string[]
     */
    public function getTargetAudience()
    {
        return $this->targetAudience;
    }

    /**
     * Sets a new targetAudience
     *
     * The demographic for which the item is targeted.
     *
     * @param string $targetAudience
     * @return self
     */
    public function setTargetAudience(array $targetAudience)
    {
        $this->targetAudience = $targetAudience;
        return $this;
    }

    /**
     * Adds as awardsWonValue
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @return self
     * @param string $awardsWonValue
     */
    public function addToAwardsWon($awardsWonValue)
    {
        $this->awardsWon[] = $awardsWonValue;
        return $this;
    }

    /**
     * isset awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAwardsWon($index)
    {
        return isset($this->awardsWon[$index]);
    }

    /**
     * unset awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAwardsWon($index)
    {
        unset($this->awardsWon[$index]);
    }

    /**
     * Gets as awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @return string[]
     */
    public function getAwardsWon()
    {
        return $this->awardsWon;
    }

    /**
     * Sets a new awardsWon
     *
     * Use this attribute if the item has won any awards in its particular product category.
     *
     * @param string $awardsWon
     * @return self
     */
    public function setAwardsWon(array $awardsWon)
    {
        $this->awardsWon = $awardsWon;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Gets as fictionNonfiction
     *
     * Basic category of literature, typically in the form of books. For example, fiction books are stories created by an author’s imagination and types of fiction include mystery, science fiction, romance. Non-fiction books present real events, people, and places and include reference books, cookbooks, and biographies.
     *
     * @return string
     */
    public function getFictionNonfiction()
    {
        return $this->fictionNonfiction;
    }

    /**
     * Sets a new fictionNonfiction
     *
     * Basic category of literature, typically in the form of books. For example, fiction books are stories created by an author’s imagination and types of fiction include mystery, science fiction, romance. Non-fiction books present real events, people, and places and include reference books, cookbooks, and biographies.
     *
     * @param string $fictionNonfiction
     * @return self
     */
    public function setFictionNonfiction($fictionNonfiction)
    {
        $this->fictionNonfiction = $fictionNonfiction;
        return $this;
    }

    /**
     * Gets as genre
     *
     * The general book or magazine category.
     *
     * @return string
     */
    public function getGenre()
    {
        return $this->genre;
    }

    /**
     * Sets a new genre
     *
     * The general book or magazine category.
     *
     * @param string $genre
     * @return self
     */
    public function setGenre($genre)
    {
        $this->genre = $genre;
        return $this;
    }

    /**
     * Gets as subgenre
     *
     * The more specific book or magazine subcategory.
     *
     * @return string
     */
    public function getSubgenre()
    {
        return $this->subgenre;
    }

    /**
     * Sets a new subgenre
     *
     * The more specific book or magazine subcategory.
     *
     * @param string $subgenre
     * @return self
     */
    public function setSubgenre($subgenre)
    {
        $this->subgenre = $subgenre;
        return $this;
    }

    /**
     * Gets as subject
     *
     * The "aboutness" of an item, distinct from the genre. It may be the subject of a documentary, nonfiction book, or art print.
     *
     * @return string
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * Sets a new subject
     *
     * The "aboutness" of an item, distinct from the genre. It may be the subject of a documentary, nonfiction book, or art print.
     *
     * @param string $subject
     * @return self
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;
        return $this;
    }

    /**
     * Gets as seriesTitle
     *
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @return string
     */
    public function getSeriesTitle()
    {
        return $this->seriesTitle;
    }

    /**
     * Sets a new seriesTitle
     *
     * If the work is one of multiple works in a series, the title of the series or collection.
     *
     * @param string $seriesTitle
     * @return self
     */
    public function setSeriesTitle($seriesTitle)
    {
        $this->seriesTitle = $seriesTitle;
        return $this;
    }

    /**
     * Gets as numberInSeries
     *
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @return int
     */
    public function getNumberInSeries()
    {
        return $this->numberInSeries;
    }

    /**
     * Sets a new numberInSeries
     *
     * The number in the series, if the work is one of multiple works in a series.
     *
     * @param int $numberInSeries
     * @return self
     */
    public function setNumberInSeries($numberInSeries)
    {
        $this->numberInSeries = $numberInSeries;
        return $this;
    }

    /**
     * Gets as issue
     *
     * For an ongoing serial publication, the specific issue, as named by the publication, usually either month and year, or issue and volume number.
     *
     * @return string
     */
    public function getIssue()
    {
        return $this->issue;
    }

    /**
     * Sets a new issue
     *
     * For an ongoing serial publication, the specific issue, as named by the publication, usually either month and year, or issue and volume number.
     *
     * @param string $issue
     * @return self
     */
    public function setIssue($issue)
    {
        $this->issue = $issue;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\BooksAndMagazinesType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\BooksAndMagazinesType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\BooksAndMagazinesType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\BooksAndMagazinesType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\BooksAndMagazinesType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\BooksAndMagazinesType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\BooksAndMagazinesType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\BooksAndMagazinesType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\BooksAndMagazinesType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\BooksAndMagazinesType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\BooksAndMagazinesType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\BooksAndMagazinesType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Adds as smallPartsWarning
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarnings($smallPartsWarning)
    {
        $this->smallPartsWarnings[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarnings($index)
    {
        return isset($this->smallPartsWarnings[$index]);
    }

    /**
     * unset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarnings($index)
    {
        unset($this->smallPartsWarnings[$index]);
    }

    /**
     * Gets as smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return int[]
     */
    public function getSmallPartsWarnings()
    {
        return $this->smallPartsWarnings;
    }

    /**
     * Sets a new smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int $smallPartsWarnings
     * @return self
     */
    public function setSmallPartsWarnings(array $smallPartsWarnings)
    {
        $this->smallPartsWarnings = $smallPartsWarnings;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as isAdultProduct
     *
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @return string
     */
    public function getIsAdultProduct()
    {
        return $this->isAdultProduct;
    }

    /**
     * Sets a new isAdultProduct
     *
     * Indicates if item is adult in nature and should not appear in results for children's products.
     *
     * @param string $isAdultProduct
     * @return self
     */
    public function setIsAdultProduct($isAdultProduct)
    {
        $this->isAdultProduct = $isAdultProduct;
        return $this;
    }

    /**
     * Gets as edition
     *
     * The specific edition of the item.
     *
     * @return string
     */
    public function getEdition()
    {
        return $this->edition;
    }

    /**
     * Sets a new edition
     *
     * The specific edition of the item.
     *
     * @param string $edition
     * @return self
     */
    public function setEdition($edition)
    {
        $this->edition = $edition;
        return $this;
    }

    /**
     * Gets as numberOfDiscs
     *
     * Number of discs included in the item.
     *
     * @return int
     */
    public function getNumberOfDiscs()
    {
        return $this->numberOfDiscs;
    }

    /**
     * Sets a new numberOfDiscs
     *
     * Number of discs included in the item.
     *
     * @param int $numberOfDiscs
     * @return self
     */
    public function setNumberOfDiscs($numberOfDiscs)
    {
        $this->numberOfDiscs = $numberOfDiscs;
        return $this;
    }

    /**
     * Adds as originalLanguage
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @return self
     * @param string $originalLanguage
     */
    public function addToOriginalLanguages($originalLanguage)
    {
        $this->originalLanguages[] = $originalLanguage;
        return $this;
    }

    /**
     * isset originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOriginalLanguages($index)
    {
        return isset($this->originalLanguages[$index]);
    }

    /**
     * unset originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOriginalLanguages($index)
    {
        unset($this->originalLanguages[$index]);
    }

    /**
     * Gets as originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @return string[]
     */
    public function getOriginalLanguages()
    {
        return $this->originalLanguages;
    }

    /**
     * Sets a new originalLanguages
     *
     * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
     *
     * @param string $originalLanguages
     * @return self
     */
    public function setOriginalLanguages(array $originalLanguages)
    {
        $this->originalLanguages = $originalLanguages;
        return $this;
    }

    /**
     * Gets as numberOfPages
     *
     * Number of pages within a work. May refer to numbered pages, for a pre-printed book, or blank pages, as in a notebook or journal.
     *
     * @return int
     */
    public function getNumberOfPages()
    {
        return $this->numberOfPages;
    }

    /**
     * Sets a new numberOfPages
     *
     * Number of pages within a work. May refer to numbered pages, for a pre-printed book, or blank pages, as in a notebook or journal.
     *
     * @param int $numberOfPages
     * @return self
     */
    public function setNumberOfPages($numberOfPages)
    {
        $this->numberOfPages = $numberOfPages;
        return $this;
    }

    /**
     * Gets as isUnabridged
     *
     * Indicates that the item is the complete original work with no omissions.
     *
     * @return string
     */
    public function getIsUnabridged()
    {
        return $this->isUnabridged;
    }

    /**
     * Sets a new isUnabridged
     *
     * Indicates that the item is the complete original work with no omissions.
     *
     * @param string $isUnabridged
     * @return self
     */
    public function setIsUnabridged($isUnabridged)
    {
        $this->isUnabridged = $isUnabridged;
        return $this;
    }

    /**
     * Gets as isLargePrint
     *
     * Indicates that the item has been especially printed in a large font to accommodate those with special eyesight needs.
     *
     * @return string
     */
    public function getIsLargePrint()
    {
        return $this->isLargePrint;
    }

    /**
     * Sets a new isLargePrint
     *
     * Indicates that the item has been especially printed in a large font to accommodate those with special eyesight needs.
     *
     * @param string $isLargePrint
     * @return self
     */
    public function setIsLargePrint($isLargePrint)
    {
        $this->isLargePrint = $isLargePrint;
        return $this;
    }

    /**
     * Gets as readingLevel
     *
     * The intended age or grade-level for a published work.
     *
     * @return string
     */
    public function getReadingLevel()
    {
        return $this->readingLevel;
    }

    /**
     * Sets a new readingLevel
     *
     * The intended age or grade-level for a published work.
     *
     * @param string $readingLevel
     * @return self
     */
    public function setReadingLevel($readingLevel)
    {
        $this->readingLevel = $readingLevel;
        return $this;
    }

    /**
     * Gets as editor
     *
     * The person or entity responsible for choosing the collection of stories or articles in a book or magazine, as printed on the title page, or a magazine masthead
     *
     * @return string
     */
    public function getEditor()
    {
        return $this->editor;
    }

    /**
     * Sets a new editor
     *
     * The person or entity responsible for choosing the collection of stories or articles in a book or magazine, as printed on the title page, or a magazine masthead
     *
     * @param string $editor
     * @return self
     */
    public function setEditor($editor)
    {
        $this->editor = $editor;
        return $this;
    }

    /**
     * Gets as translator
     *
     * The person credited with translating the book from the original language into the language of the current edition.
     *
     * @return string
     */
    public function getTranslator()
    {
        return $this->translator;
    }

    /**
     * Sets a new translator
     *
     * The person credited with translating the book from the original language into the language of the current edition.
     *
     * @param string $translator
     * @return self
     */
    public function setTranslator($translator)
    {
        $this->translator = $translator;
        return $this;
    }

    /**
     * Gets as translatedFrom
     *
     * The original language that a work was translated from.
     *
     * @return string
     */
    public function getTranslatedFrom()
    {
        return $this->translatedFrom;
    }

    /**
     * Sets a new translatedFrom
     *
     * The original language that a work was translated from.
     *
     * @param string $translatedFrom
     * @return self
     */
    public function setTranslatedFrom($translatedFrom)
    {
        $this->translatedFrom = $translatedFrom;
        return $this;
    }

    /**
     * Gets as illustrator
     *
     * The person credited with drawing illustrations within a printed work
     *
     * @return string
     */
    public function getIllustrator()
    {
        return $this->illustrator;
    }

    /**
     * Sets a new illustrator
     *
     * The person credited with drawing illustrations within a printed work
     *
     * @param string $illustrator
     * @return self
     */
    public function setIllustrator($illustrator)
    {
        $this->illustrator = $illustrator;
        return $this;
    }

    /**
     * Adds as bisacSubjectCode
     *
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @return self
     * @param string $bisacSubjectCode
     */
    public function addToBisacSubjectCodes($bisacSubjectCode)
    {
        $this->bisacSubjectCodes[] = $bisacSubjectCode;
        return $this;
    }

    /**
     * isset bisacSubjectCodes
     *
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBisacSubjectCodes($index)
    {
        return isset($this->bisacSubjectCodes[$index]);
    }

    /**
     * unset bisacSubjectCodes
     *
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBisacSubjectCodes($index)
    {
        unset($this->bisacSubjectCodes[$index]);
    }

    /**
     * Gets as bisacSubjectCodes
     *
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @return string[]
     */
    public function getBisacSubjectCodes()
    {
        return $this->bisacSubjectCodes;
    }

    /**
     * Sets a new bisacSubjectCodes
     *
     * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
     *
     * @param string $bisacSubjectCodes
     * @return self
     */
    public function setBisacSubjectCodes(array $bisacSubjectCodes)
    {
        $this->bisacSubjectCodes = $bisacSubjectCodes;
        return $this;
    }

    /**
     * Gets as sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsLeagueType
     */
    public function getSportsLeague()
    {
        return $this->sportsLeague;
    }

    /**
     * Sets a new sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsLeagueType $sportsLeague
     * @return self
     */
    public function setSportsLeague(\WalmartDSV\SportsLeagueType $sportsLeague)
    {
        $this->sportsLeague = $sportsLeague;
        return $this;
    }

    /**
     * Gets as sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsTeamType
     */
    public function getSportsTeam()
    {
        return $this->sportsTeam;
    }

    /**
     * Sets a new sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsTeamType $sportsTeam
     * @return self
     */
    public function setSportsTeam(\WalmartDSV\SportsTeamType $sportsTeam)
    {
        $this->sportsTeam = $sportsTeam;
        return $this;
    }

    /**
     * Gets as athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @return \WalmartDSV\AthleteType
     */
    public function getAthlete()
    {
        return $this->athlete;
    }

    /**
     * Sets a new athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @param \WalmartDSV\AthleteType $athlete
     * @return self
     */
    public function setAthlete(\WalmartDSV\AthleteType $athlete)
    {
        $this->athlete = $athlete;
        return $this;
    }

    /**
     * Gets as autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @return string
     */
    public function getAutographedBy()
    {
        return $this->autographedBy;
    }

    /**
     * Sets a new autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @param string $autographedBy
     * @return self
     */
    public function setAutographedBy($autographedBy)
    {
        $this->autographedBy = $autographedBy;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\BooksAndMagazinesType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\BooksAndMagazinesType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\BooksAndMagazinesType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\BooksAndMagazinesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

