<?php

namespace WalmartDSV;

/**
 * Class representing HatStyleType
 *
 * Styles specific to hats.
 * XSD Type: HatStyle
 */
class HatStyleType
{

    /**
     * @var string $hatStyleValue
     */
    private $hatStyleValue = null;

    /**
     * Gets as hatStyleValue
     *
     * @return string
     */
    public function getHatStyleValue()
    {
        return $this->hatStyleValue;
    }

    /**
     * Sets a new hatStyleValue
     *
     * @param string $hatStyleValue
     * @return self
     */
    public function setHatStyleValue($hatStyleValue)
    {
        $this->hatStyleValue = $hatStyleValue;
        return $this;
    }


}

