<?php

namespace WalmartDSV;

/**
 * Class representing SweaterStyleType
 *
 * Styles specific to sweaters.
 * XSD Type: SweaterStyle
 */
class SweaterStyleType
{

    /**
     * @var string $sweaterStyleValue
     */
    private $sweaterStyleValue = null;

    /**
     * Gets as sweaterStyleValue
     *
     * @return string
     */
    public function getSweaterStyleValue()
    {
        return $this->sweaterStyleValue;
    }

    /**
     * Sets a new sweaterStyleValue
     *
     * @param string $sweaterStyleValue
     * @return self
     */
    public function setSweaterStyleValue($sweaterStyleValue)
    {
        $this->sweaterStyleValue = $sweaterStyleValue;
        return $this;
    }


}

