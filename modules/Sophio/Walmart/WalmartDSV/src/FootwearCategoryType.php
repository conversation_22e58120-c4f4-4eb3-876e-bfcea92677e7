<?php

namespace WalmartDSV;

/**
 * Class representing FootwearCategoryType
 *
 *
 * XSD Type: FootwearCategory
 */
class FootwearCategoryType
{

    /**
     * @var \WalmartDSV\FootwearType $footwear
     */
    private $footwear = null;

    /**
     * Gets as footwear
     *
     * @return \WalmartDSV\FootwearType
     */
    public function getFootwear()
    {
        return $this->footwear;
    }

    /**
     * Sets a new footwear
     *
     * @param \WalmartDSV\FootwearType $footwear
     * @return self
     */
    public function setFootwear(\WalmartDSV\FootwearType $footwear)
    {
        $this->footwear = $footwear;
        return $this;
    }


}

