<?php

namespace WalmartDSV;

/**
 * Class representing StorageType
 *
 *
 * XSD Type: Storage
 */
class StorageType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @var string $inflexKitComponent
     */
    private $inflexKitComponent = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @var string $finish
     */
    private $finish = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @var string $shape
     */
    private $shape = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * The rooms where the item is likely or recommended to be used.
     *
     * @var string[] $recommendedRooms
     */
    private $recommendedRooms = null;

    /**
     * The primary location recommended for the item's use.
     *
     * @var string[] $recommendedLocations
     */
    private $recommendedLocations = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\StorageType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\StorageType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\StorageType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\StorageType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @var int[] $smallPartsWarnings
     */
    private $smallPartsWarnings = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @var int $compositeWoodCertificationCode
     */
    private $compositeWoodCertificationCode = null;

    /**
     * Is product unassembled and must be put together before use?
     *
     * @var string $isAssemblyRequired
     */
    private $isAssemblyRequired = null;

    /**
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @var string $assemblyInstructions
     */
    private $assemblyInstructions = null;

    /**
     * Material makeup of the item.
     *
     * @var \WalmartDSV\FabricContentValueType[] $fabricContent
     */
    private $fabricContent = null;

    /**
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @var string[] $fabricCareInstructions
     */
    private $fabricCareInstructions = null;

    /**
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @var string $collection
     */
    private $collection = null;

    /**
     * The number of shelves included in the furniture or storage unit.
     *
     * @var int $numberOfShelves
     */
    private $numberOfShelves = null;

    /**
     * Describes the structure or style of a shelving unit.
     *
     * @var string $shelfStyle
     */
    private $shelfStyle = null;

    /**
     * Depth of shelf in inches.
     *
     * @var \WalmartDSV\StorageType\ShelfDepthAType $shelfDepth
     */
    private $shelfDepth = null;

    /**
     * The number of drawers included the furniture or storage unit.
     *
     * @var int $numberOfDrawers
     */
    private $numberOfDrawers = null;

    /**
     * The location of the drawer in relation to the storage unit.
     *
     * @var string $drawerPosition
     */
    private $drawerPosition = null;

    /**
     * The height, width and depth measurements of drawers contained in this item, given in the format H" x W" x D".
     *
     * @var string $drawerDimensions
     */
    private $drawerDimensions = null;

    /**
     * A product's available space. Capacity is often provided for items that contain multiple pieces of something or that can accommodate some number of objects.
     *
     * @var string $capacity
     */
    private $capacity = null;

    /**
     * The upper weight limit or capability of an item, often used in conjunction with "Minimum Weight". The meaning varies with context of product. For example, when used with "Minimum Weight", this attribute provides weight ranges for a range of products including pet medicine, baby carriers and outdoor play structures.
     *
     * @var \WalmartDSV\StorageType\MaximumWeightAType $maximumWeight
     */
    private $maximumWeight = null;

    /**
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @var string[] $recommendedUses
     */
    private $recommendedUses = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Indicates that an item can be folded.
     *
     * @var string $isFoldable
     */
    private $isFoldable = null;

    /**
     * Indicates that the item can be withdrawn into a holder, as in a cord or leash.
     *
     * @var string $isRetractable
     */
    private $isRetractable = null;

    /**
     * Is the item designed to be easily moved?
     *
     * @var string $isPortable
     */
    private $isPortable = null;

    /**
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @var string $isIndustrial
     */
    private $isIndustrial = null;

    /**
     * Name of the standard for a collection of units of measure that is used in for item. If appropriate, more than one measurement system can be entered. Example: Metric, and SEA for a set of wrenches that has both metric sockets and SEA sockets.
     *
     * @var string $systemOfMeasurement
     */
    private $systemOfMeasurement = null;

    /**
     * @var \WalmartDSV\StorageType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as inflexKitComponent
     *
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @return string
     */
    public function getInflexKitComponent()
    {
        return $this->inflexKitComponent;
    }

    /**
     * Sets a new inflexKitComponent
     *
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @param string $inflexKitComponent
     * @return self
     */
    public function setInflexKitComponent($inflexKitComponent)
    {
        $this->inflexKitComponent = $inflexKitComponent;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @return string
     */
    public function getFinish()
    {
        return $this->finish;
    }

    /**
     * Sets a new finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @param string $finish
     * @return self
     */
    public function setFinish($finish)
    {
        $this->finish = $finish;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Gets as shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @return string
     */
    public function getShape()
    {
        return $this->shape;
    }

    /**
     * Sets a new shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @param string $shape
     * @return self
     */
    public function setShape($shape)
    {
        $this->shape = $shape;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Adds as recommendedRoom
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @return self
     * @param string $recommendedRoom
     */
    public function addToRecommendedRooms($recommendedRoom)
    {
        $this->recommendedRooms[] = $recommendedRoom;
        return $this;
    }

    /**
     * isset recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedRooms($index)
    {
        return isset($this->recommendedRooms[$index]);
    }

    /**
     * unset recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedRooms($index)
    {
        unset($this->recommendedRooms[$index]);
    }

    /**
     * Gets as recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @return string[]
     */
    public function getRecommendedRooms()
    {
        return $this->recommendedRooms;
    }

    /**
     * Sets a new recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param string $recommendedRooms
     * @return self
     */
    public function setRecommendedRooms(array $recommendedRooms)
    {
        $this->recommendedRooms = $recommendedRooms;
        return $this;
    }

    /**
     * Adds as recommendedLocation
     *
     * The primary location recommended for the item's use.
     *
     * @return self
     * @param string $recommendedLocation
     */
    public function addToRecommendedLocations($recommendedLocation)
    {
        $this->recommendedLocations[] = $recommendedLocation;
        return $this;
    }

    /**
     * isset recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedLocations($index)
    {
        return isset($this->recommendedLocations[$index]);
    }

    /**
     * unset recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedLocations($index)
    {
        unset($this->recommendedLocations[$index]);
    }

    /**
     * Gets as recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @return string[]
     */
    public function getRecommendedLocations()
    {
        return $this->recommendedLocations;
    }

    /**
     * Sets a new recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param string $recommendedLocations
     * @return self
     */
    public function setRecommendedLocations(array $recommendedLocations)
    {
        $this->recommendedLocations = $recommendedLocations;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\StorageType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\StorageType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\StorageType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\StorageType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\StorageType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\StorageType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\StorageType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\StorageType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\StorageType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\StorageType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\StorageType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\StorageType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Adds as smallPartsWarning
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarnings($smallPartsWarning)
    {
        $this->smallPartsWarnings[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarnings($index)
    {
        return isset($this->smallPartsWarnings[$index]);
    }

    /**
     * unset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarnings($index)
    {
        unset($this->smallPartsWarnings[$index]);
    }

    /**
     * Gets as smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return int[]
     */
    public function getSmallPartsWarnings()
    {
        return $this->smallPartsWarnings;
    }

    /**
     * Sets a new smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int $smallPartsWarnings
     * @return self
     */
    public function setSmallPartsWarnings(array $smallPartsWarnings)
    {
        $this->smallPartsWarnings = $smallPartsWarnings;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @return int
     */
    public function getCompositeWoodCertificationCode()
    {
        return $this->compositeWoodCertificationCode;
    }

    /**
     * Sets a new compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @param int $compositeWoodCertificationCode
     * @return self
     */
    public function setCompositeWoodCertificationCode($compositeWoodCertificationCode)
    {
        $this->compositeWoodCertificationCode = $compositeWoodCertificationCode;
        return $this;
    }

    /**
     * Gets as isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @return string
     */
    public function getIsAssemblyRequired()
    {
        return $this->isAssemblyRequired;
    }

    /**
     * Sets a new isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @param string $isAssemblyRequired
     * @return self
     */
    public function setIsAssemblyRequired($isAssemblyRequired)
    {
        $this->isAssemblyRequired = $isAssemblyRequired;
        return $this;
    }

    /**
     * Gets as assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @return string
     */
    public function getAssemblyInstructions()
    {
        return $this->assemblyInstructions;
    }

    /**
     * Sets a new assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @param string $assemblyInstructions
     * @return self
     */
    public function setAssemblyInstructions($assemblyInstructions)
    {
        $this->assemblyInstructions = $assemblyInstructions;
        return $this;
    }

    /**
     * Adds as fabricContentValue
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType $fabricContentValue
     *@return self
     */
    public function addToFabricContent(\WalmartDSV\FabricContentValueType $fabricContentValue)
    {
        $this->fabricContent[] = $fabricContentValue;
        return $this;
    }

    /**
     * isset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricContent($index)
    {
        return isset($this->fabricContent[$index]);
    }

    /**
     * unset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricContent($index)
    {
        unset($this->fabricContent[$index]);
    }

    /**
     * Gets as fabricContent
     *
     * Material makeup of the item.
     *
     * @return \WalmartDSV\FabricContentValueType[]
     */
    public function getFabricContent()
    {
        return $this->fabricContent;
    }

    /**
     * Sets a new fabricContent
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType[] $fabricContent
     * @return self
     */
    public function setFabricContent(array $fabricContent)
    {
        $this->fabricContent = $fabricContent;
        return $this;
    }

    /**
     * Adds as fabricCareInstruction
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return self
     * @param string $fabricCareInstruction
     */
    public function addToFabricCareInstructions($fabricCareInstruction)
    {
        $this->fabricCareInstructions[] = $fabricCareInstruction;
        return $this;
    }

    /**
     * isset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricCareInstructions($index)
    {
        return isset($this->fabricCareInstructions[$index]);
    }

    /**
     * unset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricCareInstructions($index)
    {
        unset($this->fabricCareInstructions[$index]);
    }

    /**
     * Gets as fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return string[]
     */
    public function getFabricCareInstructions()
    {
        return $this->fabricCareInstructions;
    }

    /**
     * Sets a new fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param string $fabricCareInstructions
     * @return self
     */
    public function setFabricCareInstructions(array $fabricCareInstructions)
    {
        $this->fabricCareInstructions = $fabricCareInstructions;
        return $this;
    }

    /**
     * Gets as collection
     *
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @return string
     */
    public function getCollection()
    {
        return $this->collection;
    }

    /**
     * Sets a new collection
     *
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @param string $collection
     * @return self
     */
    public function setCollection($collection)
    {
        $this->collection = $collection;
        return $this;
    }

    /**
     * Gets as numberOfShelves
     *
     * The number of shelves included in the furniture or storage unit.
     *
     * @return int
     */
    public function getNumberOfShelves()
    {
        return $this->numberOfShelves;
    }

    /**
     * Sets a new numberOfShelves
     *
     * The number of shelves included in the furniture or storage unit.
     *
     * @param int $numberOfShelves
     * @return self
     */
    public function setNumberOfShelves($numberOfShelves)
    {
        $this->numberOfShelves = $numberOfShelves;
        return $this;
    }

    /**
     * Gets as shelfStyle
     *
     * Describes the structure or style of a shelving unit.
     *
     * @return string
     */
    public function getShelfStyle()
    {
        return $this->shelfStyle;
    }

    /**
     * Sets a new shelfStyle
     *
     * Describes the structure or style of a shelving unit.
     *
     * @param string $shelfStyle
     * @return self
     */
    public function setShelfStyle($shelfStyle)
    {
        $this->shelfStyle = $shelfStyle;
        return $this;
    }

    /**
     * Gets as shelfDepth
     *
     * Depth of shelf in inches.
     *
     * @return \WalmartDSV\StorageType\ShelfDepthAType
     */
    public function getShelfDepth()
    {
        return $this->shelfDepth;
    }

    /**
     * Sets a new shelfDepth
     *
     * Depth of shelf in inches.
     *
     * @param \WalmartDSV\StorageType\ShelfDepthAType $shelfDepth
     * @return self
     */
    public function setShelfDepth(\WalmartDSV\StorageType\ShelfDepthAType $shelfDepth)
    {
        $this->shelfDepth = $shelfDepth;
        return $this;
    }

    /**
     * Gets as numberOfDrawers
     *
     * The number of drawers included the furniture or storage unit.
     *
     * @return int
     */
    public function getNumberOfDrawers()
    {
        return $this->numberOfDrawers;
    }

    /**
     * Sets a new numberOfDrawers
     *
     * The number of drawers included the furniture or storage unit.
     *
     * @param int $numberOfDrawers
     * @return self
     */
    public function setNumberOfDrawers($numberOfDrawers)
    {
        $this->numberOfDrawers = $numberOfDrawers;
        return $this;
    }

    /**
     * Gets as drawerPosition
     *
     * The location of the drawer in relation to the storage unit.
     *
     * @return string
     */
    public function getDrawerPosition()
    {
        return $this->drawerPosition;
    }

    /**
     * Sets a new drawerPosition
     *
     * The location of the drawer in relation to the storage unit.
     *
     * @param string $drawerPosition
     * @return self
     */
    public function setDrawerPosition($drawerPosition)
    {
        $this->drawerPosition = $drawerPosition;
        return $this;
    }

    /**
     * Gets as drawerDimensions
     *
     * The height, width and depth measurements of drawers contained in this item, given in the format H" x W" x D".
     *
     * @return string
     */
    public function getDrawerDimensions()
    {
        return $this->drawerDimensions;
    }

    /**
     * Sets a new drawerDimensions
     *
     * The height, width and depth measurements of drawers contained in this item, given in the format H" x W" x D".
     *
     * @param string $drawerDimensions
     * @return self
     */
    public function setDrawerDimensions($drawerDimensions)
    {
        $this->drawerDimensions = $drawerDimensions;
        return $this;
    }

    /**
     * Gets as capacity
     *
     * A product's available space. Capacity is often provided for items that contain multiple pieces of something or that can accommodate some number of objects.
     *
     * @return string
     */
    public function getCapacity()
    {
        return $this->capacity;
    }

    /**
     * Sets a new capacity
     *
     * A product's available space. Capacity is often provided for items that contain multiple pieces of something or that can accommodate some number of objects.
     *
     * @param string $capacity
     * @return self
     */
    public function setCapacity($capacity)
    {
        $this->capacity = $capacity;
        return $this;
    }

    /**
     * Gets as maximumWeight
     *
     * The upper weight limit or capability of an item, often used in conjunction with "Minimum Weight". The meaning varies with context of product. For example, when used with "Minimum Weight", this attribute provides weight ranges for a range of products including pet medicine, baby carriers and outdoor play structures.
     *
     * @return \WalmartDSV\StorageType\MaximumWeightAType
     */
    public function getMaximumWeight()
    {
        return $this->maximumWeight;
    }

    /**
     * Sets a new maximumWeight
     *
     * The upper weight limit or capability of an item, often used in conjunction with "Minimum Weight". The meaning varies with context of product. For example, when used with "Minimum Weight", this attribute provides weight ranges for a range of products including pet medicine, baby carriers and outdoor play structures.
     *
     * @param \WalmartDSV\StorageType\MaximumWeightAType $maximumWeight
     * @return self
     */
    public function setMaximumWeight(\WalmartDSV\StorageType\MaximumWeightAType $maximumWeight)
    {
        $this->maximumWeight = $maximumWeight;
        return $this;
    }

    /**
     * Adds as recommendedUse
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUses($recommendedUse)
    {
        $this->recommendedUses[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUses($index)
    {
        return isset($this->recommendedUses[$index]);
    }

    /**
     * unset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUses($index)
    {
        unset($this->recommendedUses[$index]);
    }

    /**
     * Gets as recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return string[]
     */
    public function getRecommendedUses()
    {
        return $this->recommendedUses;
    }

    /**
     * Sets a new recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param string $recommendedUses
     * @return self
     */
    public function setRecommendedUses(array $recommendedUses)
    {
        $this->recommendedUses = $recommendedUses;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as isFoldable
     *
     * Indicates that an item can be folded.
     *
     * @return string
     */
    public function getIsFoldable()
    {
        return $this->isFoldable;
    }

    /**
     * Sets a new isFoldable
     *
     * Indicates that an item can be folded.
     *
     * @param string $isFoldable
     * @return self
     */
    public function setIsFoldable($isFoldable)
    {
        $this->isFoldable = $isFoldable;
        return $this;
    }

    /**
     * Gets as isRetractable
     *
     * Indicates that the item can be withdrawn into a holder, as in a cord or leash.
     *
     * @return string
     */
    public function getIsRetractable()
    {
        return $this->isRetractable;
    }

    /**
     * Sets a new isRetractable
     *
     * Indicates that the item can be withdrawn into a holder, as in a cord or leash.
     *
     * @param string $isRetractable
     * @return self
     */
    public function setIsRetractable($isRetractable)
    {
        $this->isRetractable = $isRetractable;
        return $this;
    }

    /**
     * Gets as isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @return string
     */
    public function getIsPortable()
    {
        return $this->isPortable;
    }

    /**
     * Sets a new isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @param string $isPortable
     * @return self
     */
    public function setIsPortable($isPortable)
    {
        $this->isPortable = $isPortable;
        return $this;
    }

    /**
     * Gets as isIndustrial
     *
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @return string
     */
    public function getIsIndustrial()
    {
        return $this->isIndustrial;
    }

    /**
     * Sets a new isIndustrial
     *
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @param string $isIndustrial
     * @return self
     */
    public function setIsIndustrial($isIndustrial)
    {
        $this->isIndustrial = $isIndustrial;
        return $this;
    }

    /**
     * Gets as systemOfMeasurement
     *
     * Name of the standard for a collection of units of measure that is used in for item. If appropriate, more than one measurement system can be entered. Example: Metric, and SEA for a set of wrenches that has both metric sockets and SEA sockets.
     *
     * @return string
     */
    public function getSystemOfMeasurement()
    {
        return $this->systemOfMeasurement;
    }

    /**
     * Sets a new systemOfMeasurement
     *
     * Name of the standard for a collection of units of measure that is used in for item. If appropriate, more than one measurement system can be entered. Example: Metric, and SEA for a set of wrenches that has both metric sockets and SEA sockets.
     *
     * @param string $systemOfMeasurement
     * @return self
     */
    public function setSystemOfMeasurement($systemOfMeasurement)
    {
        $this->systemOfMeasurement = $systemOfMeasurement;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\StorageType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\StorageType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\StorageType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\StorageType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

