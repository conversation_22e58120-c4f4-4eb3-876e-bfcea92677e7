<?php

namespace WalmartDSV;

/**
 * Class representing RecommendedUsesType
 *
 * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
 * XSD Type: RecommendedUses
 */
class RecommendedUsesType
{

    /**
     * @var string[] $recommendedUse
     */
    private $recommendedUse = [
        
    ];

    /**
     * Adds as recommendedUse
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUse($recommendedUse)
    {
        $this->recommendedUse[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUse
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUse($index)
    {
        return isset($this->recommendedUse[$index]);
    }

    /**
     * unset recommendedUse
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUse($index)
    {
        unset($this->recommendedUse[$index]);
    }

    /**
     * Gets as recommendedUse
     *
     * @return string[]
     */
    public function getRecommendedUse()
    {
        return $this->recommendedUse;
    }

    /**
     * Sets a new recommendedUse
     *
     * @param string $recommendedUse
     * @return self
     */
    public function setRecommendedUse(array $recommendedUse)
    {
        $this->recommendedUse = $recommendedUse;
        return $this;
    }


}

