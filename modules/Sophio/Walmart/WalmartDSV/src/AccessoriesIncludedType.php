<?php

namespace WalmartDSV;

/**
 * Class representing AccessoriesIncludedType
 *
 * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
 * XSD Type: AccessoriesIncluded
 */
class AccessoriesIncludedType
{

    /**
     * @var string[] $accessoriesIncludedValue
     */
    private $accessoriesIncludedValue = [
        
    ];

    /**
     * Adds as accessoriesIncludedValue
     *
     * @return self
     * @param string $accessoriesIncludedValue
     */
    public function addToAccessoriesIncludedValue($accessoriesIncludedValue)
    {
        $this->accessoriesIncludedValue[] = $accessoriesIncludedValue;
        return $this;
    }

    /**
     * isset accessoriesIncludedValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAccessoriesIncludedValue($index)
    {
        return isset($this->accessoriesIncludedValue[$index]);
    }

    /**
     * unset accessoriesIncludedValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAccessoriesIncludedValue($index)
    {
        unset($this->accessoriesIncludedValue[$index]);
    }

    /**
     * Gets as accessoriesIncludedValue
     *
     * @return string[]
     */
    public function getAccessoriesIncludedValue()
    {
        return $this->accessoriesIncludedValue;
    }

    /**
     * Sets a new accessoriesIncludedValue
     *
     * @param string $accessoriesIncludedValue
     * @return self
     */
    public function setAccessoriesIncludedValue(array $accessoriesIncludedValue)
    {
        $this->accessoriesIncludedValue = $accessoriesIncludedValue;
        return $this;
    }


}

