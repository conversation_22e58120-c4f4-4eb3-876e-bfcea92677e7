<?php

namespace WalmartDSV;

/**
 * Class representing TargetAudienceType
 *
 * The demographic for which the item is targeted.
 * XSD Type: TargetAudience
 */
class TargetAudienceType
{

    /**
     * @var string[] $targetAudienceValue
     */
    private $targetAudienceValue = [
        
    ];

    /**
     * Adds as targetAudienceValue
     *
     * @return self
     * @param string $targetAudienceValue
     */
    public function addToTargetAudienceValue($targetAudienceValue)
    {
        $this->targetAudienceValue[] = $targetAudienceValue;
        return $this;
    }

    /**
     * isset targetAudienceValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetTargetAudienceValue($index)
    {
        return isset($this->targetAudienceValue[$index]);
    }

    /**
     * unset targetAudienceValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetTargetAudienceValue($index)
    {
        unset($this->targetAudienceValue[$index]);
    }

    /**
     * Gets as targetAudienceValue
     *
     * @return string[]
     */
    public function getTargetAudienceValue()
    {
        return $this->targetAudienceValue;
    }

    /**
     * Sets a new targetAudienceValue
     *
     * @param string $targetAudienceValue
     * @return self
     */
    public function setTargetAudienceValue(array $targetAudienceValue)
    {
        $this->targetAudienceValue = $targetAudienceValue;
        return $this;
    }


}

