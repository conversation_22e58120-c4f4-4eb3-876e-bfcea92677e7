<?php

namespace WalmartDSV;

/**
 * Class representing PantFitType
 *
 * Terms that describe the way pants will fit when worn.
 * XSD Type: PantFit
 */
class PantFitType
{

    /**
     * @var string $pantFitValue
     */
    private $pantFitValue = null;

    /**
     * Gets as pantFitValue
     *
     * @return string
     */
    public function getPantFitValue()
    {
        return $this->pantFitValue;
    }

    /**
     * Sets a new pantFitValue
     *
     * @param string $pantFitValue
     * @return self
     */
    public function setPantFitValue($pantFitValue)
    {
        $this->pantFitValue = $pantFitValue;
        return $this;
    }


}

