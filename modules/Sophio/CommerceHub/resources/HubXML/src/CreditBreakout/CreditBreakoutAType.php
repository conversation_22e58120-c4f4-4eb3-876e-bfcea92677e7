<?php

namespace CommerceHub\HubXML\CreditBreakout;

/**
 * Class representing CreditBreakoutAType
 */
class CreditBreakoutAType
{
    /**
     * Code indicating the sub-classification of the breakout amount
     *
     * @var string $creditType
     */
    private $creditType = null;

    /**
     * @var string $paymentMethod
     */
    private $paymentMethod = null;

    /**
     * @var string $paymentName
     */
    private $paymentName = null;

    /**
     * @var string $accountNumber
     */
    private $accountNumber = null;

    /**
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @var string $currencyUnit
     */
    private $currencyUnit = null;

    /**
     * Gets as creditType
     *
     * Code indicating the sub-classification of the breakout amount
     *
     * @return string
     */
    public function getCreditType()
    {
        return $this->creditType;
    }

    /**
     * Sets a new creditType
     *
     * Code indicating the sub-classification of the breakout amount
     *
     * @param string $creditType
     * @return self
     */
    public function setCreditType($creditType)
    {
        $this->creditType = $creditType;
        return $this;
    }

    /**
     * Gets as paymentMethod
     *
     * @return string
     */
    public function getPaymentMethod()
    {
        return $this->paymentMethod;
    }

    /**
     * Sets a new paymentMethod
     *
     * @param string $paymentMethod
     * @return self
     */
    public function setPaymentMethod($paymentMethod)
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    /**
     * Gets as paymentName
     *
     * @return string
     */
    public function getPaymentName()
    {
        return $this->paymentName;
    }

    /**
     * Sets a new paymentName
     *
     * @param string $paymentName
     * @return self
     */
    public function setPaymentName($paymentName)
    {
        $this->paymentName = $paymentName;
        return $this;
    }

    /**
     * Gets as accountNumber
     *
     * @return string
     */
    public function getAccountNumber()
    {
        return $this->accountNumber;
    }

    /**
     * Sets a new accountNumber
     *
     * @param string $accountNumber
     * @return self
     */
    public function setAccountNumber($accountNumber)
    {
        $this->accountNumber = $accountNumber;
        return $this;
    }

    /**
     * Gets as currencyUnit
     *
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @return string
     */
    public function getCurrencyUnit()
    {
        return $this->currencyUnit;
    }

    /**
     * Sets a new currencyUnit
     *
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @param string $currencyUnit
     * @return self
     */
    public function setCurrencyUnit($currencyUnit)
    {
        $this->currencyUnit = $currencyUnit;
        return $this;
    }
}

