<?php

namespace CommerceHub\HubXML\FAMessageBatch\FAMessageBatchAType\HubFAAType\MessageAckAType;

/**
 * Class representing DetailExceptionAType
 */
class DetailExceptionAType
{
    /**
     * ID of entry in an exception log
     *
     * @var string $detailID
     */
    private $detailID = null;

    /**
     * Free form text explanation of syntactical exceptions or violations
     *
     * @var string[] $exceptionDesc
     */
    private $exceptionDesc = [
        
    ];

    /**
     * Gets as detailID
     *
     * ID of entry in an exception log
     *
     * @return string
     */
    public function getDetailID()
    {
        return $this->detailID;
    }

    /**
     * Sets a new detailID
     *
     * ID of entry in an exception log
     *
     * @param string $detailID
     * @return self
     */
    public function setDetailID($detailID)
    {
        $this->detailID = $detailID;
        return $this;
    }

    /**
     * Adds as exceptionDesc
     *
     * Free form text explanation of syntactical exceptions or violations
     *
     * @return self
     * @param string $exceptionDesc
     */
    public function addToExceptionDesc($exceptionDesc)
    {
        $this->exceptionDesc[] = $exceptionDesc;
        return $this;
    }

    /**
     * isset exceptionDesc
     *
     * Free form text explanation of syntactical exceptions or violations
     *
     * @param int|string $index
     * @return bool
     */
    public function issetExceptionDesc($index)
    {
        return isset($this->exceptionDesc[$index]);
    }

    /**
     * unset exceptionDesc
     *
     * Free form text explanation of syntactical exceptions or violations
     *
     * @param int|string $index
     * @return void
     */
    public function unsetExceptionDesc($index)
    {
        unset($this->exceptionDesc[$index]);
    }

    /**
     * Gets as exceptionDesc
     *
     * Free form text explanation of syntactical exceptions or violations
     *
     * @return string[]
     */
    public function getExceptionDesc()
    {
        return $this->exceptionDesc;
    }

    /**
     * Sets a new exceptionDesc
     *
     * Free form text explanation of syntactical exceptions or violations
     *
     * @param string[] $exceptionDesc
     * @return self
     */
    public function setExceptionDesc(array $exceptionDesc = null)
    {
        $this->exceptionDesc = $exceptionDesc;
        return $this;
    }
}

