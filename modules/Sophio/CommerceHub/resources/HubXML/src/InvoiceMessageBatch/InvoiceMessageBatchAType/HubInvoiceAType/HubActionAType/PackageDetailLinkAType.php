<?php

namespace CommerceHub\HubXML\InvoiceMessageBatch\InvoiceMessageBatchAType\HubInvoiceAType\HubActionAType;

/**
 * Class representing PackageDetailLinkAType
 */
class PackageDetailLinkAType
{
    /**
     * @var string $__value
     */
    private $__value = null;

    /**
     * The value of this IDREF attribute must match the ID value in one of the packageDetail elements at the end of the message.
     *
     * @var string $packageDetailID
     */
    private $packageDetailID = null;

    /**
     * Construct
     *
     * @param string $value
     */
    public function __construct($value)
    {
        $this->value($value);
    }

    /**
     * Gets or sets the inner value
     *
     * @param string $value
     * @return string
     */
    public function value()
    {
        if ($args = func_get_args()) {
            $this->__value = $args[0];
        }
        return $this->__value;
    }

    /**
     * Gets a string value
     *
     * @return string
     */
    public function __toString()
    {
        return strval($this->__value);
    }

    /**
     * Gets as packageDetailID
     *
     * The value of this IDREF attribute must match the ID value in one of the packageDetail elements at the end of the message.
     *
     * @return string
     */
    public function getPackageDetailID()
    {
        return $this->packageDetailID;
    }

    /**
     * Sets a new packageDetailID
     *
     * The value of this IDREF attribute must match the ID value in one of the packageDetail elements at the end of the message.
     *
     * @param string $packageDetailID
     * @return self
     */
    public function setPackageDetailID($packageDetailID)
    {
        $this->packageDetailID = $packageDetailID;
        return $this;
    }
}

