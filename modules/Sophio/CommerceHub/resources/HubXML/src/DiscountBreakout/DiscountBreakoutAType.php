<?php

namespace CommerceHub\HubXML\DiscountBreakout;

/**
 * Class representing DiscountBreakoutAType
 */
class DiscountBreakoutAType
{
    /**
     * @var string $__value
     */
    private $__value = null;

    /**
     * Code for type of discount expected by Retailer
     *
     * @var string $discTypeCode
     */
    private $discTypeCode = null;

    /**
     * Code for type of date qualification for discount
     *
     * @var string $discDateCode
     */
    private $discDateCode = null;

    /**
     * @var string $discPercent
     */
    private $discPercent = null;

    /**
     * @var string $discDaysDue
     */
    private $discDaysDue = null;

    /**
     * @var string $netDaysDue
     */
    private $netDaysDue = null;

    /**
     * Construct
     *
     * @param string $value
     */
    public function __construct($value)
    {
        $this->value($value);
    }

    /**
     * Gets or sets the inner value
     *
     * @param string $value
     * @return string
     */
    public function value()
    {
        if ($args = func_get_args()) {
            $this->__value = $args[0];
        }
        return $this->__value;
    }

    /**
     * Gets a string value
     *
     * @return string
     */
    public function __toString()
    {
        return strval($this->__value);
    }

    /**
     * Gets as discTypeCode
     *
     * Code for type of discount expected by Retailer
     *
     * @return string
     */
    public function getDiscTypeCode()
    {
        return $this->discTypeCode;
    }

    /**
     * Sets a new discTypeCode
     *
     * Code for type of discount expected by Retailer
     *
     * @param string $discTypeCode
     * @return self
     */
    public function setDiscTypeCode($discTypeCode)
    {
        $this->discTypeCode = $discTypeCode;
        return $this;
    }

    /**
     * Gets as discDateCode
     *
     * Code for type of date qualification for discount
     *
     * @return string
     */
    public function getDiscDateCode()
    {
        return $this->discDateCode;
    }

    /**
     * Sets a new discDateCode
     *
     * Code for type of date qualification for discount
     *
     * @param string $discDateCode
     * @return self
     */
    public function setDiscDateCode($discDateCode)
    {
        $this->discDateCode = $discDateCode;
        return $this;
    }

    /**
     * Gets as discPercent
     *
     * @return string
     */
    public function getDiscPercent()
    {
        return $this->discPercent;
    }

    /**
     * Sets a new discPercent
     *
     * @param string $discPercent
     * @return self
     */
    public function setDiscPercent($discPercent)
    {
        $this->discPercent = $discPercent;
        return $this;
    }

    /**
     * Gets as discDaysDue
     *
     * @return string
     */
    public function getDiscDaysDue()
    {
        return $this->discDaysDue;
    }

    /**
     * Sets a new discDaysDue
     *
     * @param string $discDaysDue
     * @return self
     */
    public function setDiscDaysDue($discDaysDue)
    {
        $this->discDaysDue = $discDaysDue;
        return $this;
    }

    /**
     * Gets as netDaysDue
     *
     * @return string
     */
    public function getNetDaysDue()
    {
        return $this->netDaysDue;
    }

    /**
     * Sets a new netDaysDue
     *
     * @param string $netDaysDue
     * @return self
     */
    public function setNetDaysDue($netDaysDue)
    {
        $this->netDaysDue = $netDaysDue;
        return $this;
    }
}

