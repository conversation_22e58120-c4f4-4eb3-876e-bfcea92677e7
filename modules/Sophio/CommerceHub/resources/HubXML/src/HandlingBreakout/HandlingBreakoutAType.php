<?php

namespace CommerceHub\HubXML\HandlingBreakout;

/**
 * Class representing HandlingBreakoutAType
 */
class HandlingBreakoutAType
{
    /**
     * Code indicating the sub-classification of the breakout amount
     *
     * @var string $handlingType
     */
    private $handlingType = null;

    /**
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @var string $currencyUnit
     */
    private $currencyUnit = null;

    /**
     * Gets as handlingType
     *
     * Code indicating the sub-classification of the breakout amount
     *
     * @return string
     */
    public function getHandlingType()
    {
        return $this->handlingType;
    }

    /**
     * Sets a new handlingType
     *
     * Code indicating the sub-classification of the breakout amount
     *
     * @param string $handlingType
     * @return self
     */
    public function setHandlingType($handlingType)
    {
        $this->handlingType = $handlingType;
        return $this;
    }

    /**
     * Gets as currencyUnit
     *
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @return string
     */
    public function getCurrencyUnit()
    {
        return $this->currencyUnit;
    }

    /**
     * Sets a new currencyUnit
     *
     * Not used - currency is controlled by salesOrderCurrency element in poHdrData
     *
     * @param string $currencyUnit
     * @return self
     */
    public function setCurrencyUnit($currencyUnit)
    {
        $this->currencyUnit = $currencyUnit;
        return $this;
    }
}

