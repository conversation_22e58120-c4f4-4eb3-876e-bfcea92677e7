<?php

namespace CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType;

/**
 * Class representing PackageDetailAType
 */
class PackageDetailAType
{
    /**
     * The value of this ID attribute must uniquely identify the packageDetail element within the entire XML document. This value will be referenced by IDREF attributes on the packageDetailLink elements contained within the message. The value must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods
     *
     * @var string $packageDetailID
     */
    private $packageDetailID = null;

    /**
     * Date package was shipped.
     *
     * @var string $shipDate
     */
    private $shipDate = null;

    /**
     * Carrier and service level specific code.This field will contain a code that identifies the shipper and service level for the associated package. Contact CommerceHub to obtain a complete list of codes that apply to this field.
     *
     * @var string $serviceLevel1
     */
    private $serviceLevel1 = null;

    /**
     * Shipping carrier’s tracking number for this package.Invalid tracking numbers will fail to be processed. CommerceHub implements validations to ensure that the tracking numbers being passed structurally validate against the shipper and service level included in the message. Real tracking numbers should be used during the implementation to ensure that messages are processed.
     *
     * @var string $trackingNumber
     */
    private $trackingNumber = null;

    /**
     * Link to PersonPlace.
     *
     * @var \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipFromAType $shipFrom
     */
    private $shipFrom = null;

    /**
     * Additional data elements related to LTL service and/or serial numbers for products contained in package.
     *
     * @var \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipmentDataAType $shipmentData
     */
    private $shipmentData = null;

    /**
     * Gets as packageDetailID
     *
     * The value of this ID attribute must uniquely identify the packageDetail element within the entire XML document. This value will be referenced by IDREF attributes on the packageDetailLink elements contained within the message. The value must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods
     *
     * @return string
     */
    public function getPackageDetailID()
    {
        return $this->packageDetailID;
    }

    /**
     * Sets a new packageDetailID
     *
     * The value of this ID attribute must uniquely identify the packageDetail element within the entire XML document. This value will be referenced by IDREF attributes on the packageDetailLink elements contained within the message. The value must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods
     *
     * @param string $packageDetailID
     * @return self
     */
    public function setPackageDetailID($packageDetailID)
    {
        $this->packageDetailID = $packageDetailID;
        return $this;
    }

    /**
     * Gets as shipDate
     *
     * Date package was shipped.
     *
     * @return string
     */
    public function getShipDate()
    {
        return $this->shipDate;
    }

    /**
     * Sets a new shipDate
     *
     * Date package was shipped.
     *
     * @param string $shipDate
     * @return self
     */
    public function setShipDate($shipDate)
    {
        $this->shipDate = $shipDate;
        return $this;
    }

    /**
     * Gets as serviceLevel1
     *
     * Carrier and service level specific code.This field will contain a code that identifies the shipper and service level for the associated package. Contact CommerceHub to obtain a complete list of codes that apply to this field.
     *
     * @return string
     */
    public function getServiceLevel1()
    {
        return $this->serviceLevel1;
    }

    /**
     * Sets a new serviceLevel1
     *
     * Carrier and service level specific code.This field will contain a code that identifies the shipper and service level for the associated package. Contact CommerceHub to obtain a complete list of codes that apply to this field.
     *
     * @param string $serviceLevel1
     * @return self
     */
    public function setServiceLevel1($serviceLevel1)
    {
        $this->serviceLevel1 = $serviceLevel1;
        return $this;
    }

    /**
     * Gets as trackingNumber
     *
     * Shipping carrier’s tracking number for this package.Invalid tracking numbers will fail to be processed. CommerceHub implements validations to ensure that the tracking numbers being passed structurally validate against the shipper and service level included in the message. Real tracking numbers should be used during the implementation to ensure that messages are processed.
     *
     * @return string
     */
    public function getTrackingNumber()
    {
        return $this->trackingNumber;
    }

    /**
     * Sets a new trackingNumber
     *
     * Shipping carrier’s tracking number for this package.Invalid tracking numbers will fail to be processed. CommerceHub implements validations to ensure that the tracking numbers being passed structurally validate against the shipper and service level included in the message. Real tracking numbers should be used during the implementation to ensure that messages are processed.
     *
     * @param string $trackingNumber
     * @return self
     */
    public function setTrackingNumber($trackingNumber)
    {
        $this->trackingNumber = $trackingNumber;
        return $this;
    }

    /**
     * Gets as shipFrom
     *
     * Link to PersonPlace.
     *
     * @return \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipFromAType
     */
    public function getShipFrom()
    {
        return $this->shipFrom;
    }

    /**
     * Sets a new shipFrom
     *
     * Link to PersonPlace.
     *
     * @param \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipFromAType $shipFrom
     * @return self
     */
    public function setShipFrom(\CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipFromAType $shipFrom)
    {
        $this->shipFrom = $shipFrom;
        return $this;
    }

    /**
     * Gets as shipmentData
     *
     * Additional data elements related to LTL service and/or serial numbers for products contained in package.
     *
     * @return \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipmentDataAType
     */
    public function getShipmentData()
    {
        return $this->shipmentData;
    }

    /**
     * Sets a new shipmentData
     *
     * Additional data elements related to LTL service and/or serial numbers for products contained in package.
     *
     * @param \CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipmentDataAType $shipmentData
     * @return self
     */
    public function setShipmentData(?\CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType\ShipmentDataAType $shipmentData = null)
    {
        $this->shipmentData = $shipmentData;
        return $this;
    }
}

