<?php

namespace CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType;

/**
 * Class representing PartnerIDAType
 */
class PartnerIDAType
{
    /**
     * @var string $__value
     */
    private $__value = null;

    /**
     * Name of the party sending this batch of messages.
     *
     * @var string $name
     */
    private $name = null;

    /**
     * Role type (i.e. ‘vendor’) of partner sending this document
     *
     * @var string $roleType
     */
    private $roleType = null;

    /**
     * Construct
     *
     * @param string $value
     */
    public function __construct($value)
    {
        $this->value($value);
    }

    /**
     * Gets or sets the inner value
     *
     * @param string $value
     * @return string
     */
    public function value()
    {
        if ($args = func_get_args()) {
            $this->__value = $args[0];
        }
        return $this->__value;
    }

    /**
     * Gets a string value
     *
     * @return string
     */
    public function __toString()
    {
        return strval($this->__value);
    }

    /**
     * Gets as name
     *
     * Name of the party sending this batch of messages.
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Sets a new name
     *
     * Name of the party sending this batch of messages.
     *
     * @param string $name
     * @return self
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Gets as roleType
     *
     * Role type (i.e. ‘vendor’) of partner sending this document
     *
     * @return string
     */
    public function getRoleType()
    {
        return $this->roleType;
    }

    /**
     * Sets a new roleType
     *
     * Role type (i.e. ‘vendor’) of partner sending this document
     *
     * @param string $roleType
     * @return self
     */
    public function setRoleType($roleType)
    {
        $this->roleType = $roleType;
        return $this;
    }
}

