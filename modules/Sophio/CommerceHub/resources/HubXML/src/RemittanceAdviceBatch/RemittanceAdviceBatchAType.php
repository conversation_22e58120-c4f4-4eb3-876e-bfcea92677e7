<?php

namespace CommerceHub\HubXML\RemittanceAdviceBatch;

/**
 * Class representing RemittanceAdviceBatchAType
 */
class RemittanceAdviceBatchAType
{
    /**
     * Used for FA identification
     *
     * @var string $batchNumber
     */
    private $batchNumber = null;

    /**
     * Numeric HUB code for type of message
     *
     * @var string $fileType
     */
    private $fileType = null;

    /**
     * Numeric HUB code for type of message
     *
     * @var string $transactionType
     */
    private $transactionType = null;

    /**
     * Hub ID of partner receiving this document
     *
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\PartnerIDAType $partnerID
     */
    private $partnerID = null;

    /**
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType[] $remittanceAdvice
     */
    private $remittanceAdvice = [
        
    ];

    /**
     * @var string $messageCount
     */
    private $messageCount = null;

    /**
     * Gets as batchNumber
     *
     * Used for FA identification
     *
     * @return string
     */
    public function getBatchNumber()
    {
        return $this->batchNumber;
    }

    /**
     * Sets a new batchNumber
     *
     * Used for FA identification
     *
     * @param string $batchNumber
     * @return self
     */
    public function setBatchNumber($batchNumber)
    {
        $this->batchNumber = $batchNumber;
        return $this;
    }

    /**
     * Gets as fileType
     *
     * Numeric HUB code for type of message
     *
     * @return string
     */
    public function getFileType()
    {
        return $this->fileType;
    }

    /**
     * Sets a new fileType
     *
     * Numeric HUB code for type of message
     *
     * @param string $fileType
     * @return self
     */
    public function setFileType($fileType)
    {
        $this->fileType = $fileType;
        return $this;
    }

    /**
     * Gets as transactionType
     *
     * Numeric HUB code for type of message
     *
     * @return string
     */
    public function getTransactionType()
    {
        return $this->transactionType;
    }

    /**
     * Sets a new transactionType
     *
     * Numeric HUB code for type of message
     *
     * @param string $transactionType
     * @return self
     */
    public function setTransactionType($transactionType)
    {
        $this->transactionType = $transactionType;
        return $this;
    }

    /**
     * Gets as partnerID
     *
     * Hub ID of partner receiving this document
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\PartnerIDAType
     */
    public function getPartnerID()
    {
        return $this->partnerID;
    }

    /**
     * Sets a new partnerID
     *
     * Hub ID of partner receiving this document
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\PartnerIDAType $partnerID
     * @return self
     */
    public function setPartnerID(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\PartnerIDAType $partnerID)
    {
        $this->partnerID = $partnerID;
        return $this;
    }

    /**
     * Adds as remittanceAdvice
     *
     * @return self
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType $remittanceAdvice
     */
    public function addToRemittanceAdvice(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType $remittanceAdvice)
    {
        $this->remittanceAdvice[] = $remittanceAdvice;
        return $this;
    }

    /**
     * isset remittanceAdvice
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRemittanceAdvice($index)
    {
        return isset($this->remittanceAdvice[$index]);
    }

    /**
     * unset remittanceAdvice
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRemittanceAdvice($index)
    {
        unset($this->remittanceAdvice[$index]);
    }

    /**
     * Gets as remittanceAdvice
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType[]
     */
    public function getRemittanceAdvice()
    {
        return $this->remittanceAdvice;
    }

    /**
     * Sets a new remittanceAdvice
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType[] $remittanceAdvice
     * @return self
     */
    public function setRemittanceAdvice(array $remittanceAdvice)
    {
        $this->remittanceAdvice = $remittanceAdvice;
        return $this;
    }

    /**
     * Gets as messageCount
     *
     * @return string
     */
    public function getMessageCount()
    {
        return $this->messageCount;
    }

    /**
     * Sets a new messageCount
     *
     * @param string $messageCount
     * @return self
     */
    public function setMessageCount($messageCount)
    {
        $this->messageCount = $messageCount;
        return $this;
    }
}

