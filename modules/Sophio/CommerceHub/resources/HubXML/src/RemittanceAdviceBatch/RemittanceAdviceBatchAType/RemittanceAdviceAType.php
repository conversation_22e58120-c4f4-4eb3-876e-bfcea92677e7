<?php

namespace CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType;

/**
 * Class representing RemittanceAdviceAType
 */
class RemittanceAdviceAType
{
    /**
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RootTrxAType $rootTrx
     */
    private $rootTrx = null;

    /**
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemitAdviceDataAType $remitAdviceData
     */
    private $remitAdviceData = null;

    /**
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType[] $remittanceAdviceItem
     */
    private $remittanceAdviceItem = [
        
    ];

    /**
     * @var \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\PersonPlaceAType $personPlace
     */
    private $personPlace = null;

    /**
     * Gets as rootTrx
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RootTrxAType
     */
    public function getRootTrx()
    {
        return $this->rootTrx;
    }

    /**
     * Sets a new rootTrx
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RootTrxAType $rootTrx
     * @return self
     */
    public function setRootTrx(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RootTrxAType $rootTrx)
    {
        $this->rootTrx = $rootTrx;
        return $this;
    }

    /**
     * Gets as remitAdviceData
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemitAdviceDataAType
     */
    public function getRemitAdviceData()
    {
        return $this->remitAdviceData;
    }

    /**
     * Sets a new remitAdviceData
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemitAdviceDataAType $remitAdviceData
     * @return self
     */
    public function setRemitAdviceData(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemitAdviceDataAType $remitAdviceData)
    {
        $this->remitAdviceData = $remitAdviceData;
        return $this;
    }

    /**
     * Adds as remittanceAdviceItem
     *
     * @return self
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType $remittanceAdviceItem
     */
    public function addToRemittanceAdviceItem(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType $remittanceAdviceItem)
    {
        $this->remittanceAdviceItem[] = $remittanceAdviceItem;
        return $this;
    }

    /**
     * isset remittanceAdviceItem
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRemittanceAdviceItem($index)
    {
        return isset($this->remittanceAdviceItem[$index]);
    }

    /**
     * unset remittanceAdviceItem
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRemittanceAdviceItem($index)
    {
        unset($this->remittanceAdviceItem[$index]);
    }

    /**
     * Gets as remittanceAdviceItem
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType[]
     */
    public function getRemittanceAdviceItem()
    {
        return $this->remittanceAdviceItem;
    }

    /**
     * Sets a new remittanceAdviceItem
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType[] $remittanceAdviceItem
     * @return self
     */
    public function setRemittanceAdviceItem(array $remittanceAdviceItem = null)
    {
        $this->remittanceAdviceItem = $remittanceAdviceItem;
        return $this;
    }

    /**
     * Gets as personPlace
     *
     * @return \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\PersonPlaceAType
     */
    public function getPersonPlace()
    {
        return $this->personPlace;
    }

    /**
     * Sets a new personPlace
     *
     * @param \CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\PersonPlaceAType $personPlace
     * @return self
     */
    public function setPersonPlace(\CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\PersonPlaceAType $personPlace)
    {
        $this->personPlace = $personPlace;
        return $this;
    }
}

