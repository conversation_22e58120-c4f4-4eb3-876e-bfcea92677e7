<?php

namespace CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType;

/**
 * Class representing PoLineDataAType
 */
class PoLineDataAType
{
    /**
     * Customer expectation has been set that delivery will be on or before this date.
     *
     * @var string $lineReqDelvDate
     */
    private $lineReqDelvDate = null;

    /**
     * Gets as lineReqDelvDate
     *
     * Customer expectation has been set that delivery will be on or before this date.
     *
     * @return string
     */
    public function getLineReqDelvDate()
    {
        return $this->lineReqDelvDate;
    }

    /**
     * Sets a new lineReqDelvDate
     *
     * Customer expectation has been set that delivery will be on or before this date.
     *
     * @param string $lineReqDelvDate
     * @return self
     */
    public function setLineReqDelvDate($lineReqDelvDate)
    {
        $this->lineReqDelvDate = $lineReqDelvDate;
        return $this;
    }
}

