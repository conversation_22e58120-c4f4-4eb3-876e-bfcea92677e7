<?php

namespace CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType;

/**
 * Class representing PersonPlaceAType
 */
class PersonPlaceAType
{
    /**
     * Required Id for this element. Will be referenced by the IDREF attributes in the hubOrder and/or lineItem to establish a link to the to the name/address particulars for various parties associated with the purchase order. ID attribute values must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods.
     *
     * @var string $personPlaceID
     */
    private $personPlaceID = null;

    /**
     * The name field for the person/entity/facility.
     *
     * @var string $name1
     */
    private $name1 = null;

    /**
     * The first line of the street address field for the person/entity/facility.The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the ship to store information will be in address1
     *
     * @var mixed $address1
     */
    private $address1 = null;

    /**
     * Optional second line of street address.The street address (part 2) for the Ship To party.
     *
     * The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the normal address ( which would normally be in address1 ) will now be in address2
     *
     * @var mixed $address2
     */
    private $address2 = null;

    /**
     * Optional third line of street address.
     *
     * @var string $address3
     */
    private $address3 = null;

    /**
     * @var string $city
     */
    private $city = null;

    /**
     * @var string $state
     */
    private $state = null;

    /**
     * @var string $country
     */
    private $country = null;

    /**
     * @var string $postalCode
     */
    private $postalCode = null;

    /**
     * Email address associated with the person/entity/facility.
     *
     * @var string $email
     */
    private $email = null;

    /**
     * Daytime phone number for the person/entity/facility.
     *
     * @var string $dayPhone
     */
    private $dayPhone = null;

    /**
     * Id for this person/entity/facility assigned by the partner who maintains the data
     *
     * @var string $partnerPersonPlaceId
     */
    private $partnerPersonPlaceId = null;

    /**
     * Gets as personPlaceID
     *
     * Required Id for this element. Will be referenced by the IDREF attributes in the hubOrder and/or lineItem to establish a link to the to the name/address particulars for various parties associated with the purchase order. ID attribute values must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods.
     *
     * @return string
     */
    public function getPersonPlaceID()
    {
        return $this->personPlaceID;
    }

    /**
     * Sets a new personPlaceID
     *
     * Required Id for this element. Will be referenced by the IDREF attributes in the hubOrder and/or lineItem to establish a link to the to the name/address particulars for various parties associated with the purchase order. ID attribute values must begin with a letter and may be followed by any number of letters, digits, hyphens, underscores or periods.
     *
     * @param string $personPlaceID
     * @return self
     */
    public function setPersonPlaceID($personPlaceID)
    {
        $this->personPlaceID = $personPlaceID;
        return $this;
    }

    /**
     * Gets as name1
     *
     * The name field for the person/entity/facility.
     *
     * @return string
     */
    public function getName1()
    {
        return $this->name1;
    }

    /**
     * Sets a new name1
     *
     * The name field for the person/entity/facility.
     *
     * @param string $name1
     * @return self
     */
    public function setName1($name1)
    {
        $this->name1 = $name1;
        return $this;
    }

    /**
     * Gets as address1
     *
     * The first line of the street address field for the person/entity/facility.The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the ship to store information will be in address1
     *
     * @return mixed
     */
    public function getAddress1()
    {
        return $this->address1;
    }

    /**
     * Sets a new address1
     *
     * The first line of the street address field for the person/entity/facility.The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the ship to store information will be in address1
     *
     * @param mixed $address1
     * @return self
     */
    public function setAddress1($address1)
    {
        $this->address1 = $address1;
        return $this;
    }

    /**
     * Gets as address2
     *
     * Optional second line of street address.The street address (part 2) for the Ship To party.
     *
     * The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the normal address ( which would normally be in address1 ) will now be in address2
     *
     * @return mixed
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Sets a new address2
     *
     * Optional second line of street address.The street address (part 2) for the Ship To party.
     *
     * The Ship-to and Bill-to Address Line 1 data elements of the PO message will be populated with a BOSS value sent by The Home Depot. This enhancement will effectively shift the street address data for BOSS orders into the Address Line 2 field for both the Ship-to Address and Bill-to Address in the data set. In the example below, the Street Address data is now shifted to the Address Line 2 field.
     *
     * Note: For ship to store orders, the normal address ( which would normally be in address1 ) will now be in address2
     *
     * @param mixed $address2
     * @return self
     */
    public function setAddress2($address2)
    {
        $this->address2 = $address2;
        return $this;
    }

    /**
     * Gets as address3
     *
     * Optional third line of street address.
     *
     * @return string
     */
    public function getAddress3()
    {
        return $this->address3;
    }

    /**
     * Sets a new address3
     *
     * Optional third line of street address.
     *
     * @param string $address3
     * @return self
     */
    public function setAddress3($address3)
    {
        $this->address3 = $address3;
        return $this;
    }

    /**
     * Gets as city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Sets a new city
     *
     * @param string $city
     * @return self
     */
    public function setCity($city)
    {
        $this->city = $city;
        return $this;
    }

    /**
     * Gets as state
     *
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * Sets a new state
     *
     * @param string $state
     * @return self
     */
    public function setState($state)
    {
        $this->state = $state;
        return $this;
    }

    /**
     * Gets as country
     *
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Sets a new country
     *
     * @param string $country
     * @return self
     */
    public function setCountry($country)
    {
        $this->country = $country;
        return $this;
    }

    /**
     * Gets as postalCode
     *
     * @return string
     */
    public function getPostalCode()
    {
        return $this->postalCode;
    }

    /**
     * Sets a new postalCode
     *
     * @param string $postalCode
     * @return self
     */
    public function setPostalCode($postalCode)
    {
        $this->postalCode = $postalCode;
        return $this;
    }

    /**
     * Gets as email
     *
     * Email address associated with the person/entity/facility.
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Sets a new email
     *
     * Email address associated with the person/entity/facility.
     *
     * @param string $email
     * @return self
     */
    public function setEmail($email)
    {
        $this->email = $email;
        return $this;
    }

    /**
     * Gets as dayPhone
     *
     * Daytime phone number for the person/entity/facility.
     *
     * @return string
     */
    public function getDayPhone()
    {
        return $this->dayPhone;
    }

    /**
     * Sets a new dayPhone
     *
     * Daytime phone number for the person/entity/facility.
     *
     * @param string $dayPhone
     * @return self
     */
    public function setDayPhone($dayPhone)
    {
        $this->dayPhone = $dayPhone;
        return $this;
    }

    /**
     * Gets as partnerPersonPlaceId
     *
     * Id for this person/entity/facility assigned by the partner who maintains the data
     *
     * @return string
     */
    public function getPartnerPersonPlaceId()
    {
        return $this->partnerPersonPlaceId;
    }

    /**
     * Sets a new partnerPersonPlaceId
     *
     * Id for this person/entity/facility assigned by the partner who maintains the data
     *
     * @param string $partnerPersonPlaceId
     * @return self
     */
    public function setPartnerPersonPlaceId($partnerPersonPlaceId)
    {
        $this->partnerPersonPlaceId = $partnerPersonPlaceId;
        return $this;
    }
}

