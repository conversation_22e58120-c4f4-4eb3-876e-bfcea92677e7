<?php

namespace CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType;

/**
 * Class representing HubOrderAType
 */
class HubOrderAType
{
    /**
     * Not used
     *
     * @var string $type
     */
    private $type = null;

    /**
     * The transactionID attribute will contain an ID assigned by CommerceHub. This value needs to be returned in the FA in the trxID element , which is a child of messageAck.
     *
     * @var string $transactionID
     */
    private $transactionID = null;

    /**
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ParticipatingPartyAType[] $participatingParty
     */
    private $participatingParty = [
        
    ];

    /**
     * The sending (“From:”) party’s Id for the receiving party
     *
     * @var string $sendersIdForReceiver
     */
    private $sendersIdForReceiver = null;

    /**
     * A CommerceHub assigned ID for this purchase order.
     *
     * @var string $orderId
     */
    private $orderId = null;

    /**
     * The total number of line items included in this purchase order.
     *
     * @var string $lineCount
     */
    private $lineCount = null;

    /**
     * Merchant assigned PO number.
     *
     * @var string $poNumber
     */
    private $poNumber = null;

    /**
     * The orderDate element will contain the date the purchase order was issued by the merchant. CCYYMMDD format.
     *
     * @var string $orderDate
     */
    private $orderDate = null;

    /**
     * With drop-ship fulfillment, retail total - may print on customer's packing slip.
     *
     * @var string $total
     */
    private $total = null;

    /**
     * Link to personPlace element that instructs the vendor where and to whom to ship goods when fulfilling this Order.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ShipToAType $shipTo
     */
    private $shipTo = null;

    /**
     * With drop-ship fulfillment, link to personPlace element that represents the party that paid the merchant for goods.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\BillToAType $billTo
     */
    private $billTo = null;

    /**
     * With drop-ship fulfillment, link to personPlace element that represents the party to whom the merchant sold goods.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\CustomerAType $customer
     */
    private $customer = null;

    /**
     * Link to personPlace that instructs the vendor where and to whom to submit invoices related to fulfillment of this Order.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\InvoiceToAType $invoiceTo
     */
    private $invoiceTo = null;

    /**
     * If the ship method (i.e. the shipper and/or level of service that should be used when fulfilling the Order) is assumed to be the same for every line in the purchase order then the code that stipulates the ship method will be presented here.
     *
     * If the ship method can vary by line, this element will not be present here but will be populated in the lineItem element.
     *
     * Contact CommerceHub for a complete list of ship method codes.
     *
     * @var string $shippingCode
     */
    private $shippingCode = null;

    /**
     * This will contain a pre-assigned waybill number.
     *
     * @var string $preassignedWaybillNumber
     */
    private $preassignedWaybillNumber = null;

    /**
     * Numeric rank to be used as a factor in determining the order in which fulfillment work is released to the fulfillment operation on a given day.
     *
     * The work from Orders with a higher rank should be released ahead of the work from Orders with a lower rank. The work from Orders where this element is not present ranks lowest.
     *
     * This ranking should have no effect in setting the release date for the lines of an Order, only in sequencing the release of work that has been queued for a given date.
     *
     * @var string $controlNumber
     */
    private $controlNumber = null;

    /**
     * With drop-ship fulfillment, the salesDivision element contains a code that indicates which merchant sales division booked the sale. This value will typically determine the branding that must appear on the customer’s packing slip document.
     *
     * @var string $salesDivision
     */
    private $salesDivision = null;

    /**
     * ID/Reference number for one of a fulfiller's several warehouse/fulfillment centers. If some goods in Offer need to be fulfilled from another location, the exceptions must be reported at the Lineitem level.
     *
     * @var string $vendorWarehouseId
     */
    private $vendorWarehouseId = null;

    /**
     * With drop-ship fulfillment, Merchant assigned customer order number.
     *
     * @var string $custOrderNumber
     */
    private $custOrderNumber = null;

    /**
     * Agreement ID reported by the party issuing this Offer that identifies a contract or set of negotiated terms that govern this purchase.
     *
     * @var string $buyingContract
     */
    private $buyingContract = null;

    /**
     * The poHdrData element is used to wrap less commonly used header level elements (i.e. elements that apply to the purchase order as a whole).
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PoHdrDataAType $poHdrData
     */
    private $poHdrData = null;

    /**
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType[] $lineItem
     */
    private $lineItem = [
        
    ];

    /**
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @var \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PersonPlaceAType[] $personPlace
     */
    private $personPlace = [
        
    ];

    /**
     * Gets as type
     *
     * Not used
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Sets a new type
     *
     * Not used
     *
     * @param string $type
     * @return self
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Gets as transactionID
     *
     * The transactionID attribute will contain an ID assigned by CommerceHub. This value needs to be returned in the FA in the trxID element , which is a child of messageAck.
     *
     * @return string
     */
    public function getTransactionID()
    {
        return $this->transactionID;
    }

    /**
     * Sets a new transactionID
     *
     * The transactionID attribute will contain an ID assigned by CommerceHub. This value needs to be returned in the FA in the trxID element , which is a child of messageAck.
     *
     * @param string $transactionID
     * @return self
     */
    public function setTransactionID($transactionID)
    {
        $this->transactionID = $transactionID;
        return $this;
    }

    /**
     * Adds as participatingParty
     *
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @return self
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ParticipatingPartyAType $participatingParty
     */
    public function addToParticipatingParty(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ParticipatingPartyAType $participatingParty)
    {
        $this->participatingParty[] = $participatingParty;
        return $this;
    }

    /**
     * isset participatingParty
     *
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetParticipatingParty($index)
    {
        return isset($this->participatingParty[$index]);
    }

    /**
     * unset participatingParty
     *
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetParticipatingParty($index)
    {
        unset($this->participatingParty[$index]);
    }

    /**
     * Gets as participatingParty
     *
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ParticipatingPartyAType[]
     */
    public function getParticipatingParty()
    {
        return $this->participatingParty;
    }

    /**
     * Sets a new participatingParty
     *
     * Partner Id of a party involved in the business relationship.
     *
     * On documents outbound from Hub the party from whom the message is coming will be identified.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ParticipatingPartyAType[] $participatingParty
     * @return self
     */
    public function setParticipatingParty(array $participatingParty)
    {
        $this->participatingParty = $participatingParty;
        return $this;
    }

    /**
     * Gets as sendersIdForReceiver
     *
     * The sending (“From:”) party’s Id for the receiving party
     *
     * @return string
     */
    public function getSendersIdForReceiver()
    {
        return $this->sendersIdForReceiver;
    }

    /**
     * Sets a new sendersIdForReceiver
     *
     * The sending (“From:”) party’s Id for the receiving party
     *
     * @param string $sendersIdForReceiver
     * @return self
     */
    public function setSendersIdForReceiver($sendersIdForReceiver)
    {
        $this->sendersIdForReceiver = $sendersIdForReceiver;
        return $this;
    }

    /**
     * Gets as orderId
     *
     * A CommerceHub assigned ID for this purchase order.
     *
     * @return string
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * Sets a new orderId
     *
     * A CommerceHub assigned ID for this purchase order.
     *
     * @param string $orderId
     * @return self
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * Gets as lineCount
     *
     * The total number of line items included in this purchase order.
     *
     * @return string
     */
    public function getLineCount()
    {
        return $this->lineCount;
    }

    /**
     * Sets a new lineCount
     *
     * The total number of line items included in this purchase order.
     *
     * @param string $lineCount
     * @return self
     */
    public function setLineCount($lineCount)
    {
        $this->lineCount = $lineCount;
        return $this;
    }

    /**
     * Gets as poNumber
     *
     * Merchant assigned PO number.
     *
     * @return string
     */
    public function getPoNumber()
    {
        return $this->poNumber;
    }

    /**
     * Sets a new poNumber
     *
     * Merchant assigned PO number.
     *
     * @param string $poNumber
     * @return self
     */
    public function setPoNumber($poNumber)
    {
        $this->poNumber = $poNumber;
        return $this;
    }

    /**
     * Gets as orderDate
     *
     * The orderDate element will contain the date the purchase order was issued by the merchant. CCYYMMDD format.
     *
     * @return string
     */
    public function getOrderDate()
    {
        return $this->orderDate;
    }

    /**
     * Sets a new orderDate
     *
     * The orderDate element will contain the date the purchase order was issued by the merchant. CCYYMMDD format.
     *
     * @param string $orderDate
     * @return self
     */
    public function setOrderDate($orderDate)
    {
        $this->orderDate = $orderDate;
        return $this;
    }

    /**
     * Gets as total
     *
     * With drop-ship fulfillment, retail total - may print on customer's packing slip.
     *
     * @return string
     */
    public function getTotal()
    {
        return $this->total;
    }

    /**
     * Sets a new total
     *
     * With drop-ship fulfillment, retail total - may print on customer's packing slip.
     *
     * @param string $total
     * @return self
     */
    public function setTotal($total)
    {
        $this->total = $total;
        return $this;
    }

    /**
     * Gets as shipTo
     *
     * Link to personPlace element that instructs the vendor where and to whom to ship goods when fulfilling this Order.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ShipToAType
     */
    public function getShipTo()
    {
        return $this->shipTo;
    }

    /**
     * Sets a new shipTo
     *
     * Link to personPlace element that instructs the vendor where and to whom to ship goods when fulfilling this Order.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ShipToAType $shipTo
     * @return self
     */
    public function setShipTo(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\ShipToAType $shipTo)
    {
        $this->shipTo = $shipTo;
        return $this;
    }

    /**
     * Gets as billTo
     *
     * With drop-ship fulfillment, link to personPlace element that represents the party that paid the merchant for goods.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\BillToAType
     */
    public function getBillTo()
    {
        return $this->billTo;
    }

    /**
     * Sets a new billTo
     *
     * With drop-ship fulfillment, link to personPlace element that represents the party that paid the merchant for goods.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\BillToAType $billTo
     * @return self
     */
    public function setBillTo(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\BillToAType $billTo)
    {
        $this->billTo = $billTo;
        return $this;
    }

    /**
     * Gets as customer
     *
     * With drop-ship fulfillment, link to personPlace element that represents the party to whom the merchant sold goods.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\CustomerAType
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * Sets a new customer
     *
     * With drop-ship fulfillment, link to personPlace element that represents the party to whom the merchant sold goods.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\CustomerAType $customer
     * @return self
     */
    public function setCustomer(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\CustomerAType $customer)
    {
        $this->customer = $customer;
        return $this;
    }

    /**
     * Gets as invoiceTo
     *
     * Link to personPlace that instructs the vendor where and to whom to submit invoices related to fulfillment of this Order.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\InvoiceToAType
     */
    public function getInvoiceTo()
    {
        return $this->invoiceTo;
    }

    /**
     * Sets a new invoiceTo
     *
     * Link to personPlace that instructs the vendor where and to whom to submit invoices related to fulfillment of this Order.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\InvoiceToAType $invoiceTo
     * @return self
     */
    public function setInvoiceTo(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\InvoiceToAType $invoiceTo)
    {
        $this->invoiceTo = $invoiceTo;
        return $this;
    }

    /**
     * Gets as shippingCode
     *
     * If the ship method (i.e. the shipper and/or level of service that should be used when fulfilling the Order) is assumed to be the same for every line in the purchase order then the code that stipulates the ship method will be presented here.
     *
     * If the ship method can vary by line, this element will not be present here but will be populated in the lineItem element.
     *
     * Contact CommerceHub for a complete list of ship method codes.
     *
     * @return string
     */
    public function getShippingCode()
    {
        return $this->shippingCode;
    }

    /**
     * Sets a new shippingCode
     *
     * If the ship method (i.e. the shipper and/or level of service that should be used when fulfilling the Order) is assumed to be the same for every line in the purchase order then the code that stipulates the ship method will be presented here.
     *
     * If the ship method can vary by line, this element will not be present here but will be populated in the lineItem element.
     *
     * Contact CommerceHub for a complete list of ship method codes.
     *
     * @param string $shippingCode
     * @return self
     */
    public function setShippingCode($shippingCode)
    {
        $this->shippingCode = $shippingCode;
        return $this;
    }

    /**
     * Gets as preassignedWaybillNumber
     *
     * This will contain a pre-assigned waybill number.
     *
     * @return string
     */
    public function getPreassignedWaybillNumber()
    {
        return $this->preassignedWaybillNumber;
    }

    /**
     * Sets a new preassignedWaybillNumber
     *
     * This will contain a pre-assigned waybill number.
     *
     * @param string $preassignedWaybillNumber
     * @return self
     */
    public function setPreassignedWaybillNumber($preassignedWaybillNumber)
    {
        $this->preassignedWaybillNumber = $preassignedWaybillNumber;
        return $this;
    }

    /**
     * Gets as controlNumber
     *
     * Numeric rank to be used as a factor in determining the order in which fulfillment work is released to the fulfillment operation on a given day.
     *
     * The work from Orders with a higher rank should be released ahead of the work from Orders with a lower rank. The work from Orders where this element is not present ranks lowest.
     *
     * This ranking should have no effect in setting the release date for the lines of an Order, only in sequencing the release of work that has been queued for a given date.
     *
     * @return string
     */
    public function getControlNumber()
    {
        return $this->controlNumber;
    }

    /**
     * Sets a new controlNumber
     *
     * Numeric rank to be used as a factor in determining the order in which fulfillment work is released to the fulfillment operation on a given day.
     *
     * The work from Orders with a higher rank should be released ahead of the work from Orders with a lower rank. The work from Orders where this element is not present ranks lowest.
     *
     * This ranking should have no effect in setting the release date for the lines of an Order, only in sequencing the release of work that has been queued for a given date.
     *
     * @param string $controlNumber
     * @return self
     */
    public function setControlNumber($controlNumber)
    {
        $this->controlNumber = $controlNumber;
        return $this;
    }

    /**
     * Gets as salesDivision
     *
     * With drop-ship fulfillment, the salesDivision element contains a code that indicates which merchant sales division booked the sale. This value will typically determine the branding that must appear on the customer’s packing slip document.
     *
     * @return string
     */
    public function getSalesDivision()
    {
        return $this->salesDivision;
    }

    /**
     * Sets a new salesDivision
     *
     * With drop-ship fulfillment, the salesDivision element contains a code that indicates which merchant sales division booked the sale. This value will typically determine the branding that must appear on the customer’s packing slip document.
     *
     * @param string $salesDivision
     * @return self
     */
    public function setSalesDivision($salesDivision)
    {
        $this->salesDivision = $salesDivision;
        return $this;
    }

    /**
     * Gets as vendorWarehouseId
     *
     * ID/Reference number for one of a fulfiller's several warehouse/fulfillment centers. If some goods in Offer need to be fulfilled from another location, the exceptions must be reported at the Lineitem level.
     *
     * @return string
     */
    public function getVendorWarehouseId()
    {
        return $this->vendorWarehouseId;
    }

    /**
     * Sets a new vendorWarehouseId
     *
     * ID/Reference number for one of a fulfiller's several warehouse/fulfillment centers. If some goods in Offer need to be fulfilled from another location, the exceptions must be reported at the Lineitem level.
     *
     * @param string $vendorWarehouseId
     * @return self
     */
    public function setVendorWarehouseId($vendorWarehouseId)
    {
        $this->vendorWarehouseId = $vendorWarehouseId;
        return $this;
    }

    /**
     * Gets as custOrderNumber
     *
     * With drop-ship fulfillment, Merchant assigned customer order number.
     *
     * @return string
     */
    public function getCustOrderNumber()
    {
        return $this->custOrderNumber;
    }

    /**
     * Sets a new custOrderNumber
     *
     * With drop-ship fulfillment, Merchant assigned customer order number.
     *
     * @param string $custOrderNumber
     * @return self
     */
    public function setCustOrderNumber($custOrderNumber)
    {
        $this->custOrderNumber = $custOrderNumber;
        return $this;
    }

    /**
     * Gets as buyingContract
     *
     * Agreement ID reported by the party issuing this Offer that identifies a contract or set of negotiated terms that govern this purchase.
     *
     * @return string
     */
    public function getBuyingContract()
    {
        return $this->buyingContract;
    }

    /**
     * Sets a new buyingContract
     *
     * Agreement ID reported by the party issuing this Offer that identifies a contract or set of negotiated terms that govern this purchase.
     *
     * @param string $buyingContract
     * @return self
     */
    public function setBuyingContract($buyingContract)
    {
        $this->buyingContract = $buyingContract;
        return $this;
    }

    /**
     * Gets as poHdrData
     *
     * The poHdrData element is used to wrap less commonly used header level elements (i.e. elements that apply to the purchase order as a whole).
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PoHdrDataAType
     */
    public function getPoHdrData()
    {
        return $this->poHdrData;
    }

    /**
     * Sets a new poHdrData
     *
     * The poHdrData element is used to wrap less commonly used header level elements (i.e. elements that apply to the purchase order as a whole).
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PoHdrDataAType $poHdrData
     * @return self
     */
    public function setPoHdrData(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PoHdrDataAType $poHdrData)
    {
        $this->poHdrData = $poHdrData;
        return $this;
    }

    /**
     * Adds as lineItem
     *
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @return self
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType $lineItem
     */
    public function addToLineItem(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType $lineItem)
    {
        $this->lineItem[] = $lineItem;
        return $this;
    }

    /**
     * isset lineItem
     *
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetLineItem($index)
    {
        return isset($this->lineItem[$index]);
    }

    /**
     * unset lineItem
     *
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetLineItem($index)
    {
        unset($this->lineItem[$index]);
    }

    /**
     * Gets as lineItem
     *
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType[]
     */
    public function getLineItem()
    {
        return $this->lineItem;
    }

    /**
     * Sets a new lineItem
     *
     * The lineItem element is used to wrap the details for each product or service for which fulfillment is being requested in the Purchase Order.
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\LineItemAType[] $lineItem
     * @return self
     */
    public function setLineItem(array $lineItem)
    {
        $this->lineItem = $lineItem;
        return $this;
    }

    /**
     * Adds as personPlace
     *
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @return self
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PersonPlaceAType $personPlace
     */
    public function addToPersonPlace(\CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PersonPlaceAType $personPlace)
    {
        $this->personPlace[] = $personPlace;
        return $this;
    }

    /**
     * isset personPlace
     *
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPersonPlace($index)
    {
        return isset($this->personPlace[$index]);
    }

    /**
     * unset personPlace
     *
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPersonPlace($index)
    {
        unset($this->personPlace[$index]);
    }

    /**
     * Gets as personPlace
     *
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @return \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PersonPlaceAType[]
     */
    public function getPersonPlace()
    {
        return $this->personPlace;
    }

    /**
     * Sets a new personPlace
     *
     * Name and address particulars for a person/entity/facility (e.g. customer, ship-to, bill-to, invoice-to, transfer-location) involved with this Order
     *
     * @param \CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType\PersonPlaceAType[] $personPlace
     * @return self
     */
    public function setPersonPlace(array $personPlace)
    {
        $this->personPlace = $personPlace;
        return $this;
    }
}

