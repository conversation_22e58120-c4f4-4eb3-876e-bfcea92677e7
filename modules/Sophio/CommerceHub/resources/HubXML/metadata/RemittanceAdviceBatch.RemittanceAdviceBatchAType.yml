CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType:
    properties:
        batchNumber:
            expose: true
            access_type: public_method
            serialized_name: batchNumber
            accessor:
                getter: getBatchNumber
                setter: setBatchNumber
            xml_attribute: true
            type: string
        fileType:
            expose: true
            access_type: public_method
            serialized_name: fileType
            accessor:
                getter: getFileType
                setter: setFileType
            xml_attribute: true
            type: string
        transactionType:
            expose: true
            access_type: public_method
            serialized_name: transactionType
            accessor:
                getter: getTransactionType
                setter: setTransactionType
            xml_attribute: true
            type: string
        partnerID:
            expose: true
            access_type: public_method
            serialized_name: partnerID
            xml_element:
                cdata: false
            accessor:
                getter: getPartnerID
                setter: setPartnerID
            type: CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\PartnerIDAType
        remittanceAdvice:
            expose: true
            access_type: public_method
            serialized_name: remittanceAdvice
            xml_element:
                cdata: false
            accessor:
                getter: getRemittanceAdvice
                setter: setRemittanceAdvice
            xml_list:
                inline: true
                entry_name: remittanceAdvice
            type: array<CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType>
        messageCount:
            expose: true
            access_type: public_method
            serialized_name: messageCount
            xml_element:
                cdata: false
            accessor:
                getter: getMessageCount
                setter: setMessageCount
            type: string
