CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType:
    properties:
        batchNumber:
            expose: true
            access_type: public_method
            serialized_name: batchNumber
            accessor:
                getter: getBatchNumber
                setter: setBatchNumber
            xml_attribute: true
            type: string
        partnerID:
            expose: true
            access_type: public_method
            serialized_name: partnerID
            xml_element:
                cdata: false
            accessor:
                getter: getPartnerID
                setter: setPartnerID
            type: CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\PartnerIDAType
        hubOrder:
            expose: true
            access_type: public_method
            serialized_name: hubOrder
            xml_element:
                cdata: false
            accessor:
                getter: getHubOrder
                setter: setHubOrder
            xml_list:
                inline: true
                entry_name: hubOrder
            type: array<CommerceHub\HubXML\OrderMessageBatch\OrderMessageBatchAType\HubOrderAType>
        messageCount:
            expose: true
            access_type: public_method
            serialized_name: messageCount
            xml_element:
                cdata: false
            accessor:
                getter: getMessageCount
                setter: setMessageCount
            type: string
