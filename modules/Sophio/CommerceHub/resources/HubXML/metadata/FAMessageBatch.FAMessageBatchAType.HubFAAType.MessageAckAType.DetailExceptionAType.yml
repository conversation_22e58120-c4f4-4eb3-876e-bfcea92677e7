CommerceHub\HubXML\FAMessageBatch\FAMessageBatchAType\HubFAAType\MessageAckAType\DetailExceptionAType:
    properties:
        detailID:
            expose: true
            access_type: public_method
            serialized_name: detailID
            xml_element:
                cdata: false
            accessor:
                getter: getDetailID
                setter: setDetailID
            type: string
        exceptionDesc:
            expose: true
            access_type: public_method
            serialized_name: exceptionDesc
            xml_element:
                cdata: false
            accessor:
                getter: getExceptionDesc
                setter: setExceptionDesc
            xml_list:
                inline: true
                entry_name: exceptionDesc
            type: array<string>
