CommerceHub\HubXML\InvoiceMessageBatch\InvoiceMessageBatchAType\HubInvoiceAType\HubActionAType:
    properties:
        action:
            expose: true
            access_type: public_method
            serialized_name: action
            xml_element:
                cdata: false
            accessor:
                getter: getAction
                setter: setAction
            type: string
        merchantLineNumber:
            expose: true
            access_type: public_method
            serialized_name: merchantLineNumber
            xml_element:
                cdata: false
            accessor:
                getter: getMerchantLineNumber
                setter: setMerchantLineNumber
            type: string
        trxQty:
            expose: true
            access_type: public_method
            serialized_name: trxQty
            xml_element:
                cdata: false
            accessor:
                getter: getTrxQty
                setter: setTrxQty
            type: string
        trxUnitCost:
            expose: true
            access_type: public_method
            serialized_name: trxUnitCost
            xml_element:
                cdata: false
            accessor:
                getter: getTrxUnitCost
                setter: setTrxUnitCost
            type: string
        invoiceDetailLink:
            expose: true
            access_type: public_method
            serialized_name: invoiceDetailLink
            xml_element:
                cdata: false
            accessor:
                getter: getInvoiceDetailLink
                setter: setInvoiceDetailLink
            type: CommerceHub\HubXML\InvoiceMessageBatch\InvoiceMessageBatchAType\HubInvoiceAType\HubActionAType\InvoiceDetailLinkAType
        packageDetailLink:
            expose: true
            access_type: public_method
            serialized_name: packageDetailLink
            xml_element:
                cdata: false
            accessor:
                getter: getPackageDetailLink
                setter: setPackageDetailLink
            xml_list:
                inline: true
                entry_name: packageDetailLink
            type: array<CommerceHub\HubXML\InvoiceMessageBatch\InvoiceMessageBatchAType\HubInvoiceAType\HubActionAType\PackageDetailLinkAType>
