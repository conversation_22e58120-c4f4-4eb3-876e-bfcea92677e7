CommerceHub\HubXML\InvoiceMessageBatch\InvoiceMessageBatchAType\HubInvoiceAType\TrxDataAType:
    properties:
        discountBreakout:
            expose: true
            access_type: public_method
            serialized_name: discountBreakout
            xml_element:
                cdata: false
            accessor:
                getter: getDiscountBreakout
                setter: setDiscountBreakout
            xml_list:
                inline: true
                entry_name: discountBreakout
            type: array<CommerceHub\HubXML\DiscountBreakout>
