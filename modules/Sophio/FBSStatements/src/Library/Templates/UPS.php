<?php

namespace So<PERSON><PERSON>\FBSStatements\Library\Templates;

use Carbon\Carbon;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\FBS\LineItem;

class UPS extends Template
{
    public function open()
    {
        $this->reader = ReaderEntityFactory::createCSVReader();
        $this->reader->setFieldDelimiter(',');
        $this->reader->setShouldFormatDates(true);
        $this->reader->open($this->file);
    }

    public function transformRow($row)
    {
        $array = [
            'stmtdate' => Carbon::parse($row[1])->format('m/d/Y'),
            'when' => Carbon::parse($row[1]),
            'supinvdate' => Carbon::parse($row[1]),
            'ordnum' => $row[0],
            'descript' => $row[4],
            'invtotalc' =>(float) str_replace('$', '', $row[7])
        ];
        $lineitem = LineItem::where('track_num', 'UPS:' . $array['ordnum'])->first();
        if ($lineitem) {
            $array['invpk'] = $lineitem->invpk;
        } else {
            $array['invpk'] = 0;
        }
        $array['invtype'] = 'INVOICE';
        $array['profilepk'] = $this->supplier->PK;
        $array['supname'] = 'ups';
        $array['supstmntid'] = $this->supstmntid;
        $array['supinvpk'] = $this->supstmntid;
        $array['pathsupfil'] = $this->file;
        $array['resultCode'] = '';
        $array['sup'] = 'UPS';
        return $array;
    }

    public function validateRow($row)
    {
        if ($row['ordnum'] !== "")
            return true;
    }

    public function collectRow($row)
    {
        if (isset($this->rows[$row['ordnum']])) {
            $this->rows[$row['ordnum']]['invtotalc'] +=$row['invtotalc'];
        } else {
            $this->rows[$row['ordnum']] = $row;
        }

    }
}