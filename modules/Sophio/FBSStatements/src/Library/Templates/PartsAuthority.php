<?php

namespace So<PERSON>o\FBSStatements\Library\Templates;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\FBSReturns\src\Models\ReturnLine;

/**
 * PartsAuthority format is invoice centric
 * we try to explode their rows into line-centric
 * if we cannot make the explode (no invoice matched or returns) we submit the row as is
 * 'unexploded' rows will have qty=1 and empty sku/linecode
 */
class PartsAuthority extends Template
{

    protected string $format = "PAR";
    protected array $invoiced = [];
    protected array $credited = [];

    public function open()
    {

        $this->reader = ReaderEntityFactory::createXLSXReader();
        $this->reader->setShouldFormatDates(true);
        $this->reader->open($this->file);
    }

    public function collectRow($row)
    {
        $key = $row['invpk'] . '$' . $row['linecode'] . '$' . $row['sku'] . '$' . $row['invtotalc'];
        if ($row['invtotalc'] > 0) {
            $this->invoiced[] = $key;
        } else {
            $this->credited[] = $key;
        }
        $rows = $this->explodeToLines($row);
        foreach ($rows as $r) {
            if (array_key_exists('invoice', $r)) {
                unset($r['invoice']); // remove the invoice object
            }
            if (array_key_exists('returnlines', $r)) {
                unset($r['returnlines']); // remove the return lines for credit
            }
            $this->rows[] = $r;
        }

    }

    public function validateRow($row)
    {

        if (($row['account'] === $this->supplier->SETTINGS['STATEMENTACCOUNT']) || $this->supplier->SETTINGS['STATEMENTACCOUNT'] == "") {
            if ($row['qty'] !== 0) {
                return true;
            }
            if ($row['supinvpk'] == "") {
                Log::error('NOT supinvpk for ' . implode('-', $row));
                return false;
            }
        }
        Log::error('NOT valid for ' . implode('-', $row));
        return false;
    }

    public function explodeToLines($row)
    {
        $line_rows = [];
        if ($row['invpk'] > 0 && $row['invtype'] == 'INVOICE') {


            $total_cost_lines = 0;

            foreach ($row['invoice']->lineitem->where('profilepk', $this->supplier->PK) as $lineitem) {
                $total_cost_lines += $lineitem->qty * ($lineitem->cost + $lineitem->coreprice);
            }
            foreach ($row['invoice']->lineitem->where('profilepk', $this->supplier->PK) as $lineitem) {
                $line_row = $row;
                $line_row['invtotalc'] = $line_row['itemtotal'] = $row['itemtotal'] * ($lineitem->qty * ($lineitem->cost + $lineitem->coreprice)) / $total_cost_lines;
                $line_row['linecode'] = $lineitem->linecode;
                $line_row['sku'] = $lineitem->sku;
                $line_row['qty'] = $lineitem->qty;
                $line_row['cost'] = abs($line_row['itemtotal'] / $line_row['qty']);
                $line_row['handlingc'] = $row['handlingc'] * ($lineitem->qty * ($lineitem->cost + $lineitem->coreprice)) / $total_cost_lines;

                $line_rows[] = $line_row;
            }
            if (count($line_rows) > 0) {
                return $line_rows;
            } else {
                $row['invpk'] = 0;
                return [$row];
            }

        }
        if ($row['invpk'] > 0 && $row['invtype'] == 'CREDIT') {
            $total_cost_return_lines = 0;
            if ($row['ismanifest'] == 1) {
                $returns = $row['returnlines'];
            } else {
                $returns = $row['invoice'];
                if ($row['invoice'] === null) {
                    return [$row];
                }
            }

            foreach ($returns->lineitem->where('profilepk', $this->supplier->PK) as $returnLine) {
                $total_cost_return_lines += $returnLine->qty * ($returnLine->cost + $returnLine->coreprice);
            }
            foreach ($returns->lineitem->where('profilepk', $this->supplier->PK) as $returnLine) {
                $line_row = $row;
                $line_row['invtotalc'] = $line_row['itemtotal'] = $row['itemtotal'] * ($returnLine->qty * ($returnLine->cost + $returnLine->coreprice)) / $total_cost_return_lines;
                $line_row['linecode'] = $returnLine->linecode;
                $line_row['sku'] = $returnLine->sku;
                $line_row['qty'] = $returnLine->qty;
                $line_row['cost'] = abs($line_row['itemtotal'] / $line_row['qty']);
                $line_row['handlingc'] = $row['handlingc'] * ($returnLine->qty * ($returnLine->cost + $returnLine->coreprice)) / $total_cost_return_lines;

                $line_rows[] = $line_row;
            }
            if (count($line_rows) > 0) {
                return $line_rows;
            } else {
                $row['invpk'] = 0;
                return [$row];
            }
        }
        //we didn't recognized the invoice pk, we return the row as is
        return [$row];

    }

    public function transformRow($row)
    {
        $sup_ord_id = $row[4];
        $sup_ex = explode('-', $sup_ord_id);
        if (count($sup_ex) > 1) {
            $invpk = $sup_ex[1];
        } else {
            $invpk = $row[4];
        }
        $array = [
            'account' => $row[0],
            'supinvpk' => $row[3],
            'sup' => $this->supplier->SUP,
            'when' => Carbon::parse($row[2]),
            'invpk' => $invpk,
            'linecode' => "",
            'sku' => "",
            'qty' => 1,
            'cost' => abs((float)str_replace([',', ')', '(', '"'], '', $row[6])),
            'coreprice' => 0,
            'itemtotal' => (float)str_replace([',', ')', '(', '"'], '', $row[6]),
            'handlingc' => (float)str_replace([',', ')', '(', '"'], '', $row[7]),
            'invtotalc' => (float)str_replace([',', ')', '(', '"'], '', $row[6])
        ];
        $array['ponumber'] = $array['invpk'];
        $array['stmtdate'] = $array['when']->format('m/d/y');
        $array['bankdate'] = $this->date;
        $array['supcmemoid'] = '';
        $array['supstmntid'] = $this->supstmntid;
        $array['pathsupfil'] = $this->file;
        $array['profilepk'] = $this->supplier->PK;
        $array['supname'] = $this->supplier->NAME;
        $array['isinvpk'] = 0;
        $array['custtype'] = '';
        $array['ismanifest'] = 0;

        if ($array['invtotalc'] > 0) {
            $array['invtype'] = 'INVOICE';
            $array['descript'] = 'INVOICE';
            $array['invoice'] = Invoice::find($array['invpk']);
            if ($array['invoice']) {
                $array['custtype'] = $array['invoice']->custtype;
            } else {
                $array['invpk'] = 0;
            }
        } else {
            $array['invtype'] = 'CREDIT';
            $array['descript'] = 'CREDIT';
            $array['invoice'] = Invoice::find($array['invpk']);
            if ($array['invoice']) {
                $array['custtype'] = $array['invoice']->custtype;
                $array['isinvpk'] = 1;
            } else {
                $return = ReturnLine::where('returnedid', $array['invpk'])->where('profilepk', $this->supplier->PK)->first();
                if ($return) {
                    $array['returnlines'] = $return;
                    //invpk is the returnedid
                    $array['ismanifest'] = 1;
                }
            }
        }

        return $array;
    }

    public function processRows()
    {
        \Log::channel('fbsstatement')->info('processRows');
        parent::processRows();
        /**
         * here we look if invoices have credits. Note that it's possible to have more than one credit per
         * invoice, so we need to search them. If the sum of credits doesn't match the invoice, we don't do anything
         * (it will be settled later)
         */

        foreach ($this->rows as $k => $invoice) {
            if ($invoice['invtype'] === 'INVOICE') {
                $credit_keys = [];
                $credited = 0;
                foreach ($this->rows as $x => $credit) {

                    if ($credit['invtype'] === 'CREDIT' && $invoice['ponumber'] === $credit['ponumber'] && $credit['linecode'] == $invoice['linecode'] && $credit['sku'] == $invoice['sku']) {
                        if ($invoice['invtotalc'] == -$credit['invtotalc']) {
                            $this->rows[$k]['invtype'] = 'INVCRED';
                            $this->rows[$k]['supcmemoid'] = $this->rows[$x]['supinvpk'];
                            $this->rows[$x]['supcmemoid'] = $this->rows[$x]['supinvpk'];
                            $this->rows[$x]['invtype'] = 'INVCRED';
                            break;
                        }
                        $credited += $credit['invtotalc'];
                        $credit_keys[] = $x;
                    }
                }
                if ($invoice['invtotalc'] === -$credited) {
                    $this->rows[$k]['invtype'] = 'INVCRED';
                    $this->rows[$k]['supcmemoid'] = $this->rows[$x]['supinvpk'];
                    foreach ($credit_keys as $xx) {
                        $this->rows[$xx]['supcmemoid'] = $this->rows[$xx]['supinvpk'];
                        $this->rows[$xx]['invtype'] = 'INVCRED';
                    }
                }
            }

        }

    }
}