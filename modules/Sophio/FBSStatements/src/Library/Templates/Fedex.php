<?php

namespace <PERSON><PERSON><PERSON>\FBSStatements\Library\Templates;


use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\StatementHeader;


class Fedex extends Template
{
    protected $supstmntid_cache = [];

    public function open()
    {
        $f = fopen($this->file,'r');
        $line = fgets($f);
        fclose($f);
        $comma = substr_count($line, ",");
        $semicolon = substr_count($line, ";");
        $this->reader = ReaderEntityFactory::createCSVReader();
        $this->reader->setFieldDelimiter($semicolon>$comma?';':',');
        $this->reader->setShouldFormatDates(true);
        $this->reader->open($this->file);
    }

    public function transformRow($row)
    {
        Log::channel('fbsstatement')->error(print_r(combine_arr($this->header, $row), true));
        $array = [
            'account' => $row[0], //bill_to_ac
            'acctname' => $row[0], //bill_to_ac
            'stmtdate' => Carbon::parse($row[1]), //INVOICE_DA
            'supinvpk' => $row[2], //INVOICE_NU
            'invtype' => 'INVOICE',
            'tax' => $row[17], //actual_wei
            'invtotalc' => $row[10], //net_charge
            'ordnum' => $row[8], //track_num
            'descript' => $row[11], //SERVICE_TY
            'supinvdate' => Carbon::parse($row[13]), //SHIPMENT_D
            'when' => Carbon::parse($row[1]), //INVOICE_DA
            'totalamt' => $row[4], //ORIGINAL_A
            'bankdate' => Carbon::parse($row[13]), //SHIPMENT_D
            'sup_ord_id' => $row[12], //GROUND_SER
            'ponumber' => $row[56], //RMA_
            'invpk' => 0,
            'supstmntid' => $row[2]  //INVOICE_NU

        ];
        // if tdmastertr has value and different than TRACK_NUM use tdmastertr
        if ($row[25] !== "" && $row[8] !== $row[25] && $array['ordnum'] !== "") {
            $array['ordnum'] = $row[25];
        }
        // if ground_tra has value, add it to ordnum
        if ($row[7] !== "" && $array['ordnum']) {
            $array['ordnum'] = $row[7] . $array['ordnum'];
        }
        $l = [];
        if ($row[50] !== "") {  //Original Ref#3/PO Number
            $l[] = $row[50];
        }
        if ($row[49] !== "") {//Original Ref#2
            $l[] = $row[49];
        }
        if ($row[48] !== "") {//Original Customer Reference
            $l[] = $row[48];
        }
        if ($row[56] !== "") {//rma_
            $l[] = $row[56];
        }
        $invoices = Invoice::whereIn('pk', $l);
        if ($row[50] !== "") {
            $invoices->orWhere('ponumber', $row[50]);

        }
        $invoices = $invoices->get();
        if ($invoices && count($invoices) > 0) {
            foreach ($invoices as $invoice) {
                if ($invoice->ponumber == $row[50]) {
                    $array['invpk'] = $invoice->pk;
                    break;
                }
                if ($invoice->pk == $row[50]) {
                    $array['invpk'] = $invoice->pk;
                    break;
                }
                if ($invoice->pk == $row[49]) {
                    $array['invpk'] = $invoice->pk;
                    break;
                }
                if ($invoice->pk == $row[48]) {
                    $array['invpk'] = $invoice->pk;
                    break;
                }
                if ($invoice->pk == $row[56]) {
                    $array['invpk'] = $invoice->pk;
                    break;
                }
            }

        } else {
            $lineitem = LineItem::where('track_num','FDX:'.$array['ordnum'])->first();
            if($lineitem ) {
                $array['invpk'] = $lineitem->invpk;
            }else{
                $array['invpk'] = 0;
            }

        }


        $array['profilepk'] = $this->supplier->PK;
        $array['supname'] = 'fedex';
        $array['notes'] = $row[55]??'';

        $array['pathsupfil'] = $this->file;
        $array['resultCode'] = '';
        $array['sup'] = 'FDX';
        $array['originzip'] = $row[46];
        return $array;
    }

    public function validateRow($row)
    {
        if (isset($this->supstmntid_cache[$row['supstmntid']])) {
            return !$this->supstmntid_cache[$row['supstmntid']];

        } else {
            $already = StatementHeader::where('supstmntid', $row['supstmntid'])->first();
            if ($already) {
                $this->supstmntid_cache[$row['supstmntid']] = true;
                return false;
            } else {
                $this->supstmntid_cache[$row['supstmntid']] = false;
                return true;
            }
        }

    }

    public function collectRow($row)
    {
        unset($row['quoted']);
        unset($row['recipient']);
        $this->rows[] = $row;
    }
}