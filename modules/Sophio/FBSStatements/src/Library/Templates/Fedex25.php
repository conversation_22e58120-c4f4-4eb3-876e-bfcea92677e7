<?php

namespace So<PERSON><PERSON>\FBSStatements\Library\Templates;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\FBS\LineItem;

class Fedex25 extends Template
{
    protected $supstmntid_cache = [];

    public function open()
    {
        $f = fopen($this->file,'r');
        $line = fgets($f);
        fclose($f);
        $comma = substr_count($line, ",");
        $semicolon = substr_count($line, ";");
        $this->reader = ReaderEntityFactory::createXLSXReader();
        $this->reader->setShouldFormatDates(true);
        $this->reader->open($this->file);
    }
    public function transformRow($row)
    {
        $array = [
            'account' => '*********',
            'acctname' => '*********',
            'stmtdate' =>Carbon::parse( $this->date ),
            'supinvpk' => $row[7], //INVOICE_NU
            'invtype' => 'INVOICE',
            'tax' =>0, //actual_wei
            'invtotalc' => $row[8], //net_charge
            'ordnum' => $row[0], //track_num
            'descript' => $row[2], //SERVICE_TY
            'supinvdate' => Carbon::parse($row[1]), //SHIPMENT_D
            'when' => Carbon::parse($row[1]), //INVOICE_DA
            'totalamt' =>  $row[8], //ORIGINAL_A
            'bankdate' => Carbon::parse($row[1]), //SHIPMENT_D
            'sup_ord_id' =>  'Type:'.$row[2].' Payor:'.$row[5], //GROUND_SER
            'ponumber' => $row[4], //RMA_
            'invpk' => 0,
            'supstmntid' =>  $this->supstmntid  //INVOICE_NU

        ];
        Log::channel('fbsstatement')->error(print_r(combine_arr($this->header, $row), true));
        $lineitems = LineItem::where('track_num','FDX:'.$array['ordnum'])->get();
        if($lineitems->count() >0){
            $array['invpk'] = $lineitems[0]->invoice->pk;

        }

        $array['profilepk'] = $this->supplier->PK;
        $array['supname'] = 'fedex';
        $array['notes'] = 'Type:'.$row[2].' Payor:'.$row[5].' Reference:'.$row[4];

        $array['pathsupfil'] = $this->file;
        $array['resultCode'] = '';
        $array['sup'] = 'FDX';
        $array['originzip'] ='';
        return $array;
    }
    public function validateRow($row)
    {
       return true;
    }
    public function collectRow($row)
    {
        $this->rows[] = $row;
    }
}