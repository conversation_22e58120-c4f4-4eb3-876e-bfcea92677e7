<?php

namespace So<PERSON><PERSON>\FBSStatements\Library\Imports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Sophio\FBSStatements\Library\ImportStatement;

class Fedex extends ImportStatement
{
    public function retrieveFDXfile($upload_request)
    {

        $remote_filename = $upload_request->file('file')->getClientOriginalName();
        $this->local_path = $this->getLocalPrefix() . Carbon::now()->year . '_' . $remote_filename;
        if (!Storage::disk($this->storageDisk)->exists($this->getLocalPrefix())) {
            \Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.'.$this->storageDisk.'.root').'/'.dirname( $this->getLocalPrefix()),0755,true);
        }
        Storage::disk($this->storageDisk)->putFileAs($this->getLocalPrefix(), $upload_request->file('file'),Carbon::now()->year . '_' .  $remote_filename);
        $this->statement->pathsupfil = $this->local_path;
        $this->statement->save();

    }
    public function parse()
    {
        \Log::channel('fbsstatement')->info('Parsing');
        $this->template = new \Sophio\FBSStatements\Library\Templates\Fedex25(Storage::disk($this->storageDisk)->path($this->local_path), $this->statement->stmntdate, $this->statement->pk);


        $this->template->open();
        $this->template->setDatabase($this->database);
        $this->template->setProfile($this->statement->supplier);
        $this->template->processRows();
        $this->flushToDB();
        $this->template->close();
    }
}