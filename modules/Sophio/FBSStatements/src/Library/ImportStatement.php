<?php

namespace So<PERSON><PERSON>\FBSStatements\Library;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Sophio\Common\Models\FBS\StatementHeader;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;

class ImportStatement
{
    protected Supplier $supplier;
    protected string $local_path;
    protected string $storageDisk = 'fbs';
    protected string $database;
    protected $settings;
    protected $localPrefix;
    protected Carbon $date;
    protected $table = 'wws_statements_raw';
    protected StatementHeader $statement;
    protected $template;

    public function __construct(StatementHeader $statementHeader, Settings $settings, $database)
    {
        $this->statement = $statementHeader;
        $this->database = $database;
        $this->settings = $settings;
        $this->localPrefix = 'statement/' . $this->statement->supplier->PK . "/import/";

    }
    public function getUrl()
    {
        return Storage::disk($this->storageDisk)->url($this->local_path);
    }
    public function setLocalPath($path)
    {
        $this->local_path = $path;
        $this->statement->pathsupfil = $this->local_path;
        $this->statement->save();
    }
    public function setStorageDisk($disk)
    {
        $this->storageDisk = $disk;
    }

    public function getStorageDisk()
    {
        return $this->storageDisk;
    }

    public function getLocalPrefix()
    {
        return $this->localPrefix;
    }


    public function setLocalPrefix($value)
    {
        $this->localPrefix = $value;
    }
    public function flushToDB(): void
    {
        \Log::channel('fbsstatement')->info('flushToDB');
        DB::disableQueryLog();

        $i = 0;
        $bulk = [];

        foreach ($this->template->getRows() as $row) {
            $row['pkstmnt'] = $this->statement->pk;
            $row['supstmntid'] = $this->statement->pk;
            $bulk [] = $row;
            if ($i >= 10) {

                DB::table($this->database . '.' . $this->table)->insert($bulk);
                $i = 0;
                $bulk = [];
            }
            $i++;
        }
        if (count($bulk) > 0) {
            DB::table($this->database . '.' . $this->table)->insert($bulk);
        }
        DB::enableQueryLog();
    }




    public function getTemplate()
    {
        return $this->template;
    }


}