<?php

namespace So<PERSON>o\Corcentric\Library\Jobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Corcentric\Library\CorcentricManager;

class SendTransactionJob
    implements ShouldQueue
{
    use   Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $settings;
    protected $invoicepk;
    public $timeout = 0;
    public $tries = 3;
    public function __construct($invoicepk, $settings = [])
    {
        $this->invoicepk = $invoicepk;
        $this->settings = $settings;
        $this->onQueue('longtasks');

    }

    public function handle()
    {
        $invoice = Invoice::find($this->invoicepk);
        $this->settings->setSettingsByInvoice($invoice);
        $cm = new CorcentricManager($this->settings, $invoice->store);
        $cm->setInvoice($invoice);
        if ($cm->canBeSend()) {
           $cm->submitTransactionRequest();
        }
    }
}