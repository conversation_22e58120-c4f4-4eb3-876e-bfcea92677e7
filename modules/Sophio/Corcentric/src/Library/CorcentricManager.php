<?php

namespace Sophio\Corcentric\Library;

use App\Mail\QueueJobGeneral;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Sabre\Xml\Service;
use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\PaymentDetail;
use Sophio\Common\Models\FBS\Store;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSOrder\Library\Rules\Invoice\isCompleted;
use Sophio\FBSOrder\src\Library\Repository\PaymentDetailManager;

class CorcentricManager
{
    protected Settings $settings;
    protected Store $store;
    protected $url;

    protected Invoice $invoice;
    protected $orderUID;
    protected $username;
    protected $password;
    protected $accountNum;
    protected $orderPwd;

    protected $rawRequest = "";
    protected $rawResponse = "";

    protected $returnTransaction = "";
    protected $isReturnTransaction = false;

    protected $error = "";

    public function __construct($settings, $store)
    {
        $this->settings = $settings;
        $this->store = $store;
        if ($this->settings->get('CORCENTRICENDPOINT') && $this->settings->get('CORCENTRICENDPOINT') !== "") {
            $this->url = $this->settings->get('CORCENTRICENDPOINT');
        } else {
            $this->url = config('sophio.corcentric.url');
        }

        $this->username = $this->orderUID = $this->settings->get('CORCENTRICUSERNAME');
        $this->accountNum = $this->settings->get('CORCOMMUNITYCODE') ?? 'ADN';

        $this->password = $this->orderPwd = $this->settings->get('CORCENTRICPASSWORD');
        if (config('sophio.admin.demo') === true) {
            $this->url = config('sophio.corcentric.dev_url');
            $this->username = config('sophio.corcentric.dev_username');
            $this->password = config('sophio.corcentric.dev_password');
            $this->accountNum = config('sophio.corcentric.dev_accountnum');
        }

    }


    public function setInvoice(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function setReturnTransaction($returnTransaction)
    {
        $this->returnTransaction = $returnTransaction;
        $this->isReturnTransaction = true;
    }

    public function canBeSend()
    {
        if ($this->isReturnTransaction === false) {
            $pm = PaymentDetail::where('invpk', $this->invoice->pk)->where('status', 'I')->first();
            if ($pm) {
                $this->error = "This order was already sent to Corcentric on " . $pm->created_at;
                Log::channel('fbsorder')->error($this->invoice->pk . " This order was already sent to Corcentric on " . $pm->created_at);
                return false;
            }
        } else {
            $pm = PaymentDetail::where('invpk', $this->invoice->pk)->where('method', 'R')->first();
            if ($pm) {
                $this->error = "This order was already refunded to Corcentric on " . $pm->created_at;
                Log::channel('fbsorder')->error($this->invoice->pk . " This order was already refunded to Corcentric on " . $pm->created_at);
                return false;
            }
        }
        return (new isCompleted())($this->invoice) !== false;
    }

    public function getError()
    {
        return $this->error;
    }

    public function hasError()
    {
        return $this->error == "" ? false : true;
    }

    public function createTransactionRequest()
    {
        $xml = new  Service();
        if (isset($this->invoice->seller->xml['CORCUSTOMERCODE']) && $this->invoice->seller->xml['CORCUSTOMERCODE'] !== "") {
            $customerCode = $this->invoice->seller->xml['CORCUSTOMERCODE'];
        } else {
            $customerCode = ($this->settings->get('CORCENTRICPREFIX') ?? 'AZNP') . Str::remove(['-', ' '], $this->invoice->seller->company);
        }
        $invtotal = 0;
        foreach ($this->invoice->lineitem as $k => $lineitem) {
            if (($lineitem->qty + $lineitem->qty_bo) > 0) {
                $invtotal += ($lineitem->qty + $lineitem->qty_bo) * ($lineitem->price + $lineitem->coreprice);

            }
        }
        $ProcessRequest = [

            'UserName' => $this->username,
            'Password' => $this->password,
            'corRequest' => [
                'corRequestID' => $this->store->STOREPK . '-' . $this->invoice->pk,
                'corRequestType' => 'S',
                'corVendorCode' => $this->settings->get('CORVENDORCODE') ?? 'ADNB9999',
                'corCustomerCode' => $customerCode,
                'corCommunityCode' => (config('sophio.admin.demo') === true) ? 'ADN' : ($this->settings->get('CORCOMMUNITYCODE') ?? 'ADN'),
                'corAuthorizationCode' => '',
                'corTransactionType' => 'P',
                'corTransactionNumber' => $this->isReturnTransaction === true ? $this->returnTransaction : $this->invoice->pk,
                'corTransactionDate' => Carbon::createFromTimestamp(strtotime($this->invoice->invdate))->format('Ymd'),
                'corPurchaseOrderNumber' => Str::substr($this->invoice->ponumber, 0, 22),
                'corPurchaseOrderDate' => '',
                'corTransactionAmount' => ($this->isReturnTransaction === true ? -1 : 1) * $invtotal,
                'corAuthorizationAmount' => $this->invoice->invtotal,
                'corCurrencyCode' => 'USD',
                'corPointOfSale' => [
                    'corPointOfSaleName' => '',
                    'corPointOfSaleAddress1' => '',
                    'corPointOfSaleAddress2' => '',
                    'corPointOfSaleCity' => '',
                    'corPointOfSaleStateProvince' => '',
                    'corPointOfSalePostalCode' => '',
                    'corPointOfSaleCountryCode' => '',
                ],
                'corReferences' => [
                    'corReference' => [
                        'corReferenceType' => 'DT',
                        'corReferenceValue' => 'Delivered'
                    ]
                ],
                'corSections' => [
                    'corSection' => [
                        'corSectionNumber' => 1,
                        'corComments' => [
                            'corComment' => [
                                'corSectionCommentSequence' => 1,
                                'corSectionCommentType' => 'NOT',
                                'corSectionComment' => 'Customer Ship To: ' . $this->invoice->st_name . ' - Tracking number: ' . $this->invoice->track_num
                            ]
                        ],
                        'corLineDetails' => [

                        ],
                    ],


                ],
                'corTaxes' => ''
            ]

        ];


        foreach ($this->invoice->lineitem as $k => $lineitem) {
            if (($lineitem->qty + $lineitem->qty_bo) > 0) {
                $corLineDetail = [
                    'corLineDetailSequence' => $k + 1,
                    'corLineDetailType' => 'P',
                    'corLineDetailItem' => $lineitem->sku,
                    'corLineDetailManufacturerCode' => $lineitem->wdlinecode, //@TODO linecode?
                    'corLineDetailDescription' => Str::substr($lineitem->descript, 0, 80),
                    'corLineDetailVMRSCode' => ''
                ];
                if (isset($lineitem->list1['LINECODEPS']) && $lineitem->list1['LINECODEPS'] != "") {
                    $corLineDetail['corPartCategories'] = [
                        'corPartCategory' => [
                            'corCategoryType' => 'partCat2',
                            'corCategory' => $lineitem->list1['LINECODEPS']
                        ]
                    ];
                }
                $corLineDetail['corLineDetailQuantity'] = ($this->isReturnTransaction === true ? -1 : 1) * ($lineitem->qty + $lineitem->qty_bo);
                $corLineDetail['corLineDetailUnitPrice'] = $lineitem->price;
                $corLineDetail['corLineDetailCorePrice'] = $lineitem->coreprice;
                $corLineDetail['corLineDetailFET'] = '';
                $corLineDetail['corLineDetailNotes'] = ['corLineDetailNote' => ''];
                $corLineDetail['corLineDetailUOM'] = 'EA';

                $ProcessRequest['corRequest']['corSections']['corSection']['corLineDetails'][] = ['name' => 'corLineDetail', 'value' => $corLineDetail];
            }
        }
        return $xml->write('ProcessRequest', $ProcessRequest);

    }

    public function submitTransactionRequest()
    {

        if (config('sophio.admin.demo') == true) {
            LogTrack::add('Transaction Request disabled in demo mode', 'corcentric');
            return null;
        }
        $body = $this->createTransactionRequest();
        $response = $this->execute($body);
        return $this->submitTransactionResponse($response);

    }

    public function submitTransactionResponse($response)
    {
        if ($response) {

            $result = xmlToArray(new \SimpleXMLElement($response));


            if ($result) {
                if (!isset($result['corResponse'])) {
                    LogTrack::add('No corResponse in body !', 'corcentric');
                    $this->error = 'No corResponse in body !';
                    if ($this->isReturnTransaction == true) {
                        $this->invoice->SHIPTRAKID['CORREFUNDTIME'] = Carbon::now();
                    }else{
                        $this->invoice->SHIPTRAKID['CORTIMEFAIL'] = Carbon::now();
                    }
                    $this->invoice->save();
                }
                if (!in_array($result['corResponse']['corResponseStatusCode']['value'], ['2', '3'])) {
                    $this->invoice->STORENOTES = "Corcentric responded with Status Code " . $result['corResponse']['corResponseStatusCode']['value'] . ".\n" . $this->invoice->STORENOTES;
                    $this->invoice->invstatus = '3';
                }
                $status = $result['corResponse']['corResponseStatusCode']['value'] . ' - ';
                switch ($result['corResponse']['corResponseStatusCode']['value']) {
                    case '0':
                        $status .= 'Fatal error';
                        $this->invoice->ccresult = 'DENIED';
                        break;
                    case '1':
                        $status .= 'Denied';
                        $this->invoice->ccresult = 'DENIED';
                        break;
                    case '2':
                        $status .= 'Approved/Success';
                        $this->invoice->ccresult = ($this->isReturnTransaction === true) ? 'REFUNDED' : 'APPROVED';
                        break;
                    case '3':
                        $status .= 'Approved with warning';
                        $this->invoice->ccresult = ($this->isReturnTransaction === true) ? 'REFUNDED' : 'APPROVED';
                        break;
                    case '4':
                        $status .= 'Contact Corcentric';
                        $this->invoice->ccresult = 'ONHOLD';
                        break;
                    default:
                        $status .= '???';
                        $this->invoice->ccresult = 'DENIED';
                }

                $corMessage = [];
                if (is_array($result['corResponse']['corResponseMessages']) && isset($result['corResponse']['corResponseMessages'][0]))
                    foreach ($result['corResponse']['corResponseMessages'] as $corResponseMessage) {
                        $corResponseMessageType = $corResponseMessage['corResponseMessageType']['value'] . ' - ';
                        switch ($corResponseMessage['corResponseMessageType']['value']) {
                            case '0':
                                $corResponseMessageType .= 'Fatal Error';
                                break;
                            case '1':
                                $corResponseMessageType .= 'Warning';
                                break;
                            case '2':
                                $corResponseMessageType .= 'Informational';
                                break;
                            default:
                                $corResponseMessageType .= '???';
                        }
                        $corMessage[] = $corResponseMessageType . ' - Code=' . $corResponseMessage['corResponseMessageCode']['value'] . '(' . $corResponseMessage['corResponseMessageCode']['corResponseMessageComment']['value'] . ')';
                    }
                $corMessage = implode(" ", $corMessage);
                $this->invoice->STORENOTES = 'COR response: ID=' . $result['corResponse']['corResponseID']['value'] . ', status=' . $status . '(' . $corMessage . ') at ' . Carbon::now() . "\n" . $this->invoice->STORENOTES;

                if (in_array($result['corResponse']['corResponseStatusCode']['value'], ['2', '3'])) {
                    if ($this->isReturnTransaction == true) {
                        $this->invoice->CCRESULTX = "<CORREFUNDTIME>" . Carbon::now() . "</CORREFUNDTIME>" . "\n" . $this->invoice->CCRESULTX;
                        $this->invoice->SHIPTRAKID['CORREFUNDTIME'] = Carbon::now();
                    } else {
                        $this->invoice->CCRESULTX = "<CORTIME>" . Carbon::now() . "</CORTIME>" . "\n" . $this->invoice->CCRESULTX;
                        $this->invoice->SHIPTRAKID['CORTIME'] = Carbon::now();
                    }

                    $this->invoice->CCRESULTX = "Trans Id: " . $result['corResponse']['corResponseID']['value'] . ' - When: ' . \Illuminate\Support\Carbon::now() . "\n" . $this->invoice->CCRESULTX;
                }
                $this->invoice->cctype = 'CO';
                $this->invoice->save();

                $PDM = new PaymentDetailManager($this->settings);
                return $PDM->saveCorcentricPayment($this->invoice, $result, $response, $this->rawRequest, $corMessage, $this->returnTransaction);

            } else {
                if ($this->isReturnTransaction == true) {
                    $this->invoice->SHIPTRAKID['CORREFUNDTIME'] = Carbon::now();
                }else{
                    $this->invoice->SHIPTRAKID['CORTIMEFAIL'] = Carbon::now();
                }
                $this->invoice->save();

            }
        }
        return $response;
    }

    public function execute($body, $type = 'transaction')
    {
        $this->rawRequest = $body;
        try {
            $response = Http::withOptions(['http_errors' => false])->timeout(300)->withHeaders(['Content-Type' => 'text/xml; charset=utf-8'])->withBody($body, 'text/xml')->throw()->post($this->url);
            (new SupplierLogCreate())([
                'storepk' => $this->store->STOREPK,
                'timein' => now(),
                'invpk' => $this->invoice->pk,
                'accountnum' => $this->invoice->seller->accountnum,
                'apitype' => 'COR',
                'action' => $type,
                'request' => $body,

                'clientip' => request()->server('REMOTE_ADDR'),
                'response' => $response->body()
            ]);
            if (!$response->successful()) {
                return null;
            }
            $this->rawResponse = $response->body();
            return $this->rawResponse;
        } catch (\Exception $e) {
            if (property_exists($e, 'response')) {
                $response = $e->response->body();
            } else {
                $response = $e->getMessage();
            }
            Mail::to(config('sophio.admin.mail_senders.developers'))->queue( new QueueJobGeneral([
                'subject' => 'NAT COR failed !',
                'job_name' => "walmart  shipping",
                'description' => 'For '. $this->invoice->pk. ' NAT COR failed with. '.$response
            ]));
            (new SupplierLogCreate())([
                'storepk' => $this->store->STOREPK,
                'timein' => now(),
                'invpk' => $this->invoice->pk,
                'accountnum' => $this->invoice->seller->accountnum,
                'apitype' => 'COR',
                'action' => $type,
                'request' => $body,
                'clientip' => request()->server('REMOTE_ADDR'),
                'response' => $response
            ]);
            return null;
        }
    }

    public function getRawRequest()
    {
        return $this->rawRequest;
    }

    public function getRawResponse()
    {
        return $this->rawResponse;
    }

    public function checkStatusRequest($returnTransaction = null)
    {
        $xml = new  Service();
        $ProcessRequest = [

            'UserName' => $this->username,
            'Password' => $this->password,
            'corRequest' => [
                'corRequestID' => $this->store->STOREPK . '-' . $this->invoice->pk,
                'corRequestType' => 'L',
                'corVendorCode' => $this->settings->get('CORVENDORCODE') ?? 'ADNB9999',
                'corCommunityCode' => $this->settings->get('CORCOMMUNITYCODE') ?? 'ADN',
                'corAuthorizationCode' => '',
                'corTransactionType' => 'P',
                'corTransactionNumber' => $returnTransaction ?? $this->invoice->pk,
                'corCurrencyCode' => 'USD',
                'corPointOfSale' => [

                ],
                'corReferences' => '',
                'corSections' => [
                    'corSection' => [
                        'corSectionNumber' => '1',
                        'corComments' => '',
                        'corLineDetails' => [

                        ],
                    ],


                ],
                'corTaxes' => ''
            ]

        ];
        $body = $xml->write('ProcessRequest', $ProcessRequest);
        $response = $this->execute($body, 'checkstatus');
        return $this->checkStatusResponse($response, $returnTransaction);
    }

    public function checkStatusResponse($response, $returnTransaction = null)
    {

        if ($response) {

            $result = xmlToArray(new \SimpleXMLElement($response));
            if ($result) {
                if (!isset($result['corResponse'])) {
                    //@TODO error
                }
                if (!in_array($result['corResponse']['corResponseStatusCode']['value'], ['2', '3'])) {
                    $this->invoice->invstatus = '3';
                    $this->invoice->STORENOTES = "(check)Corcentric responded with Status Code " . $result['corResponse']['corResponseStatusCode']['value'] . ".\n" . $this->invoice->STORENOTES;
                }
                $status = $result['corResponse']['corResponseStatusCode']['value'] . ' - ';
                switch ($result['corResponse']['corResponseStatusCode']['value']) {
                    case '0':
                        $status .= 'Fatal error';
                         $this->invoice->ccresult = 'DENIED';
                        break;
                    case '1':
                        $status .= 'Denied';
                        $this->invoice->ccresult = 'DENIED';
                        break;
                    case '2':
                        $status .= 'Approved/Success';
                            $this->invoice->ccresult = ($returnTransaction != null) ? 'REFUNDED' : 'APPROVED';

                        break;
                    case '3':
                        $status .= 'Approved with warning';
                        $this->invoice->ccresult = ($returnTransaction != null) ? 'REFUNDED' : 'APPROVED';
                        break;
                    case '4':
                        $status .= 'Contact Corcentric';
                        $this->invoice->ccresult = 'ONHOLD';
                        break;
                    default:
                        $status .= '???';
                        $this->invoice->ccresult = 'DENIED';
                }

                $corMessage = [];
                if (is_array($result['corResponse']['corResponseMessages']) && isset($result['corResponse']['corResponseMessages']['corResponseMessage']))
                    foreach ($result['corResponse']['corResponseMessages'] as $corResponseMessage) {
                        $corResponseMessageType = $corResponseMessage['corResponseMessageType']['value'] . ' - ';
                        switch ($corResponseMessage['corResponseMessageType']['value']) {
                            case '0':
                                $corResponseMessageType .= 'Fatal Error';
                                break;
                            case '1':
                                $corResponseMessageType .= 'Warning';
                                break;
                            case '2':
                                $corResponseMessageType .= 'Informational';
                                break;
                            default:
                                $corResponseMessageType .= '???';
                        }
                        $corMessage[] = $corResponseMessageType . ' - Code=' . $corResponseMessage['corResponseMessageCode']['value'] . '(' . $corResponseMessage['corResponseMessageComment']['value'] . ')';
                    }
                $corMessage = implode(" ", $corMessage);
                if (in_array($result['corResponse']['corResponseStatusCode']['value'], ['2', '3'])) {
                    if ($this->isReturnTransaction == true) {
                        if (!Str::contains($this->invoice->CCRESULTX, 'CORREFUNDTIME', true)) {
                            $this->invoice->CCRESULTX = "<CORREFUNDTIME>" . Carbon::now() . "</CORREFUNDTIME>" . "\n" . $this->invoice->CCRESULTX;
                        }

                    } else {
                        if (!Str::contains($this->invoice->CCRESULTX, 'CORTIME', true)) {
                            $this->invoice->CCRESULTX = "<CORTIME>" . Carbon::now() . "</CORTIME>" . "\n" . $this->invoice->CCRESULTX;
                        }
                    }
                    if (!Str::contains($this->invoice->CCRESULTX, 'Trans Id', true)) {
                        $this->invoice->CCRESULTX = "Trans Id: " . $result['corResponse']['corResponseID']['value'] . ' - When: ' . \Illuminate\Support\Carbon::now() . "\n" . $this->invoice->CCRESULTX;
                    }
                }
                $this->invoice->STORENOTES = '(check) COR response: ID=' . $result['corResponse']['corResponseID']['value'] . ', status=' . $status . '(' . $corMessage . ') at ' . Carbon::now() . "\n" . $this->invoice->STORENOTES;
                Log::channel('corcentric')->error($this->invoice->STORENOTES);
                $this->invoice->save();
            }
        }
        return $result ?? null;
    }
}