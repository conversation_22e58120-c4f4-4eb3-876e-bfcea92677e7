OrderLink\PurchaseOrderRequest\WrencheadCentral\WrencheadCentralAType\OrderAType\HeaderAType:
    properties:
        username:
            expose: true
            access_type: public_method
            serialized_name: username
            accessor:
                getter: getUsername
                setter: setUsername
            xml_attribute: true
            type: string
        password:
            expose: true
            access_type: public_method
            serialized_name: password
            accessor:
                getter: getPassword
                setter: setPassword
            xml_attribute: true
            type: string
        provider:
            expose: true
            access_type: public_method
            serialized_name: provider
            accessor:
                getter: getProvider
                setter: setProvider
            xml_attribute: true
            type: string
        type:
            expose: true
            access_type: public_method
            serialized_name: type
            accessor:
                getter: getType
                setter: setType
            xml_attribute: true
            type: string
        fillflag:
            expose: true
            access_type: public_method
            serialized_name: fillflag
            accessor:
                getter: getFillflag
                setter: setFillflag
            xml_attribute: true
            type: string
        wonumber:
            expose: true
            access_type: public_method
            serialized_name: wonumber
            accessor:
                getter: getWonumber
                setter: setWonumber
            xml_attribute: true
            type: string
        ponumber:
            expose: true
            access_type: public_method
            serialized_name: ponumber
            accessor:
                getter: getPonumber
                setter: setPonumber
            xml_attribute: true
            type: string
        delmethod:
            expose: true
            access_type: public_method
            serialized_name: delmethod
            accessor:
                getter: getDelmethod
                setter: setDelmethod
            xml_attribute: true
            type: string
        ordertotal:
            expose: true
            access_type: public_method
            serialized_name: ordertotal
            accessor:
                getter: getOrdertotal
                setter: setOrdertotal
            xml_attribute: true
            type: string
        paytransid:
            expose: true
            access_type: public_method
            serialized_name: paytransid
            accessor:
                getter: getPaytransid
                setter: setPaytransid
            xml_attribute: true
            type: string
        b2ctransid:
            expose: true
            access_type: public_method
            serialized_name: b2ctransid
            accessor:
                getter: getB2ctransid
                setter: setB2ctransid
            xml_attribute: true
            type: string
        miscCharges:
            expose: true
            access_type: public_method
            serialized_name: misc_charges
            accessor:
                getter: getMiscCharges
                setter: setMiscCharges
            xml_attribute: true
            type: string
        totalDiscAmt:
            expose: true
            access_type: public_method
            serialized_name: total_disc_amt
            accessor:
                getter: getTotalDiscAmt
                setter: setTotalDiscAmt
            xml_attribute: true
            type: string
        totalRebateAmt:
            expose: true
            access_type: public_method
            serialized_name: total_rebate_amt
            accessor:
                getter: getTotalRebateAmt
                setter: setTotalRebateAmt
            xml_attribute: true
            type: string
        currencycode:
            expose: true
            access_type: public_method
            serialized_name: currencycode
            accessor:
                getter: getCurrencycode
                setter: setCurrencycode
            xml_attribute: true
            type: string
        freighttotal:
            expose: true
            access_type: public_method
            serialized_name: freighttotal
            accessor:
                getter: getFreighttotal
                setter: setFreighttotal
            xml_attribute: true
            type: string
        insurancecost:
            expose: true
            access_type: public_method
            serialized_name: insurancecost
            accessor:
                getter: getInsurancecost
                setter: setInsurancecost
            xml_attribute: true
            type: string
        shiprefid:
            expose: true
            access_type: public_method
            serialized_name: shiprefid
            accessor:
                getter: getShiprefid
                setter: setShiprefid
            xml_attribute: true
            type: string
        shippingcarrier:
            expose: true
            access_type: public_method
            serialized_name: shippingcarrier
            accessor:
                getter: getShippingcarrier
                setter: setShippingcarrier
            xml_attribute: true
            type: string
        ccv:
            expose: true
            access_type: public_method
            serialized_name: ccv
            accessor:
                getter: getCcv
                setter: setCcv
            xml_attribute: true
            type: string
        tax:
            expose: true
            access_type: public_method
            serialized_name: tax
            accessor:
                getter: getTax
                setter: setTax
            xml_attribute: true
            type: string
        salesTaxAmt:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_amt
            accessor:
                getter: getSalesTaxAmt
                setter: setSalesTaxAmt
            xml_attribute: true
            type: string
        salesTaxPct:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_pct
            accessor:
                getter: getSalesTaxPct
                setter: setSalesTaxPct
            xml_attribute: true
            type: string
        salesTaxState:
            expose: true
            access_type: public_method
            serialized_name: sales_tax_state
            accessor:
                getter: getSalesTaxState
                setter: setSalesTaxState
            xml_attribute: true
            type: string
        shiptype:
            expose: true
            access_type: public_method
            serialized_name: shiptype
            accessor:
                getter: getShiptype
                setter: setShiptype
            xml_attribute: true
            type: string
        authkey:
            expose: true
            access_type: public_method
            serialized_name: authkey
            accessor:
                getter: getAuthkey
                setter: setAuthkey
            xml_attribute: true
            type: string
        paymethod:
            expose: true
            access_type: public_method
            serialized_name: paymethod
            accessor:
                getter: getPaymethod
                setter: setPaymethod
            xml_attribute: true
            type: string
        consignmentType:
            expose: true
            access_type: public_method
            serialized_name: consignment_type
            accessor:
                getter: getConsignmentType
                setter: setConsignmentType
            xml_attribute: true
            type: string
