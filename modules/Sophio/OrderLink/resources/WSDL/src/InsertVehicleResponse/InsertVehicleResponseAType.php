<?php

namespace OrderLinkAPI\InsertVehicleResponse;

/**
 * Class representing InsertVehicleResponseAType
 */
class InsertVehicleResponseAType
{
    /**
     * @var string $insertVehicleResult
     */
    private $insertVehicleResult = null;

    /**
     * Gets as insertVehicleResult
     *
     * @return string
     */
    public function getInsertVehicleResult()
    {
        return $this->insertVehicleResult;
    }

    /**
     * Sets a new insertVehicleResult
     *
     * @param string $insertVehicleResult
     * @return self
     */
    public function setInsertVehicleResult($insertVehicleResult)
    {
        $this->insertVehicleResult = $insertVehicleResult;
        return $this;
    }
}

