<?php

namespace OrderLinkAPI\PriceAvailabilityOE2AMResponse;

/**
 * Class representing PriceAvailabilityOE2AMResponseAType
 */
class PriceAvailabilityOE2AMResponseAType
{
    /**
     * @var string $priceAvailabilityOE2AMResult
     */
    private $priceAvailabilityOE2AMResult = null;

    /**
     * Gets as priceAvailabilityOE2AMResult
     *
     * @return string
     */
    public function getPriceAvailabilityOE2AMResult()
    {
        return $this->priceAvailabilityOE2AMResult;
    }

    /**
     * Sets a new priceAvailabilityOE2AMResult
     *
     * @param string $priceAvailabilityOE2AMResult
     * @return self
     */
    public function setPriceAvailabilityOE2AMResult($priceAvailabilityOE2AMResult)
    {
        $this->priceAvailabilityOE2AMResult = $priceAvailabilityOE2AMResult;
        return $this;
    }
}

