<?php

namespace OrderLinkAPI\PurchaseOrderResponse\WrencheadCentral;

/**
 * Class representing WrencheadCentralAType
 */
class WrencheadCentralAType
{
    /**
     * @var string $rev
     */
    private $rev = null;

    /**
     * @var int $transId
     */
    private $transId = null;

    /**
     * @var \OrderLinkAPI\PurchaseOrderResponse\Orderconf $orderconf
     */
    private $orderconf = null;

    /**
     * Gets as rev
     *
     * @return string
     */
    public function getRev()
    {
        return $this->rev;
    }

    /**
     * Sets a new rev
     *
     * @param string $rev
     * @return self
     */
    public function setRev($rev)
    {
        $this->rev = $rev;
        return $this;
    }

    /**
     * Gets as transId
     *
     * @return int
     */
    public function getTransId()
    {
        return $this->transId;
    }

    /**
     * Sets a new transId
     *
     * @param int $transId
     * @return self
     */
    public function setTransId($transId)
    {
        $this->transId = $transId;
        return $this;
    }

    /**
     * Gets as orderconf
     *
     * @return \OrderLinkAPI\PurchaseOrderResponse\Orderconf
     */
    public function getOrderconf()
    {
        return $this->orderconf;
    }

    /**
     * Sets a new orderconf
     *
     * @param \OrderLinkAPI\PurchaseOrderResponse\Orderconf $orderconf
     * @return self
     */
    public function setOrderconf(\OrderLinkAPI\PurchaseOrderResponse\Orderconf $orderconf)
    {
        $this->orderconf = $orderconf;
        return $this;
    }
}

