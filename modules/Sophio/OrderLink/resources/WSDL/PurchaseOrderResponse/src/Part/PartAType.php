<?php

namespace OrderLinkAPI\PurchaseOrderResponse\Part;

/**
 * Class representing PartAType
 */
class PartAType
{
    /**
     * @var string $partno
     */
    private $partno = null;

    /**
     * @var string $linecode
     */
    private $linecode = null;

    /**
     * @var string $mfgcode
     */
    private $mfgcode = null;

    /**
     * @var string $parttype
     */
    private $parttype = null;

    /**
     * @var int $qtyreq
     */
    private $qtyreq = null;

    /**
     * @var int $qtysup
     */
    private $qtysup = null;

    /**
     * @var string $orderno
     */
    private $orderno = null;

    /**
     * @var string $errcode
     */
    private $errcode = null;

    /**
     * @var string $errmsg
     */
    private $errmsg = null;

    /**
     * @var string $freight
     */
    private $freight = null;

    /**
     * @var string $fee
     */
    private $fee = null;

    /**
     * @var string $branch
     */
    private $branch = null;

    /**
     * @var int $quoteqtyreq
     */
    private $quoteqtyreq = null;

    /**
     * @var int $quoteqtysup
     */
    private $quoteqtysup = null;

    /**
     * @var string $quoteorderno
     */
    private $quoteorderno = null;

    /**
     * @var int $sorqtyreq
     */
    private $sorqtyreq = null;

    /**
     * @var int $sorqtysup
     */
    private $sorqtysup = null;

    /**
     * @var string $sororderno
     */
    private $sororderno = null;

    /**
     * @var \OrderLinkAPI\PurchaseOrderResponse\Comment[] $comment
     */
    private $comment = [
        
    ];

    /**
     * Gets as partno
     *
     * @return string
     */
    public function getPartno()
    {
        return $this->partno;
    }

    /**
     * Sets a new partno
     *
     * @param string $partno
     * @return self
     */
    public function setPartno($partno)
    {
        $this->partno = $partno;
        return $this;
    }

    /**
     * Gets as linecode
     *
     * @return string
     */
    public function getLinecode()
    {
        return $this->linecode;
    }

    /**
     * Sets a new linecode
     *
     * @param string $linecode
     * @return self
     */
    public function setLinecode($linecode)
    {
        $this->linecode = $linecode;
        return $this;
    }

    /**
     * Gets as mfgcode
     *
     * @return string
     */
    public function getMfgcode()
    {
        return $this->mfgcode;
    }

    /**
     * Sets a new mfgcode
     *
     * @param string $mfgcode
     * @return self
     */
    public function setMfgcode($mfgcode)
    {
        $this->mfgcode = $mfgcode;
        return $this;
    }

    /**
     * Gets as parttype
     *
     * @return string
     */
    public function getParttype()
    {
        return $this->parttype;
    }

    /**
     * Sets a new parttype
     *
     * @param string $parttype
     * @return self
     */
    public function setParttype($parttype)
    {
        $this->parttype = $parttype;
        return $this;
    }

    /**
     * Gets as qtyreq
     *
     * @return int
     */
    public function getQtyreq()
    {
        return $this->qtyreq;
    }

    /**
     * Sets a new qtyreq
     *
     * @param int $qtyreq
     * @return self
     */
    public function setQtyreq($qtyreq)
    {
        $this->qtyreq = $qtyreq;
        return $this;
    }

    /**
     * Gets as qtysup
     *
     * @return int
     */
    public function getQtysup()
    {
        return $this->qtysup;
    }

    /**
     * Sets a new qtysup
     *
     * @param int $qtysup
     * @return self
     */
    public function setQtysup($qtysup)
    {
        $this->qtysup = $qtysup;
        return $this;
    }

    /**
     * Gets as orderno
     *
     * @return string
     */
    public function getOrderno()
    {
        return $this->orderno;
    }

    /**
     * Sets a new orderno
     *
     * @param string $orderno
     * @return self
     */
    public function setOrderno($orderno)
    {
        $this->orderno = $orderno;
        return $this;
    }

    /**
     * Gets as errcode
     *
     * @return string
     */
    public function getErrcode()
    {
        return $this->errcode;
    }

    /**
     * Sets a new errcode
     *
     * @param string $errcode
     * @return self
     */
    public function setErrcode($errcode)
    {
        $this->errcode = $errcode;
        return $this;
    }

    /**
     * Gets as errmsg
     *
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Sets a new errmsg
     *
     * @param string $errmsg
     * @return self
     */
    public function setErrmsg($errmsg)
    {
        $this->errmsg = $errmsg;
        return $this;
    }

    /**
     * Gets as freight
     *
     * @return string
     */
    public function getFreight()
    {
        return $this->freight;
    }

    /**
     * Sets a new freight
     *
     * @param string $freight
     * @return self
     */
    public function setFreight($freight)
    {
        $this->freight = $freight;
        return $this;
    }

    /**
     * Gets as fee
     *
     * @return string
     */
    public function getFee()
    {
        return $this->fee;
    }

    /**
     * Sets a new fee
     *
     * @param string $fee
     * @return self
     */
    public function setFee($fee)
    {
        $this->fee = $fee;
        return $this;
    }

    /**
     * Gets as branch
     *
     * @return string
     */
    public function getBranch()
    {
        return $this->branch;
    }

    /**
     * Sets a new branch
     *
     * @param string $branch
     * @return self
     */
    public function setBranch($branch)
    {
        $this->branch = $branch;
        return $this;
    }

    /**
     * Gets as quoteqtyreq
     *
     * @return int
     */
    public function getQuoteqtyreq()
    {
        return $this->quoteqtyreq;
    }

    /**
     * Sets a new quoteqtyreq
     *
     * @param int $quoteqtyreq
     * @return self
     */
    public function setQuoteqtyreq($quoteqtyreq)
    {
        $this->quoteqtyreq = $quoteqtyreq;
        return $this;
    }

    /**
     * Gets as quoteqtysup
     *
     * @return int
     */
    public function getQuoteqtysup()
    {
        return $this->quoteqtysup;
    }

    /**
     * Sets a new quoteqtysup
     *
     * @param int $quoteqtysup
     * @return self
     */
    public function setQuoteqtysup($quoteqtysup)
    {
        $this->quoteqtysup = $quoteqtysup;
        return $this;
    }

    /**
     * Gets as quoteorderno
     *
     * @return string
     */
    public function getQuoteorderno()
    {
        return $this->quoteorderno;
    }

    /**
     * Sets a new quoteorderno
     *
     * @param string $quoteorderno
     * @return self
     */
    public function setQuoteorderno($quoteorderno)
    {
        $this->quoteorderno = $quoteorderno;
        return $this;
    }

    /**
     * Gets as sorqtyreq
     *
     * @return int
     */
    public function getSorqtyreq()
    {
        return $this->sorqtyreq;
    }

    /**
     * Sets a new sorqtyreq
     *
     * @param int $sorqtyreq
     * @return self
     */
    public function setSorqtyreq($sorqtyreq)
    {
        $this->sorqtyreq = $sorqtyreq;
        return $this;
    }

    /**
     * Gets as sorqtysup
     *
     * @return int
     */
    public function getSorqtysup()
    {
        return $this->sorqtysup;
    }

    /**
     * Sets a new sorqtysup
     *
     * @param int $sorqtysup
     * @return self
     */
    public function setSorqtysup($sorqtysup)
    {
        $this->sorqtysup = $sorqtysup;
        return $this;
    }

    /**
     * Gets as sororderno
     *
     * @return string
     */
    public function getSororderno()
    {
        return $this->sororderno;
    }

    /**
     * Sets a new sororderno
     *
     * @param string $sororderno
     * @return self
     */
    public function setSororderno($sororderno)
    {
        $this->sororderno = $sororderno;
        return $this;
    }

    /**
     * Adds as comment
     *
     * @return self
     * @param \OrderLinkAPI\PurchaseOrderResponse\Comment $comment
     */
    public function addToComment(\OrderLinkAPI\PurchaseOrderResponse\Comment $comment)
    {
        $this->comment[] = $comment;
        return $this;
    }

    /**
     * isset comment
     *
     * @param int|string $index
     * @return bool
     */
    public function issetComment($index)
    {
        return isset($this->comment[$index]);
    }

    /**
     * unset comment
     *
     * @param int|string $index
     * @return void
     */
    public function unsetComment($index)
    {
        unset($this->comment[$index]);
    }

    /**
     * Gets as comment
     *
     * @return \OrderLinkAPI\PurchaseOrderResponse\Comment[]
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * Sets a new comment
     *
     * @param \OrderLinkAPI\PurchaseOrderResponse\Comment[] $comment
     * @return self
     */
    public function setComment(array $comment = null)
    {
        $this->comment = $comment;
        return $this;
    }
}

