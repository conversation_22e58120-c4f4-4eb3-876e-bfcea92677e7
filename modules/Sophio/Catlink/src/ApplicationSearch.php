<?php

namespace So<PERSON><PERSON>\Catlink;



class ApplicationSearch
{
    private $body;
    private $settings;
    private $client;

    public function __construct(CatlinkClient $client, $settings)
    {
        $this->client = $client;
        $this->settings = $settings;
        $this->body = [

            'Header' => [
                'TransId' => time(),
                'Version' => '1.0',
                'HeaderId' => 'H' . time(),
                'GroupId' => 'G' . time(),
                'UserKey' => $settings['user'],
                'ProviderKey' => $settings['provider']
            ],
            'GroupJobsName' => 'ApplicationQuestions',
            'VehicleIdentifier' =>
                [
                    'BaseVehicleId' => 0
                ],
            'GroupJobs' => [
                [
                    'Group' => [
                        [
                            'USG' => '16',
                            'PartTypeId' => [8600],
                            'MfrCode' => ["BOS", "MOT", "WIX", "CHP"]
                        ]
                    ]
                ]
            ],
            'AppOption' => [
                "ALL_QUESTIONS",   "ASSET", "PRICE", "QUAL", "LABOR_FLAG", "URI", "ATTRIB", "ATTRIB_NOTE", "CONSOLIDATE"
            ],
            "Criterion" => [
                [
                    "Id" => 1,
                    "Attrib" => "REGION"
                ],

            ],
            "GroupBy" => ["MFR"],
            "QuestionOption" => ["QUESTION_AND_APP"]

        ];
    }

    public function setGroupJobs($groupjobs)
    {
        $this->body['GroupJobs'] = $groupjobs;
        return $this;
    }
    public function setMfrCode($mfgcode)
    {
        $this->body['MfrCode'] = $mfgcode;
        return $this;
    }
    public function setVehicle($vehicle)
    {
        $this->body['VehicleIdentifier'] = $vehicle;
        return $this;
    }
    public function setCriterion($criterions) {
        $this->body['Criterion'] = $criterions;
        return $this;
    }
    public function execute()
    {
        $this->client->setEndpoint("/v1/ApplicationSearch");
        $this->client->setMethod("POST");
        $this->client->setBody($this->body);
        $response = $this->client->execute();

        if ($response[1] == 200) {

            return json_decode($response[0],true);
        }else {
            \Log::error($response);
        }
    }
}