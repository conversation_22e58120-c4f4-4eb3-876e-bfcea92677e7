<?php

namespace So<PERSON>o\FBSFeedExporter\Library;




use Sophio\Common\Models\FBS\ProductWarehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DataFeed
{
    protected $accountnum;
    protected string $database;
    protected string $feedTable = "datafeeds";

    public function __construct($accountnum, $database , $feedTable)
    {
        $this->accountnum = $accountnum;
        $this->database = $database;
        $this->feedTable = $feedTable;
        Schema::getConnection()->setDatabaseName($this->database);
    }
    public function collectDataQuery()
    {
        return ProductWarehouse::whereHas('productfeed',function($query){
            $query->whereNotNull('part_status');
            $query->whereHas('aaiabrand');
        })->where('cost','>',0)->where('qty_avail','>',0)
            ->whereRaw("contactpk in (SELECT contactpk from sophio_fbs.marketplaces_suppliers WHERE market='B2B' )");
    }
    public function updateBySP()
    {
        DB::statement("call ".$this->database.".exportfeed(?)",[$this->accountnum]);
        $last_count =  DB::table($this->database.'.'.$this->feedTable)->where('accountnum','=',$this->accountnum)->count();
        DB::table($this->database.".dailyfeeds")->where('accountnum','=',$this->accountnum)->update(['last_update'=> date ('Y-m-d H:i:s'),'last_count'=>$last_count]);
    }
}