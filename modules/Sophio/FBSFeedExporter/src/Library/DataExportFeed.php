<?php

namespace <PERSON><PERSON><PERSON>\FBSFeedExporter\Library;

use Sophio\Common\Models\FBS\DataFeed;
use <PERSON>phio\FBSFeedExporter\Library\DataRowFormat;

class DataExportFeed
{
    protected $buyer_profile;
    protected $database;
    protected $datagrab;
    protected $export_file;

    public function __construct($buyer_profile, $database)
    {
        $this->database = $database;
        $this->buyer_profile = $buyer_profile;
    }

    public function setModel()
    {
        $model = new DataFeed();
        $model->bind($this->buyer_profile->table);
        $this->datagrab = new DataGrabber($model);
    }

    public function applyRules()
    {
         $rules =$this->buyer_profile->rules;
        foreach ($rules['require'] as $rule) {
            $this->datagrab->{'require' . ucfirst($rule['rule'])}();

        }
        foreach ($rules['set'] as $rule) {
            $this->datagrab->{'set' . ucfirst($rule['rule'])}($rule['value']);
        }
    }

    public function generateFile()
    {
        $this->export_file = new DataExportTemplate('dataexport/' . $this->buyer_profile->name . '_' . date('d:m:Y') . '_' . time() . '.csv', "fbs");
        $this->export_file->open();
        $dataformat_class = "Sophio\FBSFeedExporter\Library\DataRowFormat\\".$this->buyer_profile->dataformat;

        $formatter = new $dataformat_class();
        $headers = $dataformat_class::$HEADER;


        $this->export_file->setHeaders($headers);
        $this->i = 0;
        foreach ($this->datagrab->getModel()->cursor() as $m) {
            $this->export_file->addRow($formatter->transformRow($m));
            $this->i++;
        }
        $this->export_file->close();
        return $this->export_file;
    }
}