<?php

namespace So<PERSON><PERSON>\FBSFeedExporter\Library\Actions;

use App\Exceptions\ExceptionMailAction;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Sophio\Common\Actions\SystemLogUpdate;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\FTPSbyCurl;


class SendToFTP
{
    public function __invoke($bp, $export, $or_settings)
    {
        $messages = [];
        Log::error('Starting to send file');
        $settings = new Settings([$or_settings]);

        $settings->setSettingsByStore($bp->customer->store);
        $settings->setSettingsByCustomer($bp->customer);
        if(!isset($bp->customer->settings['FTPLOCATION']) || !isset($bp->customer->settings['FTPUSERID']) ||
            !isset($bp->customer->settings['FTPPASSWORD']) ){
            $messages[] = "FTP credentials missing. Please update!";
            (new SendMailToCustomer())($bp->customer, 'Your ' . $bp->customer->store->STORENAME . ' data feed has failed to create',$messages,config('sophio.admin.mail_senders.exporters'));
            return false;
        }
        if( $bp->customer->settings['FTPLOCATION']=="" ||
            $bp->customer->settings['FTPUSERID']=="" || $bp->customer->settings['FTPPASSWORD'] =="" ) {
            $messages[] = "FTP credentials are empty. Please update!";
            (new SendMailToCustomer())($bp->customer, 'Your ' . $bp->customer->store->STORENAME . ' data feed has failed to create',$messages,config('sophio.admin.mail_senders.exporters'));
            return false;
        }
        $connect = [
            'host' => $bp->customer->settings['FTPLOCATION'],
            'username' => $bp->customer->settings['FTPUSERID'],
            'password' => $bp->customer->settings['FTPPASSWORD'],
            'port' => (int)($bp->customer->settings['FTPPORT'] ?? 21),
            'certificate' => $bp->customer->settings['FTPCERTIFICATE'] ?? '',
            'timeout' => 60
        ];


        $ftpfilename = $bp->customer->settings['FTPFILENAME'] ?? $or_settings['filename'];
        $messages[] = "<h1>" . $bp->customer->store->STORENAME . " Data Feed " . Carbon::now()->format('Ymd') . "</h1>";

        if (Str::contains($ftpfilename, 'YYYYMMDD',true)) {
            $today = Carbon::now();
            $ftpfilename = str_ireplace('yyyymmdd', $today->format('Ymd'), $ftpfilename);
        } else {
            $ftpfilename = $bp->customer->settings['FTPFILENAME'];
        }
        Log::error('ftpfilename:' . $ftpfilename);
        try {
            if ((int)($connect['port']) == 22) {
                $storage = Storage::createSFtpDriver($connect);
            } elseif ((int)($connect['port']) == 990) {
                $storage = new FTPSbyCurl($connect);
            } else {
                $storage = Storage::createFtpDriver($connect);
            }
            foreach ($export as $file) {
                $storage->put($ftpfilename, $file->getContent());
                $messages[] = "$ftpfilename send to " . $connect['host'];

            }
            (new SystemLogUpdate()) ($or_settings['syslogid'], implode("\n",$messages).'Uploaded successfully!', SystemLogStatus::INPROGRESS);
        } catch (\Throwable $e) {
            $messages[] = "Failed sending $ftpfilename to " . $connect['host'];
            $messages[] = $e->getMessage();
            (new ExceptionMailAction())($e,['customer'=>$bp->customer,    'host' => $bp->customer->settings['FTPLOCATION'],'ftpfilename'=>$ftpfilename]);

            (new SystemLogUpdate()) ($or_settings['syslogid'], implode("\n",$messages).'Upload failed!', SystemLogStatus::FAIL);
/*
            (new SendMailToCustomer())($bp->customer, 'Your ' . $bp->customer->store->STORENAME . ' data feed has failed to create',$messages,config('sophio.admin.mail_senders.exporters'));
*/

            return false;
        }

        return true;


    }


}