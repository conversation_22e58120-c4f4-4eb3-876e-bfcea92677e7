<?php

namespace Sophio\FBSFeedExporter\Library;

use App\Mail\QueueJobGeneral;

use Illuminate\Support\Facades\Mail;


class DataFeedManager
{


    protected $database;
    protected $accountnum;

    public function __construct($accountnum , $database )
    {
        \Config::set('tenant_db', $database);
        $this->database = $database;
        $this->accountnum = $accountnum;

    }
    public function getAccoutNum()
    {
        return $this->accountnum;
    }
    public function updateFeedTable()
    {
        $exportfeed = new DataFeed($this->accountnum, $this->database, 'datafeeds');
        $exportfeed->updateBySP();
    }

    public function sendMail($emails)
    {
        Mail::to(array_merge(config('sophio.admin.mail_senders.exporters'), $emails))->queue( new QueueJobGeneral([
            'subject' => 'Data feed export for accountnum' . $this->accountnum . ' from database ' . $this->database,
            'job_name' => '',
            'description' => 'Data feed export for accountnum ' . $this->accountnum . ' from database ' . $this->database . ' finished!'
        ]));

    }
}