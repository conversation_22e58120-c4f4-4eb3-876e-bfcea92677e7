<?php

namespace So<PERSON>o\FederatedLink;

use App\Exceptions\ExceptionMailAction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FedLinkAPI
{
    protected $apikey;
    protected $url;
    protected $headers;
    protected $errors=[];
    protected $request;
    protected $response;

    public function __construct($api_key)
    {
        if (env("APP_DEMO") == true) {
            $this->url = env('FEDLINK_URL_DEV','https://api.staging.groupvan.com');
            $this->apikey = env("FEDLINK_KEY_DEV","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MjI1MTA5NjksIm5iZiI6MTcyMjUxMDk2OSwianRpIjoiOWFmZTk1M2UtNDk0MC00ZmQ3LTgwMGEtZjYwYjkxYjMzZjhhIiwiaWRlbnRpdHkiOiIxNTU3OTYiLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJpbnRlZ3JhdGlvbiI6IkZMSyIsIm1lbWJlciI6IjUwMDA2NSJ9fQ.OS-ugoep0hY7SfE-uG4HiGNzWUCGM8kFWnDTq1Q1rB0");
        } else {
            $this->url = env('FEDLINK_URL','https://api.groupvan.com');
            $this->apikey = env("FEDLINK_KEY","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MjI1MTA5NjksIm5iZiI6MTcyMjUxMDk2OSwianRpIjoiOWFmZTk1M2UtNDk0MC00ZmQ3LTgwMGEtZjYwYjkxYjMzZjhhIiwiaWRlbnRpdHkiOiIxNTU3OTYiLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJpbnRlZ3JhdGlvbiI6IkZMSyIsIm1lbWJlciI6IjUwMDA2NSJ9fQ.OS-ugoep0hY7SfE-uG4HiGNzWUCGM8kFWnDTq1Q1rB0");

        }
        $this->url = env('FEDLINK_URL','https://api.groupvan.com');
        $this->apikey = env("FEDLINK_KEY","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MjI1MTA5NjksIm5iZiI6MTcyMjUxMDk2OSwianRpIjoiOWFmZTk1M2UtNDk0MC00ZmQ3LTgwMGEtZjYwYjkxYjMzZjhhIiwiaWRlbnRpdHkiOiIxNTU3OTYiLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJpbnRlZ3JhdGlvbiI6IkZMSyIsIm1lbWJlciI6IjUwMDA2NSJ9fQ.OS-ugoep0hY7SfE-uG4HiGNzWUCGM8kFWnDTq1Q1rB0");

        $this->headers = ['Authorization' => 'Bearer ' . $this->apikey];
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function placeOrder($body)
    {
        return $this->execute($body, '/json/federated/v3_1/place_order');

    }

    public function execute($body, $endpoint)
    {

        $this->request = $body;
        try {
            $response = Http::withOptions(['http_errors' => false])->withHeaders($this->headers)->withBody(json_encode($body), 'application/json')->post($this->url . $endpoint);
            $this->response  = $response->body();
            return $response->body();
        } catch (\Throwable $e) {
            Log::channel('federatedlink')->error($e->getMessage());
            $this->response  =$e->getMessage();
            $this->errors[] = $e->getMessage();
            (new ExceptionMailAction())($e, ['body' => $body]);
        }
        return null;
    }

    public function item_inquiry($body)
    {
        return $this->execute($body, '/json/federated/v3_1/item_inquiry');

    }

    public function getRequest()
    {
        return $this->request;
    }

    public function getResponse()
    {
        return $this->response;
    }
}