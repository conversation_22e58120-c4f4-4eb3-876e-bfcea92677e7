@extends('theme::layouts.app')
@section('title',$parameters['year'].' '.strtoupper($parameters['make']).' '.strtoupper($parameters['model']))
@section('content')
    <div id="parts-catalog">
        @include('catalog-2.partials.breadcrumbs')
        <div class="row">
            <div class="col-md-12">
                <div class="row">

                    <div class="col-md-4 col-lg-3 facets">

                        <div class="navfilters card card-refine">
                            <div class="header hidden-xs" style="text-align: center">

                            </div>
                            <div class="content">
                                <div class="panel-group" id="accordion">
                                    @foreach($facets  as $facetname=>$facet)
                                        @include('catalog-2.partials.facet',['facet'=>$facet,'facetname'=>$facetname])
                                    @endforeach
                                        @foreach($questions  as $facetname=>$facet)
                                            @include('catalog-2.partials.question',['question'=>$facet,'question_id'=>$facetname])
                                        @endforeach

                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="col-md-8 col-lg-9">

                        @include('catalog-2.components.search_results')

                    </div>


                </div>

            </div>


        </div>
    </div>

@endsection

@push('after-styles')
<style>
    #zero li.availability-check {
    height: 75px !important;
    }

    .sort-by .sortby {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px;
    width: 100%;
    }

    .filter-by .filterby {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px;
    width: 100%;
    }
    [class^="icon-"]:before, [class*=" icon-"]:before {
    font-family: inherit !important;
    }
    .sort-by .sortby {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px;
    width: 100%;
    }

    .filter-by .filterby {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px;
    width: 100%;
    }
</style>
@endpush

@push('after-scripts')
    <script type="text/javascript">
        $("select.branchid").on("change", function () {
            $(".brancherror").css("visibility", "hidden");
        });

        String.prototype.trunc =
            function (n) {
                return this.substr(0, n - 1) + (this.length > n ? '&hellip;' : '');
            };

        $('.list-grid-item-part-title').each(function () {
            $(this).html($(this).text().trunc(40));
        });

        function equalheight() {
            var maxHeight = 0;
            $('.addtocartform').each(function () {
                if ($(this).height() > maxHeight)
                    maxHeight = $(this).height();
            });
            console.log(maxHeight);
            $('.addtocartform').each(function () {
                $(this).height(maxHeight);
            });
        }

        $(document).ready(function () {
            equalheight();
        });
        $(window).bind("resize", equalheight);

        function UpdateQueryString(paramKey, paramVal, uri) {
            if (!uri) uri = window.location.href;
            var re = new RegExp("([?&])" + paramKey + "=[^&#]*", "i");
            if (re.test(uri)) {
                uri = uri.replace(re, '$1' + paramKey + "=" + paramVal);
            } else {
                var separator = /\?/.test(uri) ? "&" : "?";
                uri = uri + separator + paramKey + "=" + paramVal;
            }
            return uri;
        }


        function buyOnclick(partunique) {

            if ($('#branchid_' + partunique).val() === "") {
                $('#branchid_' + partunique).toggleClass('error text-danger');

            } else {
                $('#mmbuy_' + partunique).attr('checked', true);
                return true;

            }
            return false;
        }

        $(document).ready(function () {
            $('#parts-catalog .navfilters .nav-pills a').tooltip({
                title: function () {
                    return $(this).text();
                }
            });
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function () {

            // build tabs
            //$("#sortby").selectOptions('price_asc', true);

            $(".filterby").val("", true);
            //$('#store').submit();
        });

        function UpdateQueryString(paramKey, paramVal, uri) {
            if (!uri) uri = window.location.href;
            var re = new RegExp("([?&])" + paramKey + "=[^&#]*", "i");
            if (re.test(uri)) {
                uri = uri.replace(re, '$1' + paramKey + "=" + paramVal);
            } else {
                var separator = /\?/.test(uri) ? "&" : "?";
                uri = uri + separator + paramKey + "=" + paramVal;
            }
            return uri;
        }

    </script>
@endpush