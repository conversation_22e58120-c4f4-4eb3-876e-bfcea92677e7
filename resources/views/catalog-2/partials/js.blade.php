<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js"></script>

<script type="text/javascript" src="/assets/js/modaloverlay.js"></script>

<script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/knockout/3.3.0/knockout-min.js"></script>
<script type="text/javascript"
        src="//cdnjs.cloudflare.com/ajax/libs/knockout.mapping/2.4.1/knockout.mapping.min.js"></script>
<script type="text/javascript"
        src="//cdnjs.cloudflare.com/ajax/libs/knockout-validation/2.0.2/knockout.validation.min.js"></script>
<script type="text/javascript" src="/assets/js/ko/ko-postbox.js"></script>
<script type="text/javascript" src="/assets/js/ko/ko-ajax-aces-vehicle-lookup.js"></script>
<script type="text/javascript" src="/assets/data/aces-catalog-2-make-year.js"></script>
<script type="text/javascript" src="/assets/data/aces-catalog-2-models.js"></script>
<script type="text/javascript" src="/assets/js/typeahead/typeahead.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js"></script>
<script type="text/javascript" src="/assets/js/sophio/faceted-catalog-scripts-bs3.js"></script>
<script type="text/javascript" src="/assets/js/toastr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/0.5.0/sweet-alert.min.js"></script>
<script>

    <?php
        $routes = [];
        foreach (\Route::getRoutes()->getIterator() as $route) {
            if (strpos($route->uri, 'catalog') !== false) {

                $routes[$route->getName()] = $route->uri;
            }
        }
        ?>
    const sophio = {
        'api_base_url': '{{request()->api_base_url}}',
        'api_parameters': {!! json_encode(request()->api_parameters)!!},
        'routes':{!! json_encode($routes) !!},
        'current_route':{!! json_encode(\Route::current()->uri()) !!},
        'website_parameters':{"onlymakes":'{{request()->website_parameters['onlymakes']}}'},
        'incremental_url':'search/incrementalSearch?returntype=json&clientId='+sophio.api_parameters.clientId
    }
    const search_result = {'parts':{}}
    mygarage = {};
</script>
<script type="text/javascript" src="/assets/js/sophio/functions.js"></script>
<script type="text/javascript" src="/assets/js/sophio/fitwidget.js"></script>
<script type="text/javascript" src="/assets/js/sophio/mygarage.js"></script>
<script type="text/javascript" src="/assets/js/sophio/sortorder.js"></script>
<script type="text/javascript" src="/assets/js/sophio/topsearch.js"></script>
<script type="text/javascript" src="/assets/js/sophio/questions.js"></script>

    <script>

        $(document).ready(function () {
            mygarage = new MyGarage();
            let current = mygarage.getCurrent();
            if(current!==false)
            {
                $('.vehiclable-link').each(function(){
                    $(this).attr('href',$(this).attr('href',)+"/"+current.make+"/"+current.year+"/"+current.model);
                });
            }
            @if($can_have_vehicle==true)
            if (sophio.current_route != "\/") {
                if (sophio.api_parameters.make == undefined || sophio.api_parameters.model == undefined || sophio.api_parameters.year == undefined|| sophio.api_parameters.make == "" || sophio.api_parameters.model == "" || sophio.api_parameters.year == "") {
                    $('#ymmmodal').modal('show');

                }
            }
            @endif
        });
    </script>
