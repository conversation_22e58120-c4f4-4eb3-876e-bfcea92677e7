<div class="row toolbar paging-wrapper-top mb-5">
    <!-- layout selectors -->
    <div class="col-sm-3 text-center-left">
        <div class="paging-layout-selector btn-group" role="group" aria-label="grid-layout">

            <a href="javascript:void(0)" title="Currently displaying parts in grid"
               onclick="location.href=UpdateQueryString('showas','grid','{{request()->fullURlWithQuery(['_url'=>null])}}')"
               class="btn btn-default"><i class="icon-th text-success"></i></a>
            <a href="javascript:void(0)"
               onclick="location.href=UpdateQueryString('showas','list','{{request()->fullURlWithQuery(['_url'=>null])}}')"
               title="Show Parts as Listing" class="btn btn-default"><i
                        class="icon-th-list text-danger"></i></a>

        </div>
    </div>
    <form class="form-inline col-sm-9" name="store" id="store" method="post"
          action="{{request()->fullURlWithQuery(['_url'=>null])}}">
        <input type="hidden" name="_token" value="{{ csrf_token() }}">
        <!-- sort by -->

            <div class="sort-by w-100">
                <select class="sortby" name="facetedcatalogorderby">
                    <option name="sortby" value="distance_asc" @if($parameters['orderby']=='distance_asc') selected @endif > Sort by: Distance</option>
                    <option name="sortby" value="price_asc" @if($parameters['orderby']=='price_asc') selected @endif >Sort by: Price low</option>
                    <option name="sortby" value="price_desc" @if($parameters['orderby']=="price_desc") selected @endif >Sort by: Price high</option>
                    <option name="sortby" value="weight_asc" @if($parameters['orderby']=='weight_asc') selected @endif >Sort by: Relevancy</option>
                </select>
            </div>

    </form>
</div>
<div class="row toolbar mb-0">
<!-- paging as numbering -->
    <!-- paging as numbering -->
    <div class="col-sm-6 pl-0">
<script>console.log('{{$current_page}}', '{{$per_page}}', '{{$count}}');</script>
        <p style="font-size:12px;" class="pagination-text text-muted">
            <strong>
		<span class="Hidden-phone">Showing: </span> 
			@if( ($current_page-1) * $per_page < $per_page) {{$current_page}} @else {{(($current_page-1)*12)+1}} @endif
                - 
			@if( ($current_page) * $per_page > $count) {{$count}}@else {{($current_page*12)}} @endif
                of 
			{{$count}} 
		Items
	    </strong>
        </p>
    </div>
    @if($count/$per_page>1)
        <ul class="pagination pagination-sm col-sm-6 pr-0">
            <li @if($current_page<2)class="disabled" @endif>
                @if($current_page<2)
                    <a href="javascript:void(0)" title="Previous Page"><span aria-hidden="true">«</span></a>
                @else
                    <a href="{{request()->fullURlWithQuery(['currentpage'=>$current_page-1,'_url'=>null])}}"
                       title="Previous Page"><span aria-hidden="true">«</span></a>
                @endif
            </li>
            @for($page=max(1,$current_page-5);$page<=min(ceil($count/$per_page),$current_page+5);$page++)

                @if($page == $current_page)
                    <li class="active hidden-phone"><a href="javascript:void(0);">{{$page}}</a></li>
                @else
                    <li class="hidden-phone"><a class="pagenum"
                                                href="{{request()->fullURlWithQuery(['currentpage'=>$page,'_url'=>null])}}">{{$page}}</a>
                    </li>
                @endif


            @endfor
            <li @if($current_page>=floor($count/$per_page))class="disabled" @endif>
                @if($current_page>=floor($count/$per_page))
                    <a href="javascript:void(0)" title="Next Page"><span aria-hidden="true">»</span></a>
                @else
                    <a href="{{request()->fullURlWithQuery(['currentpage'=>$current_page+1,'_url'=>null])}}"
                       title="Next Page"><span aria-hidden="true">»</span></a>
                @endif
            </li>
        </ul>
@endif










</div>

