@extends('theme::layouts.app')
@section('title',$taxonomy['title'])
@section('content')
    <div id="parts-catalog">
        @include('catalog-2.partials.breadcrumbs')
        <div class="page-header">
            <h1>Select the {{$taxonomy['current']}} of your {{$taxonomy['parent']}}:</h1>
        </div>
        <div id="modelcolumn">
            <div class="row">
                @foreach($props as $prop)
                    <div class="col-xs-6 col-sm-3 col-md-3">
                        <a class="modelname-link"
                           href="{{sprintf($taxonomy['url'],$prop['id'])}}"
                           title="{{$prop['name']}}">
                            <h4 class="modelname text-center">
                                {{sprintf($taxonomy['naming'],$prop['name'])}}<br>

                            </h4>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
        @include('catalog-2.components.bottom_search_links')
    </div>
@endsection