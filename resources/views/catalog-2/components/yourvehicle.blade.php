@if(session('vehicle')!="")
    <div class="page-header catalog-title">
        <div class="thumbnail" style="position: relative;overflow: visible">
                        <span class=""
                              style="position:absolute;left:0;top:0;bottom:0; background-color: #A7a7a7; vertical-align: baseline;padding:4px; color:white;font-weight: bold">Your Vehicle: </span>
            <span style="margin-left:140px"><strong>{{request()->website_parameters['vehicle']['name']}}</strong>  <span
                        style="font-size:12px; margin-left:16px">
@if(isset(request()->website_parameters['vehicle']['extended_ids']['values']) && count(request()->website_parameters['vehicle']['extended_ids'])>0)
    {{implode(';',array_values(request()->website_parameters['vehicle']['extended_ids']['values']))}}
    @endif
                    <a class="text-danger"
                                                                    href="/catalog-2/"><i
                                class="icon icon-remove"></i> Click here to choose a new vehicle</a></span>
		</span>
        </div>
    </div>
@endif