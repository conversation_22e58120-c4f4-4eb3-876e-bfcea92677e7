<a id="universal-check-the-fit-link" class="d-none" href=""><i class="fa fa-exclamation-triangle"></i></a>
<div class="checkFitResponseLog" style="display:none"></div>
<!-- Modal -->
<div class="modal fade" id="widget-product-fit-locator" tabindex="-1" role="dialog"
     aria-labelledby="widget-product-fit-locatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">×</span></button>
                <h4 class="modal-title part-title mb-3" id="widget-product-fit-locatorModalLabel"></h4>
                <span class="part-number-title"><small>Part Number:&nbsp;</small><strong></strong></span> &nbsp; &nbsp;
                <span class="manufacturer-title"><small>Manufacturer: &nbsp;</small><strong></strong></span>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-7">
                        <div class="alert alert-info notification-fit-locator "style="display:none" id="fit-locator-choose-vehicle">
                            <h6 class="alert-heading"><i class="icon icon-exclamation"></i> Click on a vehicle from your garage or
                                select a new vehicle to check the fitment for the part below.</h6></div>
                        <div id="fit-locator-cars"></div>

                        <div class="alert alert-info notification-fit-locator" style="display:none"id="fit-locator-no-vehicle">
                        	<h6 class="alert-heading"><i class="icon icon-wrench"></i> Garage</h6>
                                	<p>You have no vehicles in your online garage. As you search, vehicles will automatically be added to your garage. Get started by selecting a vehicle.</p>
			</div>

                    </div>
                    <div class="col-sm-5">
                        <legend>Choose Vehicle</legend>
                        <div id="product-fit-locator">
                            <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css"
                                  rel="stylesheet">


                            <form method="get" id="frm-vehicle-locator-afmkt" class="vehicle-locator-afmkt-vertical"
                                  action="/catalog-2/vehicle">
                                <fieldset>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: years, optionsCaption: yearSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedYear,
            valueUpdate: 'change', enable: ( years().length)" id="af-year" name="year"></select>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: makes, optionsCaption: makeSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedMake,
                valueUpdate: 'change',  enable: (selectedYear && makes().length)" id="af-make" name="make">
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" data-bind="options: models, optionsCaption: modelSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedModel,
            valueUpdate: 'change', enable:(selectedMake && models().length)" id="af-model" name="model"></select>
                                    </div>
                                    <div class="form-group" data-bind="visible:hasSubmodels">
                                        <select class="form-control" data-bind="options: submodels, optionsCaption: submodelSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedSubModel,
            valueUpdate: 'change', enable:(selectedEngine && submodels().length)" id="af-submodel"
                                                name="submodel"></select>
                                    </div>
                                    <div class="form-group" data-bind="visible:hasEngines">
                                        <select class="form-control" data-bind="options: engines, optionsCaption: engineSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedEngine,
            valueUpdate: 'change', enable:(selectedModel && engines().length)" id="af-engine" name="engine"></select>
                                    </div>

                                    <div class="form-group" data-bind="visible:hasPartTypes">
                                        <select class="form-control" data-bind="options: parttypes, optionsCaption: parttypeSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedPartType,
            valueUpdate: 'change', enable: (selectedSubModel && parttypes().length)" id="af-parttype"
                                                name="parttype"></select>
                                    </div>
                                    <div class="form-group">
                                        <button type="button" id="afmkt-submit" class="btn btn-primary"
                                                disabled="disabled"
                                                data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle">
                                            Show Parts
                                        </button>
                                    </div>
                                </fieldset>
                            </form>


                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">Close &amp; Return to Part listing
                </button>
            </div>
        </div>
    </div>
</div>

@push('after-styles')
<link rel="stylesheet" type="text/css" href="/assets/css/toastr.min.css" />

@endpush
