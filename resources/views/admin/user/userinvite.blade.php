@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">

                    <div class="card-title"> <h2>Invite user</h2>

                    </div>

                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 bold-labels">
 <p>You are about to send an email to {{$user->email}}</p>
                                <form method="POST" id="myform" enctype="multipart/form-data"
                                      action=""
                                      id="myform">
                                    @csrf




                                    <div class="card-footer">

                                        <input type="submit" class="btn btn-primary"
                                               value="Invite">&nbsp;
                                    </div>
                                </form>

                        </div>


                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header">

                    <div class="card-title"> Invites</div>

                </div>
                <div class="card-body">
                    @if(count($invites)>0)
                    <div class="row">

                        <table class="table">
                            <tr>
                                <th>Date</th>

                            </tr>
                            @foreach($invites as $invite)
                                <tr>
                                    <td>
                                     {{$invite->created_at}}
                                    </td>

                                </tr>

                            @endforeach
                        </table>

                    </div>
                    @else
                        <p>No invites sent</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });
        $('.datatabletable').dataTable({
            order: [], pageLength: 1000, dom: 'Bfrtip',

        });
    </script>
@endpush