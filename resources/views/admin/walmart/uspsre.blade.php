@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <select name="when" class="form-select">
                                @foreach(getWhens() as $when)
                                    <option value="{{$when}}"   @if(request()->get('when')??'LASTMONTH'==$when) selected @endif>{{$when}}</option>

                                @endforeach
                            </select>
                        </div>

                    </div>
                </div>
            </div>
        </div>
            <div class="col-12">
                <div class="card">
                    <div class="card-header ">
                        <div class="card-title text-center">
                            {{$lineitems->count()}} USPS Charges, valued at {{number_format($lineitems->sum('totalcharges'),2,'.')}} for Walmart Orders - Period: {{$start}} : {{$end}}
                        </div>
                    </div>
                    <div class="card-body">

                        <div class="table-responsive">

                                <table class="table table-sm datatabletable">
                                    <thead>
                                    <tr>

                                        <th>Distributor Name</th>
                                        <th>distribuitorID</th>
                                        <th>Invoice Number</th>
                                        <th>Invoice Month</th>
                                        <th>Invoice Year</th>
                                        <th>Invoice Date</th>
                                        <th>Invoice Due Date</th>
                                        <th>Ship Date</th>
                                        <th>Walmart PO Number</th>
                                        <th>Carrier</th>
                                        <th>Carrier Method</th>
                                        <th>UPC</th>
                                        <th>Base charge</th>
                                        <th>Fuel surcharge</th>
                                        <th>Beyond zone c</th>
                                        <th>Additional surcharge</th>
                                        <th>Additional surcharge description</th>
                                        <th>Total charges</th>
                                        <th>Tracking Number</th>
                                        <th>Package Length</th>
                                        <th>Package Width</th>
                                        <th>Package Height</th>
                                        <th>Actual Weight</th>
                                        <th>Billable Weight</th>
                                        <th>Customer name</th>
                                        <th>Customer Address 1</th>
                                        <th>Customer Address 2</th>
                                        <th>Customer City</th>
                                        <th>Customer State</th>
                                        <th>Customer Zip</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($lineitems as  $lineitem)
                                        <tr>

                                            <td></td>
                                            <td>{{$lineitem->list1['WALMARTSHIPNODE']}}</td>
                                            <td>{{$lineitem->invpk}}</td>
                                            <td>{{\Illuminate\Support\Carbon::parse($lineitem->invoice->invdate)->monthName}}</td>
                                            <td>{{\Illuminate\Support\Carbon::parse($lineitem->invoice->invdate)->year}}</td>
                                            <td>{{\Illuminate\Support\Carbon::parse($lineitem->invoice->invdate)->format('m/d/Y')}}</td>
                                            <td></td>
                                            <td>{{\Illuminate\Support\Carbon::parse($lineitem->list1['TRACKINGTIME'])->format('m/d/Y')}}</td>
                                            <td>{{$lineitem->invoice->ponumber}}</td>
                                            <td>USPS</td>
                                            <td>{{$lineitem->invoice->service}}</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{number_format($lineitem->totalcharges,2,'.')}}</td>
                                            <td>{{explode(':',$lineitem->track_num)[1]}}</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{number_format($lineitem->actual_weight,2,'.')}}</td>
                                            <td>0</td>
                                            <td>{{$lineitem->invoice->st_name}}</td>
                                            <td>{{$lineitem->invoice->st_addr}}</td>
                                            <td>{{$lineitem->invoice->st_addr2}}</td>
                                            <td>{{$lineitem->invoice->st_city}}</td>
                                            <td>{{$lineitem->invoice->st_state}}</td>
                                            <td>{{$lineitem->invoice->st_zip}}</td>
                                        </tr>
                                    @endforeach

                                    </tbody>
                                    <tfoot>
                                    <tr class="fw-bold">
                                        <td>Total</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>{{number_format($lineitems->sum('totalcharges'),2,'.')}}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>{{number_format($lineitems->sum('actual_weight'),2,'.')}}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>

                                    </tr>
                                    </tfoot>
                                </table>


                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endsection

        @section('after_styles')
            @include('admin.inc.css')

        @endsection

        @push('after_scripts')
            @include('admin.inc.js')


            <script type="text/javascript"
                    src="/assets/js/sophio/utils.js"></script>
            <script>
                $(document).ready(function () {
                    var settings = {};
                    $('select').select2();
                    $('select').on('change', function () {
                        location.href = UpdateQueryString($(this).attr('name'), $(this).val(), '{!! url()->full() !!}');
                    });
                    $('.datatabletable').dataTable({
                        order: [], pageLength: 1000, dom: 'Bfrtip',
                        searching: false, paging: false,
                        buttons: [
                            {extend: 'csv',filename:'{{'usps_reimbursement_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'}, 'excel',{extend: 'pdf',filename:'{{'usps_reimbursement_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'}, {extend: 'print',filename:'{{'usps_reimbursement_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'}
                        ],
                    });
                });
            </script>
    @endpush