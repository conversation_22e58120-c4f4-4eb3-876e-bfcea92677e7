@extends(backpack_view('blank'))

@section('content')
    <h2>
        <span class="text-capitalize">FBS  \ Returns \ Return Manifest</span>
    </h2>


    <div class="row">
        <div class="col-md-12">

            <div class="card">
                <div class="card-header">

                </div>
                <div class="card-body">

                    @if($affected>0)
                        <p>You have successfully updated {{$affected}} items on manifest {{$data['returnedid']}} with shipper: {{$data['shipper']}} and tracking number: {{$data['mtracknum']}}.</p>
                    <p>The system will send an email to {{$supplier->SETTINGS['RETURNSEMAIL']}}  with a link to download manifest {{$data['returnedid']}}.</p>

                        <a href="{{route('fbs/manifest.index',['database'=>config('tenant_db'),'returnedid'=>$manifestService->getReturnedId()])}}">Click here</a> to review, edit print and email supplier.
                    @elseif($affected==0)
                        <p>Warning!  There were no items updated (probably because the items were previously shipped)</p>
                    @else
                        <p>Required for shipped are manifest id, shipper and tracking number!</p>
                    @endif

                </div>
            </div>
            </form>
        </div>
    </div>

@endsection
