@extends(backpack_view('blank'))

@section('content')
    <div class="row">
        <div class="col">
            <a class="btn " role="button"
               href="{{  sophio_route('fbs/fbsorder.main' ,['id'=> $invoice->pk]) }}"
               class="d-print-none font-sm"><i class="la la-angle-double-left"></i> Back to order </a>

            <a class="btn " role="button"
               href="{{  sophio_route('fbs/fbsorder.whdmanualcheck' ,['id'=> $invoice->pk]) }}"
               class="d-print-none font-sm">
                <span> manual </span></a></small>
            <small>This page reflects how the automatic fulfill would solve the order.</small>
        </div>
    </div>
    <div class="row">

        <div class="col-md-12 bold-labels">
            <form method="POST">
                @csrf
                <div class="card">
                    <div class="card-header">Available suppliers found in our warehouse</div>
                    <div class="card-body">
                        @if($invoice->custtype=="WAL")
                            <div class="card-title"> Please note Walmart expects this order to be delivered by
                                {{(new \Sophio\Walmart\Actions\GetOriginalSupplier())($invoice)->getSupplierDisplayName()}}</div>
                        @endif
                        <table class="table table-sm" id="suppliersLineItems">
                            <caption style="caption-side: top">
                                <span class="btn btn-sm  btn-success cursor-default"> Suppliers that system would pick</span>
                                <span class="btn btn-sm  btn-primary cursor-default"> Suppliers with quantity</span>
                                <span class="btn btn-sm  btn-danger cursor-default"> Suppliers without quantity</span>
                            </caption>
                            <thead>
                            <tr>
                                <th style="width: 10%"></th>
                                <th style="width: 20%">Supplier</th>
                                <th style="width: 10%">Items and qty</th>
                                <th style="width: 10%">Distance</th>

                                <th style="width: 10%">Profit</th>
                                <th style="width:30%">Actions</th>
                            </tr>
                            </thead>
                            <tbody>

                            @foreach($suppliers as $contactpk=>$supplier)
                                @if($supplier->profile)
                                    <tr
                                            @if($supplier->selected==true)
                                                class="table-success  dt-hasChild parent"
                                            @elseif($supplier->sum>0)
                                                class="table-primary  dt-hasChild parent"
                                            @else
                                                class="table-danger  dt-hasChild parent"
                                            @endif
                                    >
                                        <td>

                                            <a data-bs-toggle="collapse" href="#suptable{{$contactpk}}" role="button"
                                               aria-bs-expanded="true" aria-bs-target="#suptable{{$contactpk}}"><i
                                                        class="las la-plus-square"></i></a>

                                        </td>
                                        <td>
                                            <a href="{{backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/supplier/'.$supplier->profile->PK.'/edit')}}"
                                               target="_blank">
                                                {{$supplier->profile->SUP}}
                                                {{$supplier->profile->NAME}}
                                                ( {{$contactpk}})</a>
                                        </td>
                                        <td>
                                            @foreach($itemList as $mfgsku => $item)
                                                @if(isset($item->sources[$contactpk]))
                                                    {{$item->mfgsku}} ({{$item->sources[$contactpk]->qty_found}}) <br>
                                                @endif
                                            @endforeach

                                        </td>
                                        <td>
                                            @if($supplier->profile->zipcode())
                                                {{getDistanceBetweenPoints($supplier->profile->zipcode()->latitude,$supplier->profile->zipcode()->longitude,$invoice->zipcode->latitude??0,$invoice->zipcode->longitude??0)}}</td>
                                        @endif
                                        </td>

                                        <td>
                                            {{our_format($calculateProfit($invoice,$supplier,$itemList))}}
                                        </td>
                                        <td>
                                            @if($supplier->usable==true )
                                                @if(isset($supplier->profile->SETTINGS['NOTOKTOSEND']) && strtoupper($supplier->profile->SETTINGS['NOTOKTOSEND'])==="TRUE" && !auth()->user()->hasRole('super-admin'))
                                                    Fulfillment not allowed! Check with manager!
                                                @elseif(isset($supplier->profile->SETTINGS['NOTOKTOSEND']) && strtoupper($supplier->profile->SETTINGS['NOTOKTOSEND'])==="TRUE")
                                                    <a href="{{ backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/fbsorder/whdorder?id='.$invoice->pk."&supplierpk=".$supplier->profile->PK.($settings->get('ManagerApproval')!=null?'&ManagerApproval='.$settings->get('ManagerApproval'):'').($settings->get('alternateflag')!=null?'&alternateflag='.$settings->get('alternateflag'):'')) }}">
                                                        Check and Order (Fulfillment disabled)
                                                    </a>
                                                @else
                                                    <a href="{{ backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/fbsorder/whdorder?id='.$invoice->pk."&supplierpk=".$supplier->profile->PK.($settings->get('ManagerApproval')!=null?'&ManagerApproval='.$settings->get('ManagerApproval'):'').($settings->get('alternateflag')!=null?'&alternateflag='.$settings->get('alternateflag'):'')) }}">
                                                        Check and Order
                                                    </a>
                                                @endif
                                            @else

                                                Fulfillment not allowed! Check with manager!

                                            @endif


                                            @hasrole('super-admin')

                                            @php
                                                $ipk=[];
                                                foreach($itemList as $mfgsku => $item) {
                                                    if (isset($item->sources[$contactpk])) {
                                                        if( $item->sources[$contactpk]->qty_found>0) {
                                                            $ipk[]= $item->pk;
                                                        }
                                                    }
                                                }
                                         $ipk = implode(',',$ipk); @endphp
                                            |
                                            <a href="{{sophio_route('fbs/fbsorder.getratesforsupplier',['id'=>$invoice->pk]).'?supplierpk='.$supplier->profile->PK.'&lineitempk='.$ipk}} "
                                               data-toggle="modal" data-target="#rates_modal">Check SS Rates</a>

                                            @endhasrole
                                        </td>

                                    </tr>
                                    <tr class="multi-collapse collapse hide child" id="suptable{{$contactpk}}">
                                        <td colspan="12" class="child">
                                            <table class="table  table-sm">
                                                <tr>
                                                    <th></th>
                                                    <th>MfgCode</th>
                                                    <th>Linecode</th>
                                                    <th>PartNo</th>
                                                    <th>Price</th>
                                                    <th>cost</th>
                                                    <th>core</th>
                                                    <th>desc</th>
                                                    <th>qtyavail</th>
                                                    <th>alternateflag</th>
                                                    <th>Branches</th>

                                                </tr>
                                                @foreach($itemList as $mfgsku => $item)
                                                    @if(isset($item->sources[$contactpk]))
                                                        @if($item->sources[$contactpk]->rtsc)
                                                            <tr>

                                                                <td>
                                                                    @if(isset($item->sources[$contactpk]->rtsc['attributes']['mfgcode']) && isset($productInfo[mkMgfSku($item->sources[$contactpk]->rtsc['attributes']['mfgcode'],$item->sources[$contactpk]->rtsc['attributes']['partno'])])
                                             && $productInfo[mkMgfSku($item->sources[$contactpk]->rtsc['attributes']['mfgcode'],$item->sources[$contactpk]->rtsc['attributes']['partno'])]->getImage()!='')
                                                                        <img src="{{getImagePath($productInfo[mkMgfSku($item->sources[$contactpk]->rtsc['attributes']['mfgcode'],$item->sources[$contactpk]->rtsc['attributes']['partno'])]->getImage())}}"
                                                                             style="max-height: 50px;max-width: 50px;width: auto;border-radius: 3px;">
                                                                    @endif
                                                                </td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['mfgcode']??''}}</td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['linecode']??''}}</td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['partno']??''}}</td>
                                                                <td>{{$item->price}}</td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['cost']}}</td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['core']}}</td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['desc']}}</td>
                                                                <td>

                                                                    {{$item->sources[$contactpk]->rtsc['attributes']['qtyavail']}}
                                                                    @if($item->sources[$contactpk]->rtsc['branchqty']!=0)
                                                                        (+    {{$item->sources[$contactpk]->rtsc['branchqty']}}
                                                                        )
                                                                    @endif
                                                                </td>
                                                                <td>{{$item->sources[$contactpk]->rtsc['attributes']['alternateflag']}}</td>
                                                                <td>@if(isset($item->sources[$contactpk]->rtsc['altbranch']))
                                                                        {{count($item->sources[$contactpk]->rtsc['altbranch'])+1}}
                                                                    @else
                                                                        1
                                                                    @endif</td>
                                                            </tr>

                                                        @endif
                                                    @endif
                                                @endforeach
                                            </table>

                                        </td>
                                    </tr>
                                @else
                                    <tr>
                                        <td colspan="4">Bad supplier:{{$contactpk}}</td>
                                    </tr>
                                @endif
                            @endforeach

                            </tbody>

                        </table>
                    </div>

                </div>
                @if(count($all_suppliers)>0)
                <div class="card">
                    <div class="card-header">Other suppliers</div>
                    <div class="card-body">
                        <table class="table table-sm" id="suppliersLineItems2">
                            <tr>
                                <th style="width: 10%"></th>
                                <th style="width: 20%">Supplier</th>
                                <th style="width: 10%">Items with qty</th>
                                <th style="width: 10%">Distance</th>
                                <th style="width: 10%">Profit</th>
                                <th style="width:30%">Actions</th>
                            </tr>
                            </thead>
                            <tbody>

                            @foreach($all_suppliers as $supplier)
                                @if(!in_array($supplier->contactpk,array_keys($suppliers)))
                                    <tr
                                            @if($supplier->selected==true)
                                                class="table-success  dt-hasChild parent"
                                            @elseif($supplier->sum>0)
                                                class="table-primary  dt-hasChild parent"
                                            @else
                                                class="table-danger  dt-hasChild parent"
                                            @endif
                                    >
                                        <td>

                                            <a data-toggle="collapse" href="#suptable{{$supplier->contactpk}}"
                                               role="button"
                                               aria-expanded="true" aria-controls="suptable{{$supplier->contactpk}}"><i
                                                        class="las la-plus-square"></i></a>

                                        </td>
                                        <td>
                                            <a href="{{backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/supplier/'.$supplier->PK.'/edit')}}"
                                               target="_blank">
                                                {{$supplier->SUP}}
                                                {{$supplier->NAME}}
                                                ( {{$supplier->contactpk}})</a>
                                        </td>
                                        <td>

                                        </td>
                                        <td>{{($supplier->zipcode()) ?getDistanceBetweenPoints($supplier->zipcode()->latitude,$supplier->zipcode()->longitude,$invoice->zipcode->latitude??0,$invoice->zipcode->longitude??0):''}}</td>
                                        <td>

                                        </td>
                                        <td>
                                            @if(isset($supplier->SETTINGS['NOTOKTOSEND']) && strtoupper($supplier->SETTINGS['NOTOKTOSEND'])==="TRUE" && !auth()->user()->hasRole('super-admin'))
                                                Fulfillment not allowed! Check with manager!
                                            @else
                                                <a href="{{ backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/fbsorder/whdorder?id='.$invoice->pk."&supplierpk=".$supplier->PK.($settings->get('ManagerApproval')!=null?'&ManagerApproval='.$settings->get('ManagerApproval'):'').($settings->get('alternateflag')!=null?'&alternateflag='.$settings->get('alternateflag'):'')) }}">
                                                    Check and Order
                                                </a>
                                            @endif

                                        </td>

                                    </tr>

                                @endif

                            @endforeach
                            </tbody>

                        </table>
                    </div>

                </div>
               @endif
            </form>

        </div>
    </div>
    <div class="row">
        <div class="col-md-12 bold-labels">

            <form method="POST">
                @csrf
                <div class="card">
                    <div class="card-header">Line items</div>
                    <div class="card-body">
                        @if($valid && isset($result['resolved']) && $result['resolved']===true && count($result['fulfilable'])>0  )

                            @if (!(in_array($invoice->invstatus, [ 'H','R','9','8','4'])))
                                <div class="alert alert-info">This invoice can be automatically fulfilled ,but it will
                                    not be picked up by system because of it's status: {{$invoice->statusText()}}
                                    ({{$invoice->invstatus}})
                                </div>
                            @else
                                <div class="alert alert-success">This invoice can be automatically fulfilled</div>
                            @endif

                        @else
                            <div class="alert alert-warning">This invoice cannot be automatically fulfilled
                                {{!$valid && count($errors)>0?'('.implode('|',$errors).')':''}}
                                @if(request()->get('ManagerApproval')=='')
                                    <a href="{{url()->full()}}?ManagerApproval=2">Override restrictions</a>
                                @endif
                            </div>
                        @endif
                        <table class="table table-sm">
                            <tr>
                                <th></th>
                                <th></th>
                                <th>SKU</th>
                                <th>Manufacturer</th>
                                <th>Mfg/linecode</th>
                                <th>Description</th>
                                <th>Dimensions(W/H/L)</th>
                                <th>Weight</th>
                                <th>Ordered qty</th>
                                <th>Fulfilled qty</th>

                                <th>Price</th>
                                <th>Item total</th>

                                <th>Fillable</th>
                                <th>Status</th>
                            </tr>
                            @foreach($invoice->lineitem as $lineitem)
                                <tr>
                                    <td>

                                        <a data-toggle="collapse" href="#table{{$lineitem->pk}}" role="button"
                                           aria-expanded="true" aria-controls="table{{$lineitem->pk}}"><i
                                                    class="las la-plus-square"></i></a>

                                    </td>
                                    <td>
                                        @if(isset($lineitem->product->image) && $lineitem->product->image->getOriginalAttribute()!="")
                                            <img src="{{getImagePath($lineitem->product->image->getOriginalAttribute())}}"
                                                 style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                             @elseif($lineitem->image!=="")
                                                <img src="{{getImagePath($lineitem->image)}}"
                                                     style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                            @endif
                                    </td>
                                    <td>{{$lineitem->sku}}<br>

                                        <small>{{$lineitem->cat}} /<br> {{$lineitem->subcat}}</small></td>
                                    <td>{{$lineitem->mfr}}</td>
                                    <td>{{$lineitem->linecode}} / {{$lineitem->wdlinecode}}</td>
                                    <td>{{$lineitem->descript}}</td>
                                    <td>{{$lineitem->skuwidth}}/{{$lineitem->skuheight}}/{{$lineitem->skulength}}</td>
                                    <td>{{$lineitem->weight}}</td>

                                    <td>{{$lineitem->qty_ord}}</td>
                                    <td>{{$lineitem->qty}}</td>

                                    <td>{{$lineitem->price}}</td>
                                    <td>{{$lineitem->itemtotal}}</td>

                                    <td>
                                        @if($valid && $result['resolved'] && isset($result['fulfilable']) && in_array($lineitem->pk,$result['fulfilable']))
                                            <span type="button" class="btn btn-sm btn-success  cursor-default">Auto
                                                @php
                                                    $sups = 0;
                                                    if(isset($itemList[$lineitem->pk]))
                                                    foreach($itemList[$lineitem->pk]->sources as $w) {
                                                                if($w->qty_found>=$itemList[$lineitem->pk]->qty_req && $w->cost>0) {
                                                                    $sups++;
                                                                }
                                                    }
                                                @endphp <span class="badge badge-light">{{($sups)}}     </span></span>
                                        @else
                                            @php
                                                $buy = 0;
                                                 if(isset($itemList[$lineitem->pk]))
                                                foreach($itemList[$lineitem->pk]->sources as $w) {
                                                            if($w->qty_found>=$itemList[$lineitem->pk]->qty_req && $w->cost>0) {
                                                                $buy++;
                                                            }
                                                }
                                            @endphp
                                            @if($buy>0)
                                                <span type="button" class="btn btn-sm btn-warning cursor-default"> Manual <span
                                                            class="badge badge-light">{{($buy)}}     </span></span>
                                            @else
                                                <span type="button" class="btn btn-sm btn-danger cursor-default"> Unfillable</span>
                                            @endif

                                        @endif
                                    </td>
                                    <td>
                                        @if((new \Sophio\FBSOrder\Library\Rules\Lineitem\isOrdered())($lineitem) && $lineitem->supplier)
                                            Ordered from <a
                                                    href="{{backpack_url('dbs/'.\Illuminate\Support\Facades\Config::get('tenant_db').'/fbs/supplier/'.$lineitem->supplier->PK.'/show')}}">
                                                {{$lineitem->supplier->NAME}}
                                                ( {{$lineitem->supplier->contactpk}})</a> |
                                            <a href="{{backpack_url('dbs/' . \Illuminate\Support\Facades\Config::get('tenant_db'). '/fbs/fbsorder/getratesforsupplier?id='.$invoice->pk.'&supplierpk='.$lineitem->supplier->PK)}}"
                                               data-toggle="modal" data-target="#rates_modal">SS Rates</a>

                                        @else
                                            @if((new \Sophio\FBSOrder\Library\Rules\Lineitem\checkCancelled)($lineitem))
                                                Cancelled by supplier!
                                            @else
                                                @if($lineitem->sup_ord_id!="")
                                                    Attempted from
                                                    @if($lineitem->supplier)
                                                        <a
                                                                href="{{backpack_url('dbs/'.\Illuminate\Support\Facades\Config::get('tenant_db').'/fbs/supplier/'.$lineitem->supplier->PK.'/show')}}">
                                                            {{$lineitem->supplier->NAME}}
                                                            ( {{$lineitem->supplier->getProfileValue('WHMCSCONTACTPK')}}
                                                            )</a>
                                                    @endif, but failed
                                                @else
                                                    Not ordered yet
                                                @endif
                                            @endif

                                        @endif
                                    </td>
                                </tr>

                                <tr class="multi-collapse collapse hide" id="table{{$lineitem->pk}}">
                                    <td colspan="14">
                                        <div class="row justify-content-md-center">
                                            <div class="col-md-9">
                                                <table class="table  table-sm">
                                                    <tr>
                                                        <th>PK</th>
                                                        <th>Contactpk</th>
                                                        <th>WHSE</th>
                                                        <th>Supplier order id</th>
                                                        <th>Supplier invoice id</th>
                                                        <th>Marketplace SKU</th>
                                                        <th>Marketplace ID</th>
                                                        <th>Ordered price</th>
                                                        <th>Core</th>
                                                        <th>Data feed price</th>
                                                        <th>Handling</th>
                                                        <th>Supplier cost</th>
                                                        <th>Part Share wholesale</th>
                                                        <th>Contract price</th>
                                                        <th>Total shipping charge</th>
                                                        <th>Backorder qty</th>
                                                        <th>Tracking Number</th>
                                                    </tr>
                                                    <tr>
                                                        <td>{{$lineitem->pk}}</td>
                                                        <td>{{$lineitem->contactpk}}</td>
                                                        <td>{{$lineitem->whse}}</td>
                                                        <td>{{$lineitem->sup_ord_id}}</td>
                                                        <td>{{$lineitem->supinvpk}}</td>
                                                        <td>{{$lineitem->mrktplcsku}}</td>
                                                        <td>{{$lineitem->mrktplcid}}</td>
                                                        <td>{{$lineitem->price}}</td>
                                                        <td>{{$lineitem->core}}</td>
                                                        <td>{{$lineitem->cost}}</td>
                                                        <td>{{$lineitem->handlingc}}</td>
                                                        <td>{{$lineitem->scost}}</td>
                                                        <td>{{$lineitem->nwtosophio}}</td>
                                                        <td>{{$lineitem->cprice}}</td>
                                                        <td>{{$lineitem->invtotalc }}</td>
                                                        <td>{{$lineitem->qty_bo}}</td>
                                                        <td>{{$lineitem->track_num}}</td>
                                                    </tr>
                                                </table>
                                                @if($lineitem->getRawOriginal('list1')!=='' || $lineitem->getRawOriginal('list2')!=='')
                                                    <div class="row">
                                                        @if($lineitem->getRawOriginal('list1')!=='')
                                                            <div class="col-md-6 bold-labels">

                                                                <div class="language-xml" style="max-width: 1000px">
                                                                    <pre><code> {{ str_replace('> <',">\n<",$lineitem->getRawOriginal('list1')) }}</code></pre>

                                                                </div>

                                                            </div>
                                                        @endif
                                                        @if($lineitem->getRawOriginal('list2')!=='')
                                                            <div class="col-md-6 bold-labels">

                                                                <div class="language-xml" style="max-width: 1000px">
                                                                    <pre><code> {{ str_replace('> <',">\n<",$lineitem->getRawOriginal('list2')) }}</code></pre>
                                                                </div>

                                                            </div>
                                                        @endif
                                                    </div>
                                                @endif</div>
                                        </div>
                                    </td>
                                </tr>

                            @endforeach
                        </table>
                        <small>
                            <span type="button" class="btn btn-sm btn-success cursor-default"> Can be fulfilled by system</span>
                            <span type="button"
                                  class="btn btn-sm btn-warning cursor-default"> Can be manually ordered</span>
                            <span type="button"
                                  class="btn btn-sm btn-danger cursor-default"> Quantity cannot be honored</span>
                        </small>
                    </div>
                    @if($invoice->STORENOTES!=='')

                        <div class="card-footer">
                            Store notes: <br>
                            {!! nl2br($invoice->STORENOTES) !!}

                        </div>

                    @endif
                </div>
            </form>


        </div>
    </div>
    <div class="row">
        <div class="cold-md-12">
            <div class="card">
                <div class="card-header">Log</div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        @foreach(\Sophio\Common\Services\LogTrack::get('customer') as $log)

                            <li>{{$log}}</li>
                        @endforeach
                        @foreach(\Sophio\Common\Services\LogTrack::get('invoice') as $log)
                            <li>{{$log}}</li>
                        @endforeach
                        @foreach(\Sophio\Common\Services\LogTrack::get('suppliers') as $supplier=> $log)
                            <li>{{$supplier}}</li>
                            <li>{{$log}}</li>
                        @endforeach
                        @foreach($invoice->lineitem as $lineitem)
                            @foreach(\Sophio\Common\Services\LogTrack::get('lineitem.'.$lineitem->pk) as $log)
                                <li>{{$log}}</li>
                            @endforeach
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    <div class="modal" tabindex="-1" role="dialog" id="rates_modal">
        <div class="modal-dialog modal-lg" role="document" style="min-width:60%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Rates</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    Please wait while getting rates from Shipstation, it can take a few seconds ...</p>
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    @include('admin.inc.js')

    <script>


        $(document).ready(function () {
            $('#logs-table').DataTable({
                autoWidth: false, columnsDefs: [
                    {width: "10%", targets: [0, 1, 2, 3]}, {width: "30%", targets: [4, 5]}
                ],

                orderCellsTop: true,
                initComplete: function () {
                    this.api().columns([2, 3]).every(function () {

                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                            .appendTo($(column.header()).empty())
                            .on('change', function () {
                                var val = $.fn.dataTable.util.escapeRegex(
                                    $(this).val()
                                );

                                column
                                    .search(val ? '^' + val + '$' : '', true, false)
                                    .draw();
                            });

                        column.data().unique().sort().each(function (d, j) {
                            select.append('<option value="' + d + '">' + d + '</option>')
                        });
                    });
                }
            });
            $('#rates_modal').on('show.bs.modal', function (e) {

                $(this).find('.modal-body').load(e.relatedTarget.href, function () {
                    $('.rate_table').DataTable({
                        paging: false, searching: false, order: [[5, "asc"]]
                    });
                });

            });
            $('#rates_modal').on('hide.bs.modal', function (e) {

                $(this).find('.modal-body').html('  <p>Please wait while getting rates from Shipstation...</p>');

            });
            //$('#suppliersLineItems').dataTable({order: []});
            $('[data-toggle="tooltip"]').tooltip();
            $('#ship_track_status_get').on('click', function (e) {
                e.preventDefault();
                $.ajax({
                    url: '{{backpack_url('dbs/' .  \Route::current()->parameter('database'). '/fbs/fbsorder/shiptrackstatus?id=' . $invoice->pk )}}',
                    type: 'GET',
                    dataType: 'json',
                    'success': function (res) {
                        var ul = document.createElement("ul");
                        for (var x in res) {

                            var li = document.createElement("li");
                            li.innerHTML = 'Order: ' + res[x].orderId + ': ' + res[x].status;
                            if (res[x].tracknums.length > 0) {
                                li.innerHTML = li.innerHTML + ' with track_nums: ' + res[x].tracknums.join(';');
                            }
                            ul.appendChild(li);
                        }
                        $('#ship_track_status_div').append(ul);
                    }
                });
                return false;
            });
            $('.form-check-input').on('change', function () {
                if ($(this).is(':checked')) {
                    location.href = UpdateQueryString($(this).attr('name'), $(this).val());
                } else {
                    location.href = removeURLParameter(window.location.href, $(this).attr('name'));
                }

            });
        });
    </script>
@endpush