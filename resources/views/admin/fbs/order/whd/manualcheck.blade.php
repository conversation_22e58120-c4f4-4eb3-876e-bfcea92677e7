@extends(backpack_view('blank'))

@section('content')

    <div class="row">

        <div class="col">
  <a class="btn " role="button"
                    href="{{ url(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/fbsorder/main')}}?id={{$invoice->pk}}"
                    class="d-print-none font-sm"><i class="la la-angle-double-left"></i> Back to  order  </a>
        <a class="btn btn-primary" role="button"
                href="{{sophio_route('fbs/fbsorder.whdautocheck' ,['id'=> $invoice->pk]) }}"
                class="d-print-none font-sm">
            <span> auto-check </span></a></small>

        </div>
    </div>
    <div class="row">

        <div class="col-md-12 bold-labels">
            <form method="POST">
                @csrf

                <div class="card">
                    <div class="card-header">Suppliers</div>
                    <div class="card-body">
                        <table class="table table-sm" id="suppliersLineItems2">
                            <thead>
                            <tr>
                                <th>Stock check</th>
                                <th>Buyer Postal</th>
                                <th>Supplier Postal</th>
                                <th>Supplier</th>
                                <th>Name</th>

                                <th>Distance</th>
                                <th>Product cost</th>
                                <th>Qty Avail</th>

                            </tr>
                            </thead>
                            <tbody>
                            @foreach($all_suppliers as $supplier)

                                <tr>
                                    <td>

                                        <a href="{{ backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/fbsorder/whdorder?id='.$invoice->pk."&supplierpk=".$supplier['supplier']->PK.($settings->get('ManagerApproval')!=null?'&ManagerApproval='.$settings->get('ManagerApproval'):'').($settings->get('alternateflag')!=null?'&alternateflag='.$settings->get('alternateflag'):'')) }}">
                                            Real Time Stock Check
                                        </a> |

                                        <a href="{{sophio_route('fbs/fbsorder.getratesforsupplier',['id'=>$invoice->pk]).'?supplierpk='.$supplier['supplier']->PK.'&lineitempk='.implode(',',$invoice->lineitem->pluck('pk')->toArray())}}"
                                           data-toggle="modal" data-target="#rates_modal">Check SS Rates</a>

                                    </td>
                                    <td>
                                        {{$invoice->st_zip}}
                                    </td>
                                    <td>
                                        {{$supplier['supplier']->SETTINGS['INVENTORYZIPCODE']}}
                                    </td>
                                    <td>{{$supplier['supplier']->SUP}}</td>
                                    <td>
                                        <a href="{{backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/supplier/'.$supplier['supplier']->PK.'/edit')}}"
                                           target="_blank">
                                            {{$supplier['supplier']->SUP}}
                                            {{$supplier['supplier']->NAME}}
                                            ( {{$supplier['supplier']->contactpk}})</a>
                                    </td>

                                    <td>{{($supplier['supplier']->zipcode()) ?getDistanceBetweenPoints($supplier['supplier']->zipcode()->latitude,$supplier['supplier']->zipcode()->longitude,$invoice->zipcode->latitude??0,$invoice->zipcode->longitude??0):''}}</td>

                                    <td>{{implode(',',$supplier['cost']??[])}}</td>
                                    <td>{{implode(',',$supplier['qty']??[])}}</td>

                                </tr>

                            @endforeach
                            </tbody>

                        </table>
                        <small>Customer is using

                            @if($settings->get('FULFILLMENTMODEL')=="1")
                                lowest cost model.
                            @else
                                closest model.
                        @endif</small>
                    </div>

                </div>

            </form>

        </div>
    </div>

@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    <div class="modal" tabindex="-1" role="dialog" id="rates_modal">
        <div class="modal-dialog modal-lg" role="document" style="min-width:60%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Rates</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Please wait while getting rates from Shipstation...</p>
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    @include('admin.inc.js')


    <script>

        $(document).ready(function () {
            $('#logs-table').DataTable({
                autoWidth: false, columnsDefs: [
                    {width: "10%", targets: [0, 1, 2, 3]}, {width: "30%", targets: [4, 5]}
                ],

                orderCellsTop: true,
                initComplete: function () {
                    this.api().columns([2, 3]).every(function () {

                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                            .appendTo($(column.header()).empty())
                            .on('change', function () {
                                var val = $.fn.dataTable.util.escapeRegex(
                                    $(this).val()
                                );

                                column
                                    .search(val ? '^' + val + '$' : '', true, false)
                                    .draw();
                            });

                        column.data().unique().sort().each(function (d, j) {
                            select.append('<option value="' + d + '">' + d + '</option>')
                        });
                    });
                }
            });
            $('#rates_modal').on('show.bs.modal', function (e) {

                $(this).find('.modal-body').load(e.relatedTarget.href, function () {
                    $('.rate_table').DataTable({
                        paging: false, searching: false, order: [[5, "asc"]]
                    });
                });

            });
            $('#rates_modal').on('hide.bs.modal', function (e) {

                $(this).find('.modal-body').html('  <p>Please wait while getting rates from Shipstation...</p>');

            });
            $('#suppliersLineItems2').dataTable({order: [], pageLength: 50});
            $('[data-toggle="tooltip"]').tooltip();
            $('#ship_track_status_get').on('click', function (e) {
                e.preventDefault();
                $.ajax({
                    url: '{{backpack_url('dbs/' . session('database') . '/fbs/fbsorder/shiptrackstatus?id=' . $invoice->pk )}}',
                    type: 'GET',
                    dataType: 'json',
                    'success': function (res) {
                        var ul = document.createElement("ul");
                        for (var x in res) {

                            var li = document.createElement("li");
                            li.innerHTML = 'Order: ' + res[x].orderId + ': ' + res[x].status;
                            if (res[x].tracknums.length > 0) {
                                li.innerHTML = li.innerHTML + ' with track_nums: ' + res[x].tracknums.join(';');
                            }
                            ul.appendChild(li);
                        }
                        $('#ship_track_status_div').append(ul);
                    }
                });
                return false;
            });
            $('.form-check-input').on('change', function () {
                if ($(this).is(':checked')) {
                    location.href = UpdateQueryString($(this).attr('name'), $(this).val());
                } else {
                    location.href = removeURLParameter(window.location.href, $(this).attr('name'));
                }

            });
        });
    </script>
@endpush