@extends(backpack_view('blank'))


@section('content')
    @if(( isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) &&  $supplier->SETTINGS['TRACKINGNUMBERTYPE']=='SHIPSTATIONCUSTOM')  || ($supplier->SUP!=="WHD" && $supplier->SUP!=="UAS" ) )
        <div class="row">
            <div class="card">
                <div class="card-body">
                    This supplier does not support placing orders to Shipstation!
                </div>
            </div>
        </div>
    @else
        <div class="row">
            <div class="col-md-12 bold-labels">
                <div class="card">
                    <div class="card-header">
                        <div>
                            <h3 class="card-title"><span class="bg-blue text-blue-fg btn cursor-default">Customer: {{$invoice->seller->company}}</span> <span class="btn bg-green text-green-fg cursor-default">  Market:  {{$invoice->custtype}}</span></h3>
                            <p class="card-subtitle">
                            <ul class="list-inline">
                                <li class="list-inline-item">Customer shipping settings:</li>
                                <li class="list-inline-item">Carrier: {{$invoice->seller->carrier}}</li>
                                <li class="list-inline-item">Service: {{$invoice->seller->service}}</li>

                                @if($invoice->custtype=='WAL')
                                    <li class="list-inline-item">Fedex Account for
                                        supplier: {{$invoice->seller->xml['FEDEXACCOUNT'.$supplier->PK]??'-'}}</li>
                                @else
                                    <li class="list-inline-item">Fedex
                                        Account: {{$invoice->seller->xml['FEDEXACCOUNT']??'-'}}</li>
                                @endif
                            </ul>
                            </p>
                        </div>
                        @if(backpack_user()->hasRole('super-admin'))
                        <div class="card-actions">
                            <a target="_blank" href="{{sophio_route('fbs/fbsorder.getratesforsupplier',['id'=>$invoice->pk,'supplierpk'=>$supplier->PK])}}" class="btn btn-info">Check rates</a>

                            <a href="{{sophio_route('fbs/fbsorder.testshipstation',['id'=>$invoice->pk,'supplierpk'=>$supplier->PK])}}" class="btn btn-primary">Send To Shipstation</a>
                            @if(isset($order->orderId))
                                <a href="{{sophio_route('fbs/fbsorder.deleteshipstation',['id'=>$invoice->pk,'supplierpk'=>$supplier->PK,'shipstationid'=>$order->orderId])}}" class="btn btn-danger">Delete</a>
                            @endif
                        </div>
                            @endif
                    </div>
                    <div class="card-body">
                        <ul class="list-inline">
                            <li class="list-inline-item">Supplier:
                                <a href="{{sophio_route('fbs/supplier.edit',['id'=>$supplier->PK])}}"
                                   target="_blank">{{$supplier->NAME}} ({{$supplier->PK}})</a>
                            </li>
                            @if($warehouse)
                                <li class="list-inline-item">From:
                                    {{$warehouse->originAddress->street1}} {{$warehouse->originAddress->city}} {{$warehouse->originAddress->state}}
                                    , {{$warehouse->originAddress->postalCode}}

                                </li>
                            @endif
                            <li class="list-inline-item">To:
                                {{$invoice->st_zip}} ( {{$invoice->st_city}} {{$invoice->st_state}})
                            </li>
                            <li class="list-inline-item">Weight (total):
                                {{$order->weight->value}} ({{$order->weight->units}})
                            </li>

                            <li class="list-inline-item">Dimensions (total,as packed/box):
                                Width: {{$order->dimensions->width}} Height: {{$order->dimensions->height}}
                                Length: {{$order->dimensions->length}}</li>
                            <li class="list-inline-item"></li>

                        </ul>


                    </div>
                </div>
            </div>
        </div>
    @include('admin.fbs.order.shipping.shipstation_order_partial')
    @endif
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>

        $(document).ready(function () {

            $('.rate_table').DataTable({
                paging: false, searching: false, order: [[5, "asc"]]
            });
        });

    </script>
@endpush
