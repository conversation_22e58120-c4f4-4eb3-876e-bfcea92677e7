@extends(backpack_view('blank'))


@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-body">
                    @if($cancelled)
                        <ul class="list-unstyled">

                        </ul>


                        <div class="alert alert-success" role="alert">
                            <div class="d-flex">
                                <div>

                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24"
                                         height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                         fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M5 12l5 5l10 -10"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="alert-title">Order has been cancelled successful!
                                    </h4>
                                    @foreach(\Sophio\Common\Services\LogTrack::get('invoice') as $message)
                                        <div class="text-secondary">{{$message}}</div>
                                    @endforeach
                                    @if($invoice->custtype=='TRA')
                                        <div class="alert alert-info">
                                            <p>Please login to their vendor portal and choose ‘shipment delay’ tab and tell them the reason we are cancelling.</p>
                                            <p><a href="https://vtp.tractorsupply.com/networking/servicedesk/index.jsp">Tractor Supply Transaction Vendor Portal</a></p>
                                            <p>Credentials:</p>
                                            <ul>
                                                <li>Username: {{$invoice->seller->xml['B2BUSERNAME']}}</li>
                                                <li>Password: {{$invoice->seller->xml['B2BPASSWORD']}}</li>
                                            </ul>

                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <a href="{{sophio_route('fbs/fbsorder.main',['id'=>$invoice->pk])}}" class="btn btn-primary">Back
                            to order</a>
                    @else

                        @if($canCancel || $override)
                            @if($error!=="")
                                <div class="alert alert-danger">{{$error}} </div>
                            @endif
                            <form method="POST"
                                  action="{{route('fbs/fbsorder.cancelorder',['database'=>config('tenant_db'),'id'=>$invoice->pk])}}">
                                @csrf
                                <p class="alert alert-danger"">IMPORTANT! If you want to re-route the order to another supplier, please cancel each line with "Cancel by Supplier". Cancelling here the order will trigger actions to customer like refunding or informing them of the cancellation.</p>
                                <div class="form-group">
                                    <label>Cancel order:</label>
                                    <select name="newstatus" class="form-control">
                                        <option value="4">We cancelled (Pricing)</option>
                                        <option value="5">Supplier cancelled (No Stock)</option>
                                        <option value="X">Buyer cancelled (did not want)</option>

                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Store notes:</label>
                                    <textarea class="form-control" name="note"> {{backpack_user()->name}} changed status from {{$invoice->invstatus}} at {{\Illuminate\Support\Carbon::now()}} - </textarea>
                                </div>
                                <input type="submit" class="btn btn-success" value="Continue">
                                <a href="{{sophio_route('fbs/fbsorder.main',['id'=>$invoice->pk])}}"
                                   class="btn btn-info">Back to order</a>
                            </form>

                        @else
                            @if($error!=="")
                                <div class="alert alert-danger">{{$error}} </div>
                                <a class="btn btn-danger" href="{{sophio_route('fbs/fbsorder.cancelorder',['id'=>$invoice->pk,'override'=>true])}}">Override and force cancel</a>
                            @else
                                <div class="alert alert-danger">You cannot cancel this order. Please contact support.</div>
                                @endif
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>

        $(document).ready(function () {

            $('.rate_table').DataTable({
                paging: false, searching: false, order: [[5, "asc"]]
            });
        });

    </script>
@endpush
