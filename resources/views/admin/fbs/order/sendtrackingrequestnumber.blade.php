@extends(backpack_view('blank'))


@section('content')


    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    @if($sent==null)
                        <table  class="table">
                            @foreach($profilepks as $profile=>$items)
                                <tr>
                                    <td>

                                        {{$invoice->lineitem->where('profilepk',$profile)->first()->supplier->getSupplierDisplayName()}}
                                    </td>
                                    <td>
                                        @if(isset($already[$profile]))
                                            We there already a tracking number from this supplier. Do you want to resend the request? <a href="{{sophio_route('fbs/fbsorder.sendtrackingrequestnumber',['id'=>$invoice->pk,'profilepk'=>$profile,'resend'=>true])}}">Yes</a>
                                        @else
                                        <a href="{{sophio_route('fbs/fbsorder.sendtrackingrequestnumber',['id'=>$invoice->pk,'profilepk'=>$profile])}}">Send tracking request number</a>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                    @elseif($sent==true)
                        Tracking Number Request Sent for order number: {{$invoice->pk}}
                    @else
                        Oops, we are either missing contact info or this supplier has integrated tracking.  Contact support for help if you think this message is in error.
                        <br>
                        {{$s->getError()}}
                    @endif

                </div>
            </div>
        </div>
    </div>
@endsection

