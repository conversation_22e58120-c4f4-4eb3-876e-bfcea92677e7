@extends(backpack_view('blank'))


@section('content')
    <div class="row">

        <div class="card">
            <div class="card-body">

                <h1> Order {{$invoice->pk}} from {{$invoice->seller->company}} was cancelled!</h1>

                <p>
                    <strong>
                        @switch($invoice->custtype)
                            @case('WAL')
                                You must now generate a cancellation for Walmart.  <a
                                        href="{{sophio_route('fbs/fbsorder.walmartCancel',['id'=>$invoice->pk])}}">Click
                                    here to
                                    Cancel with Walmart</a>.
                                @break
                            @case('HD')
                                You must now process a cancel confirmation  in COMMERCE HUB.<a
                                        href="{{sophio_route('fbs/fbsorder.chubConfirmation',['id'=>$invoice->pk])}}">Click
                                    here to Cancel with CHUB</a>.
                                @break
                            @case('NAT')
                                You must use our Refund Corcentric feature if this   order  was shipped already and you
                                are cancelling
                                to complete the cancellation process.
                                @break
                            @case('ZOR')
                                You must now process a cancel confirmation  for ZORO EDI.
                                @break
                            @case('TRA')
                                Tractor Supply does not allow us to automate cancellations.  Please login to their
                                vendor portal and choose ‘shipment delay’ tab and tell them the reason we are
                                cancelling.
                                <a href="https://vtp.tractorsupply.com/networking/servicedesk/index.jsp"
                                   target="_blank">Tractor Supply Transaction Vendor Portal </a>
                                @break
                            @case('B2B')
                                You must visit the {{$invoice->company}} portal and manually cancel
                                the @if($action==='lineitem')
                                    line
                                @else
                                    order
                                @endif.
                                <a target="_blank" href="{{$invoice->seller->url.$invoice->ponumber}}">B2B Portal</a>
                                @break
                            @case('AF')
                                @if(in_array($invoice->paymethod,['OA','OP']))
                                    Affiliates like this one are on open account.  As such, we refund this order simply
                                    by changing
                                    the status on the order to CANCELLED.
                                    If the affiliate uses our API they will discover cancellations when they check for
                                    tracking.
                                @else
                                    Customers who pay by CC need a refund when orders are cancelled because they are
                                    charged when the order is placed and before it is shipped.  A text message will be
                                    sent to the mobile phone number of the customer (if configured) and an email will be
                                    sent notifying them of the cancellation (if they have notifications enabled).  If
                                    the customer uses our platform for managing orders, their order will be set to NEEDS
                                    REVIEW status with a message notifying them of the cancellation.
                                @endif
                                @break
                        @endswitch
                    </strong>
                </p>
            </div>
            <div class="card-footer">
                <div class="row">
                    <div class="col-md-6 py-2 text-center"><a
                                href="{{backpack_url('dbs/'. \Route::current()->parameter('database').'/fbs/fbsorderdashboard/openorders')}}">
                            <button class="btn btn-primary">Open orders</button>
                        </a></div>
                    <div class="col-md-6 py-2 text-center">

                        <a href="{{sophio_route('fbs/fbsorder.main' ,['id'=> $invoice->pk]) }}">
                            <button class="btn btn-primary">Back to Order</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>


    </div>
@endsection

