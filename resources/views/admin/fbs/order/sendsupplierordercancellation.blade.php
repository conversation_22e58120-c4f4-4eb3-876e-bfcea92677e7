@extends(backpack_view('blank'))


@section('content')


    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    @if($sent==null)
                        <table  class="table">
                            @if(count($profilepks)==0)
                                There is currently no order made to a supplier, no cancellation to confirm.
                                @endif
                            @foreach($profilepks as $profile)
                                <tr>
                                    <td>
                                        {{$invoice->lineitem->where('profilepk',$profile)->first()->supplier->getSupplierDisplayName()}}
                                    </td>
                                    <td><a href="{{sophio_route('fbs/purchaseorder.acksupplerordercancellation',['id'=>$invoice->pk,'profilepk'=>$profile])}}">Send supplier order cancellation</a></td>
                                </tr>
                            @endforeach
                        </table>
                    @elseif($sent==true)
                      Order cancellation send to {{$supplier->getSupplierDisplayName()}} for order number: {{$invoice->pk}}
                    @else
                        Oops,something went wrong. Contact support.
                        <br>
                        {{$s->getError()}}
                    @endif

                </div>
            </div>
        </div>
    </div>
@endsection

