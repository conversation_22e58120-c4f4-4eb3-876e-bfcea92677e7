@extends(backpack_view('blank'))


@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{sophio_route('fbs/fbsorder.testorder')}}">

                        @csrf
                        <p>A random item that has qty>20, cost<10 and min qty=1 will be picked.</p>
                        <p>The order will not be automatically fulfilled, after creation it will land you on the purchase screen.</p>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Customer Id*:</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" value="{{request()->get('custpk','219357')}}"  name="custpk">

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Choose supplier*:</label>
                            <div class="col-sm-10">
                                <select name="profilepk" class="form-control" required>

                                    @foreach((new \Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList )()->get() as $profile)
                                        <option value="{{$profile->PK}}"
                                                @if($profile->PK == request()->input('pk')) selected @endif>
                                            {{$profile->PK}} {{$profile->NAME}}
                                            [{{$profile->contactpk}}]
                                        </option>
                                    @endforeach
                                </select>

                            </div>
                        </div>
                        <!--
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">FullFil:</label>
                            <div class="col-sm-10">
                                <label>Yes<input type="radio" class="form-check" value="true"  name="fullfil" checked></label>
                                <label>No<input type="radio" class="form-check" value="false"  name="fullfil"></label>
                            </div>
                        </div>
                        //-->
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Item Description:</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" value=""  name="description">
                                <small>Optional, set a description on the item.</small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Item cost:</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" value=""  name="cost">
                                <small>Optional, force cost of item.</small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">Order Note:</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" value=""  name="note">
                                <small>Optional, sets a note on invoice.</small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input type="submit" class="btn btn-success" value="Create">
                                <input type="reset" class="btn btn-info" value="Reset">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
<script>
    $(document).ready(function(){
        $('select').select2();
    })
</script>

@endpush
