@extends(backpack_view('blank'))
@section('content')


    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">
                    Supplier {{$supplier->getSupplierDisplayName()}} has unpaid statements. Please mark them paid before processing a new one!
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 bold-labels">

                            <table class="table">
                                <thead>
                                <tr>

                                    <th>Statement date</th>
                                    <th>Month</th>
                                    <th>Amount</th>
                                    <th>Statement Type</th>
                                    <th>Supplier Name</th>
                                    <th>Supplier ID</th>
                                    <th>Store id</th>
                                    <th>Debit amt</th>
                                    <th>Debit cnt</th>
                                    <th>Credit amt</th>
                                    <th>Credit cnt</th>
                                    <th>Deductions</th>
                                    <th>Supstmntid</th>
                                    <th>Variance</th>
                                    <th>When</th>
                                    <th>Sophiouser</th>
                                    <th>Confirm</th>
                                    <th>Mark paid</th>

                                    <th>Report</th>
                                    <th>Delete</th>

                                </tr>
                                </thead>
                                <tbody>
                                @foreach($statements as $statement)

                                    <tr>

                                        <td>{{$statement->stmntdate}}</td>
                                        <td>{{$statement->stmntdate->month}}</td>
                                        <td>{{$statement->amount}}</td>
                                        <td>{{$statement->stmnttype}}</td>
                                        <td>{{$statement->supname}}</td>

                                        <td>{{$statement->profilepk}}</td>

                                        <td>{{$statement->storepk}}</td>
                                        <td>{{$statement->debitamt}}</td>
                                        <td>{{$statement->debitcnt}}</td>
                                        <td>{{$statement->creditamt}}</td>
                                        <td>{{$statement->creditcnt}}</td>
                                        <td>{{$statement->deductions}}</td>
                                        <td>{{$statement->supstmntid}}</td>
                                        <td>{{$statement->variance}}</td>
                                        <td>{{$statement->when}}</td>
                                        <td>{{$statement->sophiouser}}</td>
                                        <td><a href="{{sophio_route('fbs/statement.confirmstatementpayment',[
                                        'id'=>$statement->pk,'amount'=>$statement->amount,'supstmntid'=>$statement->supstmntid
                                            ])}}">Confirm</a> </td>
                                        <td><a href="{{sophio_route('fbs/statement.marksupplierstatementpaid',[
                                        'id'=>$statement->pk,'amount'=>$statement->amount,'supstmntid'=>$statement->supstmntid
                                            ])}}">Mark Paid</a> </td>
                                        <td><a href="{{sophio_route('fbs/statement.viewreportstatement',[
                                        'id'=>$statement->pk,
                                            ])}}">View</a> </td>
                                        <td><a href="{{sophio_route('fbs/statement.delete',[
                                        'id'=>$statement->pk,
                                            ])}}">Delete</a> </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>

                        </div>
                        <div class="0 py-4 px-4 d-flex justify-content-center align-items-center flex-wrap">
                            <div class="mx-auto    align-items-center" >
                            <div class="card">
                                <div class="card-body">
                                  <a class="btn btn-red" href="{{ request()->fullUrlWithQuery(['override' => 'true']) }} ">Click here to override and import a statement for {{\Illuminate\Support\Carbon::now()->subMonths(1)->endOfMonth()->format('Y-m-d')}}  for supplier {{$supplier->getSupplierDisplayName()}}</a>
                                    &nbsp;      &nbsp;      &nbsp;
                                </div>
                                <div class="ribbon bg-red">
                                    <!-- SVG icon from http://tabler-icons.io/i/star -->


                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-alert-triangle" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10.24 3.957l-8.422 14.06a1.989 1.989 0 0 0 1.7 2.983h16.845a1.989 1.989 0 0 0 1.7 -2.983l-8.423 -14.06a1.989 1.989 0 0 0 -3.4 0z"></path>
                                        <path d="M12 9v4"></path>
                                        <path d="M12 17h.01"></path>
                                    </svg>
                                </div>
                            </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')

    @include('admin.inc.css')
@endsection

@push('after_scripts')

    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });
    </script>
@endpush