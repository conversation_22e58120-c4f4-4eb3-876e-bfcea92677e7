@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-9 bold-labels">
            @if(isset($error))
                @switch($error)
                    @case('already_paid')
                        <div class="alert alert-danger"><strong>Warning!</strong> This statement was previously paid
                            on {{$statement->paid}}.<br>
                            Approver Notes: {{$statement->notes}}
                            <br>

                        </div>
                        @break
                    @case('already_sent')
                        <div class="alert alert-danger"><strong>Warning!</strong> This statement was already sent to
                            accounting on {{$statement->pmtrequest}}!
                            Click <a style="color:blue"
                                    href="{{sophio_route('fbs/statement.sendpaymentrequesttoaccounting',['id'=>$statement->pk,'override'=>'true'])}}">here</a>
                            to reprint and send to accounting.
                            - OR -
                            Click <a style="color:blue"
                                    href="{{sophio_route('fbs/statement.confirmstatementpayment',['id'=>$statement->pk,'override'=>'true'])}}">here</a>
                            to confirm it again.
                            <br>
                        </div>

                        @break
                @endswitch
            @else
                @if($confirm==true)
                    <div class="card">
                        <div class="card-header">
                        </div>
                        <div class="card-body">
                            @if(request()->get('override'))
                                <h2>Last Step! Click <a  style="color:blue" href="{{sophio_route('fbs/statement.sendpaymentrequesttoaccounting',[
    'id'=>$statement->pk,
    'supstmntid' =>$statement->supstmntid,
    'requested'=>$requested,
    'approved'=>$statement->approved,
    'duedate'=>$duedate,
    'glaccount'=>$glaccount,
    'missingcredits'=>$missingcredits,
    'updated'=>true

    ])}}">here</a> to send an updated payment request to accounting or click back to make a change.</h2>
                                @else
                            <h2>Last Step! Click <a style="color:blue" href="{{sophio_route('fbs/statement.sendpaymentrequesttoaccounting',[
    'id'=>$statement->pk,
    'supstmntid' =>$statement->supstmntid,
    'requested'=>$requested,
    'approved'=>$statement->approved,
    'duedate'=>$duedate,
    'glaccount'=>$glaccount,
    'missingcredits'=>$missingcredits,

    ])}}">here</a> to send payment request to accounting or click back to make a change.</h2>
                                @endif
                        </div>
                    </div>
                @else
                    <form method="POST" id="myform" action="{{sophio_route('fbs/statement.confirmstatementpayment',['id'=>$statement->pk])}}"
                          id="myform">
                        @csrf

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title "> Confirm payment request
                                    for {{$statement->supplier->getSupplierDisplayName()}}</div>
                            </div>
                            <div class="card-body">

                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Statement Date:</label>
                                    <div class="col-sm-6">
                                        <input id="stmntdate" name="stmtdate" type="date" value="{{$stmntdate}}"
                                               class="form-control">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Due Date:</label>
                                    <div class="col-sm-6">
                                        <input id="duedate" name="duedate" type="date" value="{{$duedate}}"
                                               class="form-control">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Pay Supplier (please review notes):</label>
                                    <div class="col-sm-6">
                                        <input id="requested" name="requested" type="number" step="any" value="{{$requested}}" step="any"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Notes:</label>
                                    <div class="col-sm-6">
                                        @if(trim($notes)!=="")
                                            <textarea id="notes" name="notes"
                                                      class="form-control">{{$notes}}</textarea>
                                        @else
                                            <textarea id="notes" name="notes"
                                                      class="form-control">Supplier Requested: {{request()->get('requested') ?? $statement->amount}}, Less Missing Credits for returned items: {{our_format($missingcredits)}},
Less Returns Allowance: {{our_format($returnallo)}}, Less Retailer Penalties: {{our_format($penalties)}}
                                                @if($requested!=$approved)
                                                    - Variance Deduction Breakdown = Line Item
                                                    Variances: {{our_format($lineitemvariance)}}
                                                    - Unrecognized PO's: {{our_format($unrecognized)}}
                                                    - Double Charged PO's: {{$doublecharge}}
                                        @endif</textarea>
                                        @endif

                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Statement Id:</label>
                                    <div class="col-sm-6">
                                        <input id="pk" name="pk" type="text" value="{{$statement->pk}}" readonly
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">
                                        <a href="{{$missingcredits_file->getUrl()}}">
                                            Missing Credits Deduction:</a></label>
                                    <div class="col-sm-6">
                                        <input id="missingcredits" name="missingcredits" type="number" step="any"  
                                               value="{{$missingcredits}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Return Allowance Deduction:</label>
                                    <div class="col-sm-6">
                                        <input id="returnallo" name="returnallo" type="number" step="any" value="{{$returnallo}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Retailer Penalty Deductions:</label>
                                    <div class="col-sm-6">
                                        <input id="penalties" name="penalties" type="number" step="any" value="{{$penalties}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Unrecognized orders/invoices:</label>
                                    <div class="col-sm-6">
                                        <input id="unrecognized" name="unrecognized" type="number" step="any"
                                               value="{{$unrecognized}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Double Charged Orders/Invoices:</label>
                                    <div class="col-sm-6">
                                        <input id="doublecharge" name="doublecharge" type="number" step="any"
                                               value="{{$doublecharge}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Line Item Variances:</label>
                                    <div class="col-sm-6">
                                        <input id="lineitemvariance" name="lineitemvariance" type="number" step="any"
                                               value="{{$lineitemvariance}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Payment Id:</label>
                                    <div class="col-sm-6">
                                        <input id="paymentid" name="paymentid" type="text"
                                               value="{{$statement->paymentid}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">Date Paid:</label>
                                    <div class="col-sm-6">
                                        <input id="paid" name="paid" type="date" value="{{$statement->paid}}"
                                               class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">User:</label>
                                    <div class="col-sm-6">
                                        <input id="sophiouser" name="sophiouser" type="text"
                                               value="{{$statement->sophiouser}}"
                                               class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                @if(request()->get('override'))
                                    <input type="hidden" name="override" value="true">
                                    @endif
                                    <input type="hidden" name="approved" value="{{$approved??$statement->approved}}">
                                <input type="submit" class="btn btn-primary"
                                       value="Submit">&nbsp;
                            </div>
                        </div>
                    </form>
                @endif
            @endif
        </div>
    </div>
@endsection