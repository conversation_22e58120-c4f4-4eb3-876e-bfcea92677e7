@extends(backpack_view('blank'))
@section('content')
    <h2>
        <span class="text-capitalize">FBS \ Returns \ Receive Return </span>
    </h2>

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">
                    Write off for {{$returnline->qty}} {{$returnline->linecode}} {{$returnline->sku}} from order {{$returnline->invpk}} on Manifest {{$returnline->returnedid}}
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 bold-labels">

                            <form method="POST" id="myform"
                                  action=""
                                  id="myform">
                                @csrf
                <input type="hidden" name="origin" value="{{   request()->headers->get('referer')}}">
                                <div class="card">


                                    <div class="card-body">

                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Reason*:</label>
                                            <div class="col-sm-10">
                                                <select class="form-control" name="condition" required>
                                                    <option value="">-- choose --
                                                    </option>

                                                    <option value="BAD2">Bad - Damaged product
                                                    </option>
                                                    <option value="BAD3">Bad - Missing Items
                                                    </option>
                                                    <option value="BAD6">Bad - Damaged Packing - NOT Resellable
                                                    </option>
                                                    <option value="BAD4">Bad - Not our inventory
                                                        (BAD4)
                                                    </option>
                                                    <option value="BAD5">Bad - Other reason (enter note)
                                                    </option>
                                                    <option value="BAD7">Missing from Inventory/Manifest
                                                    </option>
                                                </select>
                                            </div>
                                        </div>


                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Note:</label>
                                            <div class="col-sm-10">
                                            <textarea name="notes" cols="50" rows="3" id="notes"
                                                      class="form-control"></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">PO:</label>
                                            <div class="col-sm-10">

                                                <input id="invpk" name="invpk" type="text"
                                                       value="{{$returnline->invpk}}"

                                                       class="form-control" >
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Qty:</label>
                                            <div class="col-sm-10">
                                                <input id="qty" name="qty" type="text" value="{{$returnline->qty}}" max="{{$returnline->qty}}" class="form-control">
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Sku:</label>
                                            <div class="col-sm-10">
                                                <input id="sku" name="sku" type="text" value="{{$returnline->sku}}" class="form-control">
                                            </div>
                                        </div>

                                        <p><small>* required fields</small></p>
                                    </div>
                                </div>
                                <div class="card-footer">

                                    <input type="submit" class="btn btn-primary"
                                           value="Write Off">&nbsp;
                                </div>
                            </form>
                        </div>


                        <div class="col-md-6 bold-labels">
                            @if(isset($returnline) && $returnline->image!="")
                                <img src="{{explode(';',$returnline->image)[0]}}" style="max-width: 600px">
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')

    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });
    </script>
@endpush