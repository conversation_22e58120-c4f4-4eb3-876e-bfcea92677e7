@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-md-12 bold-labels">
            <form>
                <div class="card">
                    <div class="card-header">
                        Refund Analysis
                    </div>
                    <div class="card-body">

                        <div class="row">

                            <div class="col">
                                <select name="when" class="form-select">
                                    <option>- Select Period -</option>
                                    @foreach(getWhens() as $when)
                                        <option value="{{$when}}"
                                                @if(request()->get('when')==$when) selected @endif>{{$when}}</option>
                                    @endforeach
                                </select>

                            </div>
                            <div class="col">
                                <select name="group" class="form-select">
                                    <option value="">- Group -</option>

                                        <option value="day" @if(request()->get('group')=='day') selected @endif>Day</option>
                                    <option value="month" @if(request()->get('group')=='month') selected @endif>Month</option>
                                </select>

                            </div>

                            <div class="col">
                                <input type="submit" class="btn btn-success btn-small">
                            </div>
                        </div>
                    </div>

                </div>
            </form>
        </div>
    </div>

        <div class="row">

            <div class="col-12">
                <div class="card">
                    <div class="card-header ">
                        <div class="card-title text-center">
                           Return Analysis User

                        </div>
                    </div>
                    <div class="card-body">

                        <div class="table-responsive">

                            <table class="table table-sm datatabletable">
                                <thead>
                                <tr>
                                    <th>Received</th>
                                    <th>User</th>
                                    <th>Count</th>

                                </tr>
                                </thead>
                                <tbody>
                                @foreach($rows as  $row)
                                    <tr>

                                        <td>{{$row->period}}</td>
                                        <td>{{$row->user->name}}</td>


                                        <td>{{$row->cnt}}</td>

                                    </tr>
                                @endforeach
                                </tbody>

                            </table>
                        </div>
                    </div>
                </div>
            </div>


            @endsection

            @section('after_styles')
                @include('admin.inc.css')

            @endsection

            @push('after_scripts')
                @include('admin.inc.js')


                <script type="text/javascript"
                        src="/assets/js/sophio/utils.js"></script>
                <script>
                    $(document).ready(function () {
                        var settings = {};
                        $('select').select2();

                        $('.datatabletable').dataTable({
                            order: [], pageLength: 1000, dom: 'Bfrtip',
                            searching: false, paging: false,

                        });
                    });
                </script>
        @endpush