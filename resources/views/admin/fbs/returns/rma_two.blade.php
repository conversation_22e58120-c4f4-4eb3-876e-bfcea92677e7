@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">
                    <h4>Step 2 of 2 - Submit Return Request and wait for approval and RMA number</h4>
                </div>

            </div>

        </div>
    </div>
    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-body">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{$invoice->pk}}</td>
                            <td>{{$invoice->invdate}}</td>
                            <td>{{$invoice->statusText()}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
        <div class="col-md-12 bold-labels">
            <form method="POST" id="myform"   action="{{  session('nonlogged_accountum')? sophio_route('nonlogged/customer/return.rma',['id'=>$invoice->pk,'token'=>request()->get('token')]) :($is_customer ? sophio_route('customer/return.rma',['id'=>$invoice->pk])  :sophio_route('fbs/returns.rma',['id'=>$invoice->pk]))}}">
                @csrf
                <input type="hidden" name="step" value="2">
                <div class="card">
                    <div class="card-header">
                        Returning Items:
                    </div>

                    <div class="card-body">
                        <table class="table">
                            <thead>
                            <tr>

                                <th>Qty</th>
                                <th>Buyer RMA</th>
                                <th>Reason</th>
                                @if(!$is_customer)
                                <th>Supplier</th>
                                @endif
                                <th>SKU</th>
                                <th>Description</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($returnlines as $lineitempk=>$return)

                                <tr>

                                    <td>{{$return['qty']}}
                                    <input type="hidden" name="returnline[]" value="{{$lineitempk}}">
                                        <input type="hidden" name="qty[{{$lineitempk}}]" value="{{$return['qty']}}">
                                        <input type="hidden" name="rma[{{$lineitempk}}]" value="{{$return['rma']}}">
                                        <input type="hidden" name="reason[{{$lineitempk}}]" value="{{$return['reason']}}">
                                    </td>
                                    <td>{{$return['rma']}}</td>

                                    <td>{{$reasons->find($return['reason'])->name}}</td>
                                    @if(!$is_customer)
                                        <td>{{$return['lineitem']->supplier?$return['lineitem']->supplier->NAME:'N/A'}}</td>
                                    @endif
                                    <td>{{$return['lineitem']->sku}}</td>
                                    <td>{{$return['lineitem']->descript}}</td>

                                    <td>{{$return['lineitem']->price}}</td>
                                    <td>{{$return['lineitem']->price*$return['qty']}}

                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                            <tr>


                                <td colspan="7"><p  class="float-right">Subtotal before charges:</p></td>
                                <td>{{$subtotal}}</td>
                            </tr>
                        </table>
                        <div class="form-group">
                            <label for="exampleFormControlTextarea1">Notes or Comments</label>
                            <textarea class="form-control" name="custnotes" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="card-footer">
                        @if(count($returnlines)>0)
                        <input type="submit" class="btn btn-primary"
                               value="Submit"  >&nbsp;
                        @else
                            Please go back and select at least one line!
                        @endif
                    </div>
                </div>

            </form>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection

@push('after_scripts')
    @include('admin.inc.js')

@endpush