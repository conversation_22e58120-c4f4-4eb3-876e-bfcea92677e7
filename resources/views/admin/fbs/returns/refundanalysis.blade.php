@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-md-12 bold-labels">
            <form>
                <div class="card">
                    <div class="card-header">
                        Refund Analysis
                    </div>
                    <div class="card-body">

                        <div class="row">
                            <div class="col">
                                <select name="type" class="form-select">
                                    <option>- Select Type -</option>

                                    <option value="SUMMARY" @if(request()->get('type')=='SUMMARY') selected @endif>
                                        Summary
                                    </option>
                                    <option value="DETAIL" @if(request()->get('type')=='DETAIL') selected @endif>
                                        Detail
                                    </option>

                                </select>

                            </div>
                            <div class="col">
                                <select name="when" class="form-select">
                                    <option>- Select Period -</option>
                                    @foreach(getWhens() as $when)
                                        <option value="{{$when}}"
                                                @if(request()->get('when')==$when) selected @endif>{{$when}}</option>
                                    @endforeach
                                </select>

                            </div>
                            <div class="col">
                                <select name="writeoff" class="form-select">
                                    <option disabled>- Write Offs -</option>

                                    <option value="true" @if(request()->get('writeoff')=='true') selected @endif>Show
                                        Write Offs
                                    </option>
                                    <option value="false" @if(request()->get('writeoff')=='false') selected @endif>Hide
                                        Write Offs
                                    </option>

                                </select>

                            </div>
                            <div class="col">
                                <select name="action" id="action" class="form-select">
                                    <option value="REFUNDS" @if(request()->get('action')=="REFUNDS") selected @endif>
                                        Refunds
                                    </option>
                                    <option value="WRITEOFFS"
                                            @if(request()->get('action')=="WRITEOFFS") selected @endif>Write-Offs
                                        (supplier)
                                    </option>
                                    <option value="LOST" @if(request()->get('action')=="LOST" ) selected @endif>
                                        Write-Offs (NPW Lost)
                                    </option>
                                    <option value="INVENTORY"
                                            @if(request()->get('action')=="INVENTORY" ) selected @endif>Return Inventory
                                    </option>
                                </select>
                            </div>
                            <div class="col">
                                <select name="custtype" class="form-select">
                                    <option value>All Markets</option>
                                    @foreach(\Sophio\Common\Models\FBS\Marketplace::all() as $marketplace)
                                        <option value="{{trim($marketplace->market)}}"
                                                @if(request()->get('custtype')==trim($marketplace->market)) selected @endif>{{$marketplace->market}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col">
                                <select name="custpk" class="form-select">
                                    <option value>All</option>
                                    @foreach($customers as $customer)
                                        <option value="{{trim($customer->pk)}}"
                                                @if(request()->get('custpk')==trim($customer->pk)) selected @endif>{{$customer->company}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col">
                                <input type="submit" class="btn btn-success btn-small">
                            </div>
                        </div>
                    </div>

                </div>
            </form>
        </div>
    </div>
    @if(request()->get('type')=='SUMMARY')
        <div class="row">

            <div class="col-12">
                <div class="card">
                    <div class="card-header ">
                        <div class="card-title text-center">
                            {{request()->get('action')}} Summary from {{$start}} to {{$end}} - Supplier

                        </div>
                    </div>
                    <div class="card-body">

                        <div class="table-responsive">

                            <table class="table table-sm datatabletable">
                                <thead>
                                <tr>
                                    <th>Supplier Name</th>
                                    <th>Supplier ID</th>
                                    <th>Supplier Expected Due</th>
                                    @if(request()->get('action')!=='INVENTORY')
                                        <th>Supplier Statement Credit</th>
                                        <th>Supplier Reconciled Credit</th>
                                        <th>Supplier Reconciled Refund</th>
                                    @endif
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($rows as  $statement)
                                    <tr>

                                        <td>{{$statement->supplier->NAME}}</td>
                                        <td>{{$statement->profilepk}}</td>


                                        <td>{{$statement->supplier_expected_due}}</td>
                                        @if(request()->get('action')!=='INVENTORY')
                                            <td>{{$statement->supplier_statement_credit}}</td>
                                            <td>{{$statement->supplier_reconciled_refund}}</td>
                                            <td>{{$statement->customer_refunded}}</td>
                                        @endif
                                    </tr>
                                @endforeach
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td>{{$rows->sum('supplier_expected_due')}}</td>
                                    @if(request()->get('action')!=='INVENTORY')
                                        <td>{{$rows->sum('supplier_statement_credit')}}</td>
                                        <td>{{$rows->sum('supplier_reconciled_refund')}}</td>
                                        <td>{{$rows->sum('customer_refunded')}}</td>
                                    @endif
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            @if(request()->get('type')=='DETAIL')
                <div class="row">

                    <div class="col-12">
                        <div class="card">
                            <div class="card-header ">
                                <div class="card-title text-center">
                                    {{request()->get('action')}} Detail from {{$start}} to {{$end}}
                                    - Supplier @if(request()->get('action')=='WRITEOFFS')
                                        Write-Offs
                                    @else
                                        Due
                                    @endif : {{$rows->sum('supplier_expected_due')}}
                                </div>
                            </div>
                            <div class="card-body">

                                <div class="table-responsive">

                                    <table class="table table-sm datatabletable">
                                        <thead>
                                        <tr>
                                            <th>Supplier ID</th>
                                            <th>Supplier Name</th>
                                            <th>Return Id</th>
                                            <th>Invoice Id</th>
                                            <th>Qty</th>
                                            <th>Linecode</th>
                                            <th>Wdlinecode</th>
                                            <th>Sku</th>
                                            <th>Description</th>
                                            <th>Cost</th>
                                            <th>Supplier Expected Due</th>
                                            <th>Supplier Reconciled Credit</th>
                                            <th>Received</th>
                                            <th>Returned</th>
                                            <th>Write Off</th>
                                            <th>Notes</th>

                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($rows as  $returnline)
                                            <tr>
                                                <td>{{$returnline->profilepk}}</td>
                                                <td>{{$returnline->supplier->NAME}}</td>
                                                <td><a href="{{sophio_route('fbs/returns.show',['id'=>$returnline->retpk])}}">{{$returnline->retpk}}</a></td>
                                                <td><a href="{{sophio_route('fbs/fbsorder.main',['id'=>$returnline->invpk])}}">{{$returnline->invpk}}<a/></td>
                                                <td>{{$returnline->qty}}</td>
                                                <td>{{$returnline->linecode}}</td>
                                                <td>{{$returnline->wdlinecode}}</td>
                                                <td>{{$returnline->sku}}</td>
                                                <td>{!! wordwrap($returnline->descript,40,'<br>') !!}</td>
                                                <td>{{$returnline->cost}}</td>
                                                <td>{{$returnline->supplier_expected_due}}</td>
                                                <td>{{$returnline->supplier_reconciled_credit}}</td>
                                                <td>{{$returnline->received}}</td>
                                                <td>{{$returnline->returned}}</td>
                                                <td>{{$returnline->writeoff}}</td>
                                                <td>{!! wordwrap($returnline->notes,80,'<br>') !!}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                        <tfoot>
                                        <tr>

                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{$rows->sum('qty')}}</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{$rows->sum('cost')}}</td>
                                            <td>{{$rows->sum('supplier_expected_due')}}</td>
                                            <td>{{$rows->sum('supplier_reconciled_credit')}}</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>

                                        </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            @endsection

            @section('after_styles')
                @include('admin.inc.css')

            @endsection

            @push('after_scripts')
                @include('admin.inc.js')


                <script type="text/javascript"
                        src="/assets/js/sophio/utils.js"></script>
                <script>
                    $(document).ready(function () {
                        var settings = {};
                        $('select').select2();

                        $('.datatabletable').dataTable({
                            order: [], pageLength: 1000, dom: 'Bfrtip',
                            searching: false, paging: false,
                            buttons: [
                                {
                                    extend: 'csv',
                                    filename: '{{'writeoffDetail_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'
                                }, 'excel', {
                                    extend: 'pdf',
                                    filename: '{{'writeoffDetail_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'
                                }, {
                                    extend: 'print',
                                    filename: '{{'writeoffDetail_'.\Illuminate\Support\Carbon::now()->subMonth()->monthName}}'
                                }
                            ],
                        });
                    });
                </script>
        @endpush