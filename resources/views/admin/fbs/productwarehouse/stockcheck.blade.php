@extends(backpack_view('blank'))

@section('content')
    <h2>
        <span class="text-capitalize"> Real Time Stock Check</span>
    </h2>

    <div class="row">

                <div class="card">
                    <div class="card-header"><h3> RTSC for {{$productWarehouse->mfgcode.' '.$productWarehouse->product_number}} on {{$supplier->NAME}} </h3></div>
                    <div class="card-body">
                        <a href="{{ url()->previous() }}">Back</a>@if(backpack_user()->hasRole('super-admin'))  | <a href="{{backpack_url('dbs/'.\Route::current()->parameter('database').'/productwarehouse/'.$productWarehouse->pk.'/rtsc?alternateflag=yes')}}">Try with alternate yes</a>@endif
                        <table class="table table-responsive">


                            <tr>

                                <th>Local Information</th>
                                <th>RTSC Information</th>
                            </tr>
 @foreach($itemList as $pk=>$itemL)
                                    @php
                                        $warehouse = $itemL->sources[$productWarehouse->contactpk];
                                    @endphp

                                        <td>
                                            <table class="table table-responsive">
                                                <tr><td>Product Number</td><td>{{$productWarehouse->product_number}}</td></tr>
                                                <tr><td>Linecode</td><td>{{$productWarehouse->linecode}}</td></tr>
                                                <tr><td>MfgCode</td><td>{{$productWarehouse->mfgcode}}</td></tr>
                                                <tr><td>Cost</td><td>{{$productWarehouse->cost}}</td></tr>
                                                <tr><td>Core</td><td>{{$productWarehouse->coreprice}}</td></tr>
                                                <tr><td>Description</td><td>{{$productWarehouse->wddescription}}</td></tr>
                                                <tr><td>Qty avail</td><td>{{$productWarehouse->qty_avail}}</td></tr>

                                            </table>
                                        </td>
                                        <td>
                                            <table class="table">

                                                @if($warehouse->rtsc)

                                                    @if(isset($warehouse->rtsc['attributes']['mfgcode']) && isset($productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])])
                                         && $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])]->getImage()!='')
                                                        <tr>
                                                            <td></td>
                                                            <td> <img src="{{$productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])]->getImage()}}" style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                                            </td>
                                                        </tr>
                                                    @endif
                                                @if(isset($warehouse->rtsc['attributes']['errcode']))
                                                    @if(strtolower($warehouse->rtsc['attributes']['errcode'])!='success')
                                                        <tr class="table-danger" >
                                                            <td>Error code</td>
                                                            <td>{{$warehouse->rtsc['attributes']['errcode']??''}}</td>
                                                        </tr>
                                                        @if(isset($warehouse->rtsc['attributes']['errmsg']))
                                                            <tr class="table-danger">
                                                                <td>Error message</td>
                                                                <td>{{$warehouse->rtsc['attributes']['errmsg']??''}}</td>
                                                            </tr>
                                                        @endif
                                                    @endif
                                                    @endif
                                                    <tr>
                                                        <td>SKU</td>
                                                        <td>{{$warehouse->rtsc['attributes']['partno']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Linecode</td>
                                                        <td>{{$warehouse->rtsc['attributes']['linecode']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>MfgCode</td>
                                                        <td>{{$warehouse->rtsc['attributes']['mfgcode']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Cost</td>
                                                        <td>{{$warehouse->rtsc['attributes']['cost']}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['core']>0) class="table-warning" @endif>
                                                        <td>Core</td>
                                                        <td>{{$warehouse->rtsc['attributes']['core']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Description</td>
                                                        <td>{{$warehouse->rtsc['attributes']['desc']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Qty req.</td>
                                                        <td>{{$warehouse->rtsc['attributes']['qtyreq']}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['qtyreq']>$warehouse->rtsc['attributes']['qtyavail']) class="table-danger" @else class="table-success" @endif >
                                                        <td>Qty avail</td>

                                                        <td>{{$warehouse->rtsc['attributes']['qtyavail']}}

                                                                @php
                                                                    $total = 0;
                                                                    if(isset($warehouse->rtsc['altbranch'] ) )
                                                                            foreach($warehouse->rtsc['altbranch'] as $altbranch) {
                                                                                $total+=$altbranch['branch_qtyavail']??0;
                                                                            }

                                                                @endphp

                                                             @if($total>0)( {{$total}} total)@endif
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Branch Name</td>
                                                        <td>{{$warehouse->rtsc['attributes']['branchname']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>MinQty</td>
                                                        <td>{{$warehouse->rtsc['attributes']['minqty']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Delivery time</td>
                                                        <td>{{$warehouse->rtsc['attributes']['deliverytime']??''}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['alternateflag']=='yes') class="table-warning" @endif>
                                                        <td>Alternate</td>
                                                        <td>{{$warehouse->rtsc['attributes']['alternateflag']}}</td>
                                                    </tr>
                                                    @if(isset($warehouse->rtsc['comment']))
                                                        <tr>
                                                            <td>Comment</td>
                                                            <td>{{implode('<br>',$warehouse->rtsc['comment'])}}</td>
                                                        </tr>
                                                    @endif
                                                    @if(isset($warehouse->rtsc['qtybreakdetails']))
                                                        @foreach($warehouse->rtsc['qtybreakdetails']['qtybreak'] as $k=>$break)
                                                        <tr>
                                                            <td>Qty break min qty / cost break</td>
                                                            <td>
                                                                @if(isset($break['attributes']))
                                                                {{$break['attributes']['minqty']??''}} /{{$break['attributes']['cost']}}
                                                                @elseif(isset($break['minqty']))
                                                                    {{$break['minqty']??''}} /{{$break['cost']}}
                                                                @else
                                                                    {{json_encode($break)}}
                                                                @endif
                                                            </td>
                                                        </tr>

                                                            @endforeach
                                                    @endif
                                                    <tr>
                                                        <td>Branch</td>
                                                        <td>
                                                            <select name="branch[{{$productWarehouse->pk}}]" class="form-control">
                                                                <option value="{{$warehouse->rtsc['attributes']['branch']??''}}">{{$warehouse->rtsc['attributes']['branchname']??''}}
                                                                    #{{$warehouse->rtsc['attributes']['branch']??''}} - default
                                                                </option>

                                                                @if(isset($warehouse->rtsc['altbranch']) &&is_array($warehouse->rtsc['altbranch']) && count($warehouse->rtsc['altbranch'])>1)
                                                                    @foreach($warehouse->rtsc['altbranch'] as $altbranch)

                                                                        <option value="{{$altbranch['alt_branch']??''}}"
                                                                                @if( (int)$altbranch['branch_qtyavail']< 1)
                                                                                    disabled
                                                                                @endif;
                                                                        >{{$altbranch['branch_name']}}
                                                                            #{{$altbranch['alt_branch']}}
                                                                            Qty: {{$altbranch['branch_qtyavail']}}  {{$altbranch['viewonly']=="yes"?"View Only!":""}} </option>
                                                                    @endforeach
                                                                @endif
                                                            </select>
                                                        </td>
                                                    </tr>
                                                @endif

                                            </table>

                                        </td>
                                    </tr>
                            <tr><td>
                                 <textarea class="form-control" rows="10">   {{$supplierlog->request}}</textarea>
                                </td>
                                <td>
                                    <textarea class="form-control" rows="10">   {{$supplierlog->response}}</textarea>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2">
                                    <ul class="list-unstyled">
                                        @foreach(\Sophio\Common\Services\LogTrack::get('lineitem.'.$pk) as $log)
                                            <li>{{$log}}</li>
                                        @endforeach
                                    </ul>
                                </td>
                            </tr>
                            @endforeach
                            <tr><td colspan="2">
                                    <ul class="list-unstyled">
                                        @foreach(\Sophio\Common\Services\LogTrack::get('invoice') as $log)
                                            <li>{{$log}}</li>
                                        @endforeach
                                        @foreach(\Sophio\Common\Services\LogTrack::get('suppliers') as $log)
                                            <li>{{$log}}</li>
                                        @endforeach
                                            @foreach(\Sophio\Common\Services\LogTrack::get('orderlink') as $log)
                                                <div class="language-xml" style="max-width: 1000px">
                            <pre><code>{{$log}}</code></pre>
                                                </div>
                                            @endforeach
                                    </ul>
                                </td></tr>

                        </table>

                    </div>
                </div>

            </form>
        </div>
    </div>

@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection

@push('after_scripts')
    @include('admin.inc.js')


    <script>
        $(document).ready(function () {
            $('#suppliersLineItems').dataTable();
            $('#myform').submit(function(){
                if($('.lineitem_checkbox:checked').length > 0) {

                }else {
                    alert("You need to select at least one line item");
                    return false;
                }
            });
        });
    </script>
@endpush