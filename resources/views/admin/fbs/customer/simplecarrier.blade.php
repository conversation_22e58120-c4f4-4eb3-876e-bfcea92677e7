
<div class="row ">
    <div class="col-12">
        <div class="card">
            <div class="card-header">

                <h3 class="card-title">{{$carrier}}  (third_party) accounts for {{$customer->company}}</h3>

            </div>
            <div class="card-body">
                <p class="text-secondary">@if(isset($customer->xml[$carrier.'ACCOUNT']) && $customer->xml[$carrier.'ACCOUNT']!=="")If set, they will override the default {{$carrier}}  {{$customer->xml[$carrier.'ACCOUNT']}} @endif</p>
                <p>                <small>If Postal code is empty, then default <strong>{{$customer->xml['FEDEXPOSTALCODE']??''}}</strong> will be used.</small>     <small>Grayed out suppliers don't use Shipstation.</small></p>

                <form method="POST" action="{{sophio_route('fbs/customer.simplesuppliercarrier',['id'=>$customer->pk])}}?carrier={{$carrier}}" id="{{$carrier}}_accounts_form">
                    @csrf
                    <table class="table">
                        <tr>
                            <th>Supplier Name</th>
                            <th>Account</th>
                            <th>Postal Code</th>
                        </tr>
                        @foreach($suppliers as $supplier)

                            <tr  @if(isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && in_array($supplier->SETTINGS['TRACKINGNUMBERTYPE'], ['SOPHIOFBS','Shipstation']))@else class="table-secondary" @endif >

                                <td>{{$supplier->PK}} {{$supplier->NAME}}<br>
                                <small>{{$supplier->SETTINGS['RETURNSCITY']??''}} {{$supplier->SETTINGS['RETURNSSTATEPROVINCE']??''}}</small></td>
                                <td><input type="text" class="form-control"
                                           name="{{$carrier}}ACCOUNT[{{$supplier->PK}}]"
                                           value="{{$customer->xml[$carrier.'ACCOUNT'.$supplier->PK]??''}}"></td>
                                <td><input type="text" class="form-control"
                                           name="{{$carrier}}POSTALCODE[{{$supplier->PK}}]"
                                           value="{{$customer->xml[$carrier.'POSTALCODE'.$supplier->PK]??''}}"></td>
                            </tr>

                        @endforeach
                    </table>
                    <div class="col">

                        <button name="submit" type="submit" class="btn btn-success" value="true" id="{{$carrier}}_accounts_submit">Submit</button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
