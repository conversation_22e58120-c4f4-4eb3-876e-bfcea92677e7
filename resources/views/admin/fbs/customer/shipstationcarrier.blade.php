
    <div class="row ">
        <div class="col-12">
            <div class="card">
                <div class="card-header">

                        <h3 class="card-title">{{$carrier}}  (third_party) accounts for {{$customer->company}}</h3>


                </div>
                <div class="card-body">
                    <p class="text-secondary">@if(isset($customer->xml[$carrier.'ACCOUNT']) && $customer->xml[$carrier.'ACCOUNT']!=="")If set, they will override the default {{$carrier}}  {{$customer->xml[$carrier.'ACCOUNT']}} @endif</p>
                    <form method="POST" action="{{sophio_route('fbs/customer.simplesuppliercarrier',['id'=>$customer->pk])}}?carrier={{$carrier}}" id="{{$carrier}}_accounts_form">
                        @csrf
                        <table class="table">
                            <tr>
                                <th>Supplier Name</th>
                                <th>Value</th>
                            </tr>
                            @foreach($suppliers as $supplier)
                                <tr>

                                    <td>{{$supplier->PK}} {{$supplier->NAME}}</td>
                                    <td>
                                        @if(isset($supplier_sophio_carriers[$supplier->PK]))
                                        <select name="{{$carrier}}ACCOUNT[{{$supplier->PK}}]" class="form-select">
                                            <option value=""></option>
                                            @foreach ($supplier_sophio_carriers[$supplier->PK] as $c)
                                                <option value="{{$c->shippingProviderId}}"  @if(isset($customer->xml[$carrier.'ACCOUNT'.$supplier->PK]) && $customer->xml[$carrier.'ACCOUNT'.$supplier->PK]==$c->shippingProviderId) selected @endif>
                                                    {{$c->nickname}} ({{$c->shippingProviderId}})</option>
                                            @endforeach
                                        </select>
                                        @else
                                            Contact Supplier
                                        @endif
                         </td>
                                </tr>
                            @endforeach
                        </table>
                        <div class="col">
                            <button name="submit" type="submit" class="btn btn-success" value="true" id="{{$carrier}}_accounts_submit">Submit</button>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
