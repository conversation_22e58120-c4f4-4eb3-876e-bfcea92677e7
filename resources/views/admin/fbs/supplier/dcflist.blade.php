@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">

                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title center">
                    There are {{$pw_mapped->where('mfgcode','')->count()}} linecodes with stock valued at ${{$pw_mapped->where('mfgcode','')->sum('value')}} without mappings, If the mfgcode is empty, Sophio cannot buy the line from you.<br>

                    Please map all that you want to sell and HIDE line codes that you do not want us to buy from you.
                    </div>
                    <div class="card-actions">
                        <a href="{{sophio_route('fbs/dcfsupplier.exportUnmapped',['profilepk'=>$supplier->PK])}}" class="btn btn-primary btn-3">
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-arrow-bar-to-down"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 20l16 0" /><path d="M12 14l0 -10" /><path d="M12 14l4 -4" /><path d="M12 14l-4 -4" /></svg>
                            Download Unmapped Parts
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table datatabletable">
                        <thead>
                        <tr>
                            <th>Inventory</th>
                            <th>Linecode</th>
                            <th>Mfg Name</th>
                            <th>Mfg Code</th>
                            <th>Items</th>
                            <th>Value</th>
                            <th>Type</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($pw_mapped as $p)
                            <tr @if(!$p->dcfmanufacturer) class="table-warning" @elseif(in_array($p->linecode,explode(',',$supplier->SETTINGS['EXCLUDELINECODES']))) class="table-danger" @endif>
                                <td><a href="{{sophio_route('fbs/importedinventory.index',['linecode'=>$p->linecode])}}">View {{$p->linecode}}</a></td>
                                <td>
                                    @if(in_array($p->linecode,explode(',',$supplier->SETTINGS['EXCLUDELINECODES'])))
                                        <a href="{{sophio_route('fbs/dcfsupplier.togglelinecode',['linecode'=>$p->linecode])}}">Show {{$p->linecode}}</a>
                                    @else
                                        <a href="{{sophio_route('fbs/dcfsupplier.togglelinecode',['linecode'=>$p->linecode])}}">Hide {{$p->linecode}}</a>
                                    @endif
                                </td>
                                <td>{{$p->dcfmanufacturer->mfgname??''}}</td>
                                <td>{{$p->mfgcode??''}}</td>
                                <td>{{$p->items}}</td>
                                <td>{{$p->value}}</td>
                                <td>{{$p->type}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script type="text/javascript"
            src="/assets/js/sophio/utils.js"></script>
    <script>
        $(document).ready(function () {
            $('select').on('change', function () {
                location.href = UpdateQueryString($(this).attr('name'), $(this).val(), '{!! url()->full() !!}');
            });
            $('.datatabletable').dataTable({
                order: [], pageLength: 1000, dom: 'Bfrtip',


            });
        });
    </script>
@endpush