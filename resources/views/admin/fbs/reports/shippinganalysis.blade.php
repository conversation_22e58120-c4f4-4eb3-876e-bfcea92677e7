@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form>
                        <div class="row">

                            <div class="col">
                                <label class="form-label">Action:</label>
                                <select name="action" id="action" class="form-select">
                                    <option>--select --</option>
                                    <option value="SUMMARY" @if(request() ->get('action')==="SUMMARY") selected @endif>
                                        Summary
                                    </option>
                                    <option value="DETAIL" @if(request() ->get('action')==="DETAIL") selected @endif>
                                        Detail
                                    </option>
                                    <option value="ORPHANS" @if(request() ->get('action')==="ORPHANS") selected @endif>
                                        Missing Shipping Cost (Orphans)
                                    </option>
                                    <option value="SERVICESTATS"
                                            @if(request() ->get('action')==="SERVICESTATS") selected @endif>Service
                                        Stats
                                    </option>
                                    <option value="DURATION"
                                            @if(request() ->get('action')==="DURATION") selected @endif>Duration
                                    </option>
                                    <option value="SUPPLIEREXCEPTIONS"
                                            @if(request() ->get('action')==="SUPPLIEREXCEPTIONS") selected @endif>
                                        Supplier Exceptions
                                    </option>
                                    <option value="DEACTIVATE"
                                            @if(request() ->get('action')==="DEACTIVATE") selected @endif>Deactivate $$$
                                        Shipment Items
                                    </option>
                                </select>

                            </div>
                            <div class="col">
                                <label class="form-label">Type:</label>
                                <select name="type" id="type" class="form-select">
                                    <option>--select --</option>
                                    <option value="SUMMARY" @if(request() ->get('type')==="SUMMARY") selected @endif>
                                        Summary
                                    </option>
                                    <option value="DETAIL" @if(request() ->get('type')==="DETAIL") selected @endif>
                                        Detail
                                    </option>

                                </select>

                            </div>
                            <div class="col">
                                <label class="form-label">Shipping Cost:</label>
                                <input type="text" name="cost" value="{{request()->get('cost')??10}}"
                                       class="form-control">
                            </div>
                            <div class="col">
                                <label class="form-label">Margin (use 10 for 10%):</label>
                                <input type="text" name="margin" value="{{request()->get('margin')??10}}"
                                       class="form-control">
                            </div>
                            <div class="col">
                                <label class="form-label">Period:</label>
                                <select name="when" class="form-select">
                                    @foreach(getWhens() as $when)
                                        <option value="{{$when}}"
                                                @if((request()->get('when')??'LASTMONTH')==$when) selected @endif>{{$when}}</option>

                                    @endforeach
                                </select>
                            </div>
                            <div class="col">
                                <label class="form-label">Acccount:</label>
                                <select name="custpk" class="form-select">
                                    <option value>All</option>
                                    @foreach($customers as $customer)
                                        <option value="{{trim($customer->pk)}}"
                                                @if(request()->get('custpk')==trim($customer->pk)) selected @endif>{{$customer->company}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col">
                                <label class="form-label">Customer Type:</label>
                                <select name="custtype" class="form-select">
                                    <option value>All</option>
                                    @foreach(\Sophio\Common\Models\FBS\Marketplace::all() as $marketplace)
                                        <option value="{{trim($marketplace->market)}}"
                                                @if(request()->get('custtype')==trim($marketplace->market)) selected @endif>{{$marketplace->market}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col">
                                <label class="form-label">Select supplier:</label>
                                <select name="profilepk" class="form-select">
                                    <option value>All</option>
                                    @foreach((new \Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList())()->get() as $supplier)
                                        <option value="{{trim($supplier->PK)}}"
                                                @if(request()->get('profilepk')==trim($supplier->PK)) selected @endif>{{$supplier->getSupplierDisplayName()}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col">
                                <label class="form-label">&nbsp;</label>
                                <input type="submit" value="submit" class="btn btn-success">
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
        @if($result)
            <div class="col-12">
                @switch(request()->get('action'))
                    @case('RUNDEACTIVATE')
                        <div class="card">
                            <div class="card-body">
                                <p class="alert alert-success"> {{$result}}  items have been deactivated in whi aces product feed so that they are not sold to flat rate shipping customers.</p>
                            </div>
                        </div>
                        @break
                    @case('DEACTIVATE')
                        <div class="card">
                            <div class="card-header text-center">
                              <span>  {{count($result)}} results tracking (# {{request()->get('action')}})
                                - {{implode(' : ',getDatesFromWhen(request('when'),'m/d/y'))}};
                                Profit: {{$result->sum(function($row) {return $row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc;})}}
                                  </span>&nbsp;&nbsp;&nbsp;
                                 <a href="{{generateURL('action','RUNDEACTIVATE')}}" class="btn btn-danger">Deactivate ALL</a>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <table class="table table-responsive datatabletable">

                                        <thead>
                                        <tr>
                                            <th>Linecode</th>
                                            <th>SKU</th>
                                            <th>Ptype</th>
                                            <th>Price</th>
                                            <th>Cost</th>
                                            <th>GP</th>
                                            <th>GP Margin</th>
                                            <th>Descript</th>
                                            <th>Carrier</th>
                                            <th>Weight</th>
                                            <th>Skulength</th>
                                            <th>Skuwidth</th>
                                            <th>Skuheight</th>
                                            <th>Factor</th>
                                            <th>Shipping Cost</th>
                                            <th>Net Profit</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($result as $row)
                                            <tr>
                                                <td>{{$row->linecode}}</td>
                                                <td>{{$row->sku}}</td>
                                                <td></td>
                                                <td>{{$row->price}}</td>
                                                <td>{{$row->cost}}</td>
                                                <td>{{our_format($row->price-$row->cost)}}</td>
                                                <td>{{our_format(($row->price-$row->cost)/$row->price)}}</td>
                                                <td>{{$row->descript}}</td>
                                                <td>{{explode(':',$row->track_num)[0]}}</td>
                                                <td>{{our_format($row->weight)}}</td>
                                                <td>{{our_format($row->skulength)}}</td>
                                                <td>{{our_format($row->skuwidth)}}</td>
                                                <td>{{our_format($row->skuheight)}}</td>
                                                <td>{{our_format($row->skuheight*$row->skuwidth*$row->skuheight/$row->handlingc)}}</td>
                                                <td>{{our_format($row->handlingc)}}</td>
                                                <td
                                                        @if((($row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc)<0) &&
    (($row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc)>-1))
                                                            class="bg-yellow text-yellow-fg"
                                                        @elseif($row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc<0) class="bg-red text-red-fg" @endif
                                                >{{our_format($row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc)}}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                        <tfoot>

                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td> </td>
                                            <td>{{our_format($result->sum('price'))}}</td>
                                            <td>{{our_format($result->sum('cost'))}}</td>

                                            <td> </td>
                                            <td> </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{$result->sum(function($row) {return $row->price + ($row->seller->xml['AFFILIATEFLATSHIPPINGRATE']??0)  - $row->cost -$row->handlingc;})}}</td>

                                        </tr>

                                        </tfoot>

                                    </table>

                                </div>
                            </div>
                            @break
                            @case('SUMMARY')
                                <div class="card">
                                    <div class="card-header text-center">
                                        {{count($result)}} results tracking (# {{request()->get('action')}})
                                        - {{implode(' : ',getDatesFromWhen(request('when'),'m/d/y'))}};

                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <table class="table table-responsive datatabletable">
                                                <thead>
                                                <tr>
                                                    <th>sales_year</th>
                                                    <th>sales_month</th>
                                                    <th>weight</th>
                                                    <th>orders</th>
                                                    <th>sales</th>
                                                    <th>shipping_sold</th>
                                                    <th>shipping_cost</th>
                                                    <th>cost_per_pound</th>
                                                    <th>avg_shipping_cost</th>

                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($result as $row)
                                                    <tr>
                                                        <td>{{$row->sales_year}}</td>
                                                        <td>{{$row->sales_month}}</td>
                                                        <td>{{our_format($row->weight)}}</td>
                                                        <td>{{$row->orders}}</td>
                                                        <td>{{our_format($row->sales)}}</td>
                                                        <td>{{$row->shipping_sold}}</td>
                                                        <td>{{our_format($row->shipping_cost)}}</td>
                                                        <td>{{our_format($row->cost_per_pound)}}</td>
                                                        <td>{{our_format($row->avg_shipping_cost)}}</td>

                                                    </tr>
                                                @endforeach
                                                </tbody>
                                                <tfoot>

                                                <tr>
                                                    <td></td>
                                                    <td></td>
                                                    <td>{{our_format($result->sum('weight'))}}</td>
                                                    <td>{{$result->sum('orders')}}</td>
                                                    <td>{{our_format($result->sum('sales'))}}</td>
                                                    <td>{{$result->sum('shipping_sold')}}</td>
                                                    <td>{{our_format($result->sum('shipping_cost'))}}</td>
                                                    <td></td>
                                                    <td></td>

                                                </tr>

                                                </tfoot>

                                            </table>

                                        </div>
                                    </div>
                                    @break
                                    @case('ORPHANS')
                                        <div class="card">
                                            <div class="card-header text-center">
                                                {{count($result)}} results tracking (# {{request()->get('action')}})
                                                - {{implode(' : ',getDatesFromWhen(request('when'),'m/d/y'))}};

                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <table class="table table-responsive datatabletable">
                                                        @if(request()->get('type')=='SUMMARY')
                                                            <thead>
                                                            <tr>
                                                                <th>Supplier</th>
                                                                <th>Count</th>


                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            @foreach($result as $row)
                                                                <tr>
                                                                    <td>{{$row->supplier->getSupplierDisplayName()}}</td>
                                                                    <td>{{$row->lines}}</td>


                                                                </tr>
                                                            @endforeach
                                                            </tbody>
                                                            <tfoot>
                                                            <tr>
                                                                <td></td>
                                                                <td>{{$result->sum('lines')}}</td>


                                                            </tr>
                                                            </tfoot>
                                                        @else
                                                            <thead>
                                                            <tr>
                                                                <th>Supplier</th>
                                                                <th>Invoice</th>
                                                                <th>Timeout</th>
                                                                <th>Customer</th>
                                                                <th>Custtype</th>
                                                                <th>Carrier</th>
                                                                <th>Service</th>
                                                                <th>Shipping charge</th>
                                                                <th>Shipping estimate</th>
                                                                <th>Track num</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            @foreach($result as $row)
                                                                <tr>
                                                                    <td>{{$row->supplier->getSupplierDisplayName()}}</td>
                                                                    <td>
                                                                        <a href="{{sophio_route('fbs/fbsorder.main',['id'=>$row->invpk])}}">{{$row->invpk}}</a>
                                                                    </td>
                                                                    <td>{{$row->timeout->format('m/d/y h:m:s a')}}</td>
                                                                    <td>{{$row->invoice->custpk}}</td>
                                                                    <td>{{$row->invoice->custtype}}</td>
                                                                    <td>{{$row->invoice->carrier}}</td>
                                                                    <td>{{$row->invoice->service}}</td>
                                                                    <td>{{$row->invoice->handling}}</td>
                                                                    <td>{{extractEstimateCost($row->invoice->getRawOriginal('SHIPTRAKID'))}}</td>
                                                                    <td>{{$row->track_num}}</td>
                                                                </tr>
                                                            @endforeach
                                                            </tbody>
                                                            <tfoot>
                                                            <tr>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td>{{$result->sum('invoice.handling')}}</td>

                                                                <td>{{$result->sum(function($line){
    return extractEstimateCost($line->invoice->getRawOriginal('SHIPTRAKID'));
})}}</td>
                                                                <td></td>
                                                            </tr>
                                                            </tfoot>
                                                        @endif

                                                    </table>

                                                </div>
                                            </div>
                                            @break

                                            @endswitch

                                        </div>
                                </div>
                                @endif
                        </div>
                        @endsection

                        @section('after_styles')
                            @include('admin.inc.css')

                        @endsection

                        @push('after_scripts')
                            @include('admin.inc.js')


                            <script type="text/javascript"
                                    src="/assets/js/sophio/utils.js"></script>
                            <script>
                                $(document).ready(function () {
                                    var settings = {};

                                    $('.datatabletable').dataTable({
                                        order: [], pageLength: 1000, dom: 'Bfrtip',
                                        searching: false, paging: false,

                                    });
                                });
                            </script>
    @endpush