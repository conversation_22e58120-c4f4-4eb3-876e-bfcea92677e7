@extends(backpack_view('blank'))
@section('content')
    @php
         [$start,$end] = getDatesFromWhen(request()->get('when','MTD'),true);
         @endphp
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header"> <div class="card-title">
                        @if(request()->get('mfg_code')!=="" &&  request()->get('mfg_code')!==null)
                            For manufacturer {{\Sophio\Common\Models\WHIACES\Manufacturer::where('mfg_code',request()->get('mfg_code'))->first()->mfg_name}}

                            between {{$start}} -  {{$end}}           we sold
                            {{$sales->sum('items')}} items worth of ${{number_format($sales->sum('total_sale'),2,'.')}}
                            @if($sales->sum('total_sale')>0)
                            at a gross profit of {{number_format((1-$sales->sum('total_cost')/$sales->sum('total_sale'))*100,2,'.')}}%.
                                @endif
                        @else
                            Select a manufacturer
                        @endif
                    </div></div>
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <label class="form-label">Select manufacturer code:</label>
                            <select name="mfg_code" class="form-select">
                                <option value>All</option>
                                @foreach(\Sophio\Common\Models\WHIACES\Manufacturer::orderBy("mfg_name")->get() as $manufacturer)
                                    <option value="{{trim($manufacturer->mfg_code)}}"
                                            @if(request()->get('mfg_code')==trim($manufacturer->mfg_code))) selected @endif>{{$manufacturer->mfg_code.' - '.$manufacturer->mfg_name}}</option>
                                @endforeach
                            </select>
                        </div>
                           <div class="col">
                            <label class="form-label">Period:</label>
                            <select name="when" class="form-select"

                                @foreach(getWhens() as $when)
                                    <option value="{{$when}}"
                                            @if((request()->get('when')??'TODAY')==$when) selected @endif>{{$when}}</option>

                                @endforeach
                            </select>
                        </div>
                        @if(!backpack_user()->hasRole(['supplier']))
                            <div class="col">
                                <label class="form-label">Customer Type:</label>
                                <select name="custtype" class="form-select">
                                    <option value>All</option>
                                    @foreach(\Sophio\Common\Models\FBS\Marketplace::all() as $marketplace)
                                        <option value="{{trim($marketplace->market)}}"
                                                @if(request()->get('custtype')==trim($marketplace->market)) selected @endif>{{$marketplace->market}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col">
                                <label class="form-label">Select supplier:</label>
                                <select name="profilepk" class="form-select">
                                    <option value>All</option>
                                    @foreach((new \Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList())()->get()  as $supplier)
                                        <option value="{{trim($supplier->PK)}}"
                                                @if(request()->get('profilepk')==trim($supplier->PK)) selected @endif>{{$supplier->getSupplierDisplayName()}}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                        <div class="col">
                            <label class="form-label">Group:</label>
                            <select name="group" class="form-select">
                                <option value>SKU</option>
                                <option value="brand"    @if(request()->get('group')=="brand") selected @endif> Brand</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">

                            <table class="table datatabletable">
                                <thead>
                                <tr>
                                    <th>Mfr</th>
                                    @if(request()->get('group')!=="brand")
                                    <th>SKU</th>
                                    <th>Description</th>
                                    @endif
                                    <th>Items</th>
                                    <th>Cost</th>
                                    <th>Sales</th>
                                    <th>Handling Cost</th>
                                    <th>GP</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($sales as  $sale)
                                    <tr>

                                        <td>{{$sale->mfr}}</td>
                                        @if(request()->get('group')!=="brand")
                                        <td>{{$sale->sku}}</td>
                                        <td>{{substr($sale->descript,0,30)}}</td>
                                        @endif
                                        <td>{{$sale->items}}</td>
                                        <td>${{number_format($sale->total_cost,2,'.')}}</td>
                                        <td>${{number_format($sale->total_sale,2,'.')}}</td>
                                        <td>${{number_format($sale->total_handlingc,2,'.')}}</td>
                                        <td>{{ $sale->total_sale>0?number_format((1-$sale->total_cost/$sale->total_sale)*100,2,'.'):0}}%</td>
                                    </tr>
                                @endforeach

                                </tbody>
                                <tfoot>
                                <tr class="fw-bold">
                                    <td>Total</td>
                                    @if(request()->get('group')!=="brand")
                                    <td></td>
                                    <td></td>
                                    @endif
                                    <td>{{round($sales->sum('items'))}}</td>
                                    <td>{{number_format($sales->sum('total_cost'),2,'.')}}</td>
                                    <td>{{number_format($sales->sum('total_sale'),2,'.')}}</td>
                                    <td>{{number_format($sales->sum('total_handlingc'),2,'.')}}</td>
                                    <td>{{ $sales->sum('total_sale')>0?number_format((1-$sales->sum('total_cost')/$sales->sum('total_sale'))*100,2,'.'):0}}%</td>
                                </tr>
                                </tfoot>
                            </table>




                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection

@push('after_scripts')
    @include('admin.inc.js')


    <script type="text/javascript"
            src="/assets/js/sophio/utils.js"></script>
    <script>
        $(document).ready(function () {
            var settings = {};
            $('select').select2();
            $('select').on('change', function () {
                location.href = UpdateQueryString($(this).attr('name'), $(this).val(), '{!! url()->full() !!}');
            });
            $('.datatabletable').dataTable({
                order: [], pageLength: 1000, dom: 'Bfrtip',
                searching: false, paging: false,

            });
        });
    </script>
@endpush