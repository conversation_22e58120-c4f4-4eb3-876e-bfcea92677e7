@extends(backpack_view('blank'))
@section('content')
    <div class="row">

        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        Today's markets sales compared with  previous weeks
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Custtype</th>
                                <th>2 Weeks ago No. Orders</th>
                                <th>A Week ago No. Orders</th>
                                <th>Yesterday No. Orders</th>
                                <th>Today No. Orders</th>
                                <th>2 Weeks ago Sales</th>
                                <th>A Week ago Sales</th>
                                <th>Yesterday Sales</th>
                                <th>Today Sales</th>
                                <th>Delta vs 2 weeks ago</th>
                                <th>Delta vs week ago</th>
                                <th>Delta vs yesterday ago</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($markets as $custtype=>$market)
                                <tr>
                                    <td>{{$custtype}}</td>
                                    <td>{{$market['2weekago']}}</td>
                                    <td>{{$market['aweekago']}}</td>
                                    <td>{{$market['yesterday']}}</td>
                                    <td>
                                        @if($market['today']>$market['aweekago'])
                                            <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                        @elseif($market['today']<$market['aweekago'])
                                                    <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                                 class="icon icon-tabler icon-tabler-trending-down"
                                                                                 width="24"
                                                                                 height="24"
                                                                                 viewBox="0 0 24 24" stroke-width="2"
                                                                                 stroke="currentColor"
                                                                                 fill="none"
                                                                                 stroke-linecap="round"
                                                                                 stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                @else
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1"
                                                                 width="24" height="24" viewBox="0 0 24 24"
                                                                 stroke-width="2" stroke="currentColor" fill="none"
                                                                 stroke-linecap="round" stroke-linejoin="round"><path
                                                                        stroke="none" d="M0 0h24v24H0z"
                                                                        fill="none"></path><path d="M5 12l14 0"></path></svg>

                                                        @endif
                                                        {{$market['today']}}</span>
                                    </td>
                                    <td>${{number_format($market['2weekago_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['aweekago_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['yesterday_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['today_sale'],2,'.')}}</td>
                                    <td>
                                        @if($market['2weekago_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['2weekago_sale'])/$market['2weekago_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @elseif(number_format(100*($market['today_sale']-$market['2weekago_sale'])/$market['2weekago_sale'],2,'.')<0)
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                    @else
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1"
                                                             width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                                                             stroke="currentColor" fill="none" stroke-linecap="round"
                                                             stroke-linejoin="round"><path stroke="none"
                                                                                           d="M0 0h24v24H0z"
                                                                                           fill="none"></path><path
                                                                    d="M5 12l14 0"></path></svg>
                                                    @endif
                                                    {{number_format(100*($market['today_sale']-$market['2weekago_sale'])/$market['2weekago_sale'],2,'.')}}%
                                                    </span>
                                            @else
                                                <span class="text-green">NEW</span>
                                            @endif
                                    </td>
                                    <td>
                                        @if($market['aweekago_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @elseif(number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')<0)
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                    @else
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1"
                                                             width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                                                             stroke="currentColor" fill="none" stroke-linecap="round"
                                                             stroke-linejoin="round"><path stroke="none"
                                                                                           d="M0 0h24v24H0z"
                                                                                           fill="none"></path><path
                                                                    d="M5 12l14 0"></path></svg>
                                                    @endif
                                                    {{number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')}}%
                                                    </span>
                                            @else
                                                <span class="text-green">NEW</span>
                                            @endif
                                    </td>
                                    <td>
                                        @if($market['yesterday_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @elseif(number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')<0)
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                    @else
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1"
                                                             width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                                                             stroke="currentColor" fill="none" stroke-linecap="round"
                                                             stroke-linejoin="round"><path stroke="none"
                                                                                           d="M0 0h24v24H0z"
                                                                                           fill="none"></path><path
                                                                    d="M5 12l14 0"></path></svg>
                                                    @endif
                                                    {{number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')}}%
                                                    </span>
                                            @else
                                                <span class="text-green">NEW</span>
                                            @endif
                                    </td>
                                </tr>
                            @endforeach

                            </tbody>
                            <tfoot>
                            <tr>
                                <td></td>
                                <td>{{$markets->sum('2weekago')}} </td>
                                <td>{{$markets->sum('aweekago')}} </td>
                                <td>{{$markets->sum('yesterday')}} </td>
                                <td>
                                    @if($markets->sum('today')>collect($markets)->sum('aweekago'))
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                @else
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                                    {{$markets->sum('today')}}</span></td>
                                <td>{{our_format($markets->sum('2weekago_sale'))}}</td>
                                <td>{{our_format($markets->sum('aweekago_sale'))}}</td>
                                <td>{{our_format($markets->sum('yesterday_sale'))}}</td>
                                <td>{{our_format($markets->sum('today_sale'))}}   </span></td>
                                <td>        @if($markets->sum('2weekago_sale')>0 && number_format(100*($markets->sum('today_sale')-$markets->sum('2weekago_sale'))/$markets->sum('2weekago_sale'),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                       @if($markets->sum('2weekago_sale')>0)     {{number_format(100*($markets->sum('today_sale')-$markets->sum('2weekago_sale'))/$markets->sum('2weekago_sale'),2,'.')}}%  @endif  </span>
                                </td>
                                <td>        @if($markets->sum('aweekago_sale')>0 && number_format(100*($markets->sum('today_sale')-$markets->sum('aweekago_sale'))/$markets->sum('aweekago_sale'),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                       @if($markets->sum('aweekago_sale')>0)     {{number_format(100*($markets->sum('today_sale')-$markets->sum('aweekago_sale'))/$markets->sum('aweekago_sale'),2,'.')}}%  @endif </span>
                                </td>
                                <td>        @if($markets->sum('yesterday_sale')>0 &&  number_format(100*($markets->sum('today_sale')-$markets->sum('yesterday_sale'))/$markets->sum('yesterday_sale'),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                         @if($markets->sum('yesterday_sale')>0)   {{number_format(100*($markets->sum('today_sale')-$markets->sum('yesterday_sale'))/$markets->sum('yesterday_sale'),2,'.')}}%  @endif  </span>
                                </td>

                            </tr>

                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">

        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        Today's supplier purchases   compared with a week ago
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Supplier</th>
                                <th>A Week ago No. Orders</th>
                                <th>Yesterday No. Orders</th>
                                <th>Today No. Orders</th>
                                <th>A Week ago Sales</th>
                                <th>Yesterday Sales</th>
                                <th>Today Sales</th>
                                <th>Delta vs a week ago</th>
                                <th>Delta vs yesteday</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($suppliers as $profilepk=>$market)
                                <tr>
                                    <td>{{$profilepk>0?\Sophio\Common\Models\FBS\Supplier::find($profilepk)->getSupplierDisplayName():'N/A'}}</td>

                                    <td>{{$market['aweekago']}}</td>
                                    <td>{{$market['yesterday']}}</td>
                                    <td>
                                        @if($market['today']>$market['aweekago'])
                                            <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                        @elseif($market['today']<$market['aweekago'])
                                                    <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                                 class="icon icon-tabler icon-tabler-trending-down"
                                                                                 width="24"
                                                                                 height="24"
                                                                                 viewBox="0 0 24 24" stroke-width="2"
                                                                                 stroke="currentColor"
                                                                                 fill="none"
                                                                                 stroke-linecap="round"
                                                                                 stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                        @endif
                                                        {{$market['today']}}</span>
                                    </td>
                                    <td>${{number_format($market['aweekago_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['yesterday_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['today_sale'],2,'.')}}</td>
                                    <td>
                                        @if($market['aweekago_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @elseif($market['aweekago_sale']>0 && number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale']>0,2,'.')<0)
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                @endif
                                                    {{number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')}}%
                                                    </span>
                                            @else
                                                <span class="text-green">NEW</span>
                                            @endif
                                    </td>
                                    <td>
                                        @if($market['yesterday_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @elseif(number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')<0)
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                @endif
                                                    {{number_format(100*($market['today_sale']-$market['yesterday_sale'])/$market['yesterday_sale'],2,'.')}}%
                                                    </span>
                                            @else
                                                <span class="text-green">NEW</span>
                                            @endif
                                    </td>
                                </tr>
                            @endforeach

                            </tbody>
                            <tfoot>
                            <tr>
                                <td></td>
                                <td>{{$suppliers->sum('aweekago')}} </td>
                                <td>{{$suppliers->sum('yesterday')}} </td>
                                <td>
                                    @if($suppliers->sum('today')>$suppliers->sum('aweekago'))
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                @else
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                                    {{$suppliers->sum('today')}}</span></td>
                                <td>{{our_format($suppliers->sum('aweekago_sale'))}}</td>
                                <td>{{our_format($suppliers->sum('yesterday_sale'))}}</td>
                                <td>{{our_format($suppliers->sum('today_sale'))}}   </span></td>

                                <td>        @if($suppliers->sum('aweekago_sale')>0 && number_format(100*($suppliers->sum('today_sale')-$suppliers->sum('aweekago_sale'))/$suppliers->sum('aweekago_sale'),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                       @if($suppliers->sum('aweekago_sale')>0)     {{number_format(100*($suppliers->sum('today_sale')-$suppliers->sum('aweekago_sale'))/$suppliers->sum('aweekago_sale'),2,'.')}}% @endif  </span>
                                </td>


                                <td>        @if($suppliers->sum('yesterday_sale')>0 && number_format(100*($suppliers->sum('today_sale')-$suppliers->sum('yesterday_sale'))/$suppliers->sum('yesterday_sale'),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                    @if($suppliers->sum('yesterday_sale')>0)        {{number_format(100*($suppliers->sum('today_sale')-$suppliers->sum('yesterday_sale'))/$suppliers->sum('yesterday_sale'),2,'.')}}%  @endif </span>
                                </td>
                            </tr>

                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">

        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        Today  returns compared to a week ago
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Custtype</th>
                                <th>A Week ago No. Returns</th>
                                  <th>Today No. Returns</th>
                                <th>A Week ago Returns</th>
                                <th>Today Returns</th>
                                <th>Delta Returns Value</th>

                            </tr>
                            </thead>
                            <tbody>
                            @foreach($returns as $custtype=>$market)
                                <tr>
                                    <td>{{$custtype}}</td>

                                    <td>{{$market['aweekago']}}</td>
                                    <td>
                                        @if($market['today']>$market['aweekago'])
                                            <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                        @else
                                                    <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                                 class="icon icon-tabler icon-tabler-trending-down"
                                                                                 width="24"
                                                                                 height="24"
                                                                                 viewBox="0 0 24 24" stroke-width="2"
                                                                                 stroke="currentColor"
                                                                                 fill="none"
                                                                                 stroke-linecap="round"
                                                                                 stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                        @endif
                                                        {{$market['today']}}</span>
                                    </td>
                                    <td>${{number_format($market['aweekago_sale'],2,'.')}}</td>
                                    <td>${{number_format($market['today_sale'],2,'.')}}</td>
                                    <td>
                                        @if($market['aweekago_sale']>0)
                                            @if(number_format(100*($market['today_sale']-$market['aweekago_sale'])/$market['aweekago_sale'],2,'.')>0)
                                                <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                            @else
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                                @endif
                                                    {{number_format(100*($market['today_sale']-$market['aweekago_sale'])/($market['aweekago_sale']>0?$market['aweekago_sale']:9999),2,'.')}}%
                                                    </span>
                                                @else
                                                    <span class="text-green">NEW</span>
                                                @endif
                                    </td>
                                </tr>
                            @endforeach

                            </tbody>
                            <tfoot>
                            <tr>
                                <td></td>
                                <td>{{collect($returns)->sum('aweekago')}} </td>
                                <td>
                                    @if(collect($returns)->sum('today')>collect($returns)->sum('aweekago'))
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>

                                @else
                                                <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                             class="icon icon-tabler icon-tabler-trending-down"
                                                                             width="24"
                                                                             height="24"
                                                                             viewBox="0 0 24 24" stroke-width="2"
                                                                             stroke="currentColor"
                                                                             fill="none"
                                                                             stroke-linecap="round"
                                                                             stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                                    {{collect($returns)->sum('today')}}</span></td>
                                <td>{{our_format(collect($returns)->sum('aweekago_sale'))}}</td>
                                <td>{{our_format(collect($returns)->sum('today_sale'))}}   </span></td>

                                <td>     @if(number_format(100*(collect($returns)->sum('today_sale')-collect($returns)->sum('aweekago_sale'))/(collect($returns)->sum('aweekago_sale')>0?collect($returns)->sum('aweekago_sale'):1),2,'.')>0)
                                        <span class="text-green">
                                                 <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                      fill="none"
                                                      stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 17l6 -6l4 4l8 -8"></path>
                                                        <path d="M14 7l7 0l0 7"></path>
                                                    </svg>
                                                </span>
                                    @else
                                        <span class="text-red"> <svg xmlns="http://www.w3.org/2000/svg"
                                                                     class="icon icon-tabler icon-tabler-trending-down"
                                                                     width="24"
                                                                     height="24"
                                                                     viewBox="0 0 24 24" stroke-width="2"
                                                                     stroke="currentColor"
                                                                     fill="none"
                                                                     stroke-linecap="round"
                                                                     stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M3 7l6 6l4 -4l8 8"></path>
                                                        <path d="M21 10l0 7l-7 0"></path>
                                                    </svg>
                                @endif
                                            {{number_format(100*(collect($returns)->sum('today_sale')-collect($returns)->sum('aweekago_sale'))/(collect($returns)->sum('aweekago_sale')>0?collect($returns)->sum('aweekago_sale'):1),2,'.')}}%   </span>
                                </td>
                            </tr>

                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection


@push('after_scripts')
    @include('admin.inc.js')


    <script type="text/javascript"
            src="/assets/js/sophio/utils.js"></script>
    <script>
        $(document).ready(function () {
            var settings = {};
            $('select').select2();
            $('select').on('change', function () {
                location.href = UpdateQueryString($(this).attr('name'), $(this).val(), '{!! url()->full() !!}');
            });

        });
    </script>
@endpush