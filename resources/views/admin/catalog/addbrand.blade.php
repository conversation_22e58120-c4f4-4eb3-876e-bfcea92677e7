@extends(backpack_view('blank'))

@section('content')

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 bold-labels">
                <table class="taxonomy dataTable table table-striped table-hover compact table-condensed">

                    <thead>
                    <tr>
                        <th>ParentCompany</th>
                        <th>BrandName</th>
                        <th>BrandID</th>
                        <th>SubBrand</th>
                        <th>SubBrandID</th>
                        <th>WHi MfgCode</th>
                        <th></th>

                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>loading...</td>
                    </tr>
                    </tbody>
                    <tfoot>
                    <tr>
                        <th>ParentCompany</th>
                        <th>BrandName</th>
                        <th>BrandID</th>
                        <th>SubBrand</th>
                        <th>SubBrandID</th>
                        <th>WHi MfgCode</th>
                        <th></th>

                    </tr>
                    </tfoot>
                </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
    @section('after_styles')
        @include('admin.inc.css')

    @endsection

    @push('after_scripts')
        @include('admin.inc.js')

    <script>
        $(document).ready(function(){
            $('.taxonomy tfoot th').each( function () {
                var title = $(this).text();
                $(this).html( '<input type="text" class="input is-small" placeholder="Search '+title+'" />' );
            } );
            var datat =  $('.taxonomy').DataTable({
                pageLength: 25,
                serverSide: true,
                responsive: true,
                ajax: '{{sophio_route('catalog/brands.brandajax')}}',
                columns: [
                    { data: 'ParentCompany' },
                    { data: 'BrandName' },
                    { data: 'BrandID' },
                    { data: 'SubBrand' },
                    { data: 'SubBrandID' },
                    { data: 'mfg_code' },
                    { data: 'id' },
                ],
                columnDefs: [
                    {
                        render: function(row,type,val,meta) {


                            if(row!==null) {
                                return   'Already assigned';
                            }else{
                                return '<button class="btn btn-primary btn-sm" data-row=\''+JSON.stringify(val)+'\'>Add to Site</button>';
                            }
                        },
                        "searchable": false,
                        "orderable": false,
                        targets: -1
                    },{
                        "searchable": false,
                        targets:[5]
                    }
                ]
            });
            datat.on('click', 'button', function (e) {

                $.ajax({
                    url: '{{ url()->current() }}',
                    type: 'POST',
                    data: JSON.parse($(e.currentTarget).attr('data-row')),
                    success: function (result) {
                        // Show an alert with the result
                        new Noty({
                            type: "success",
                            text: "<strong>Success</strong>"
                        }).show();

                        datat.draw(false);

                    },
                    error: function (result) {
                        // Show an alert with the result
                        new Noty({
                            type: "danger",
                            text: "<strong>Error</strong>"
                        }).show();
                    }
                });

            });
            $('.taxonomy tfoot input').on( 'keyup change', function () {
                datat
                    .column( $(this).parent().index()+':visible' )
                    .search( this.value )
                    .draw();
            } );
        });


    </script>
    @endpush
