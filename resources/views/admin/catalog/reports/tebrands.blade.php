@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-title"> Count by brands</div>

            </div>
        </div>
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">

                        <table class="table datatabletable">
                            <thead>
                            <tr>
                                <th>AAIA Brand ID</th>
                                <th>Brand Name</th>
                                <th>PS Linecode</th>
                                <th>NPW Linecode</th>
                                <th>wi_items count</th>
                                <th>Partshare count</th>
                                <th>NPW stocked count</th>
                                <th>Other stocked count</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($tebrands as  $tebrand)
                                <tr>
                                    <td>{{$tebrand->mfg_code}}</td>
                                    <td>{{$tebrand->mfg_name}}</td>
                                    <td>{{$tebrand->ps_linecode}}</td>
                                    <td>{{$tebrand->npw_linecode}}</td>
                                    <td>{{$tebrand->wi_count}}</td>
                                    <td>{{$tebrand->ps_count}}</td>
                                    <td>{{$tebrand->npw_count}}</td>
                                    <td>{{$tebrand->spw_count}}</td>
                                </tr>
                            @endforeach

                            </tbody>

                        </table>


                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection

@push('after_scripts')
    @include('admin.inc.js')


    <script type="text/javascript"
            src="/assets/js/sophio/utils.js"></script>
    <script>
        $(document).ready(function () {
            var settings = {};
            $('select').select2();
            $('select').on('change', function () {
                location.href = UpdateQueryString($(this).attr('name'), $(this).val(), '{!! url()->full() !!}');
            });
            $('.datatabletable').dataTable({
                order: [], pageLength: 1000, dom: 'Bfrtip',
                searching: false, paging: false,

            });
        });
    </script>
@endpush