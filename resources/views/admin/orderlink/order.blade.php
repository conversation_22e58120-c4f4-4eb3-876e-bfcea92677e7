@extends(backpack_view('blank'))

@section('content')
    <h2>
        <span class="text-capitalize">FBS \ Order \ Manual check</span>
    </h2>

    <div class="row">
        <div class="col-md-8 bold-labels">
            <form method="POST" id="myform" action="{{ url()->full()}}">
                @csrf
                <div class="card">
                    <div class="card-header">Supplier</div>
                    <div class="card-body">
                        <div class="form-group" element="div"><label>Supplier name</label>

                            <select name="pk" class="form-control" required       style="width:100%" >

                                @foreach($suppliers as $profile)
                                    <option value="{{$profile->PK}}"
                                            @if($profile->PK == request()->input('pk')) selected @endif>
                                        {{$profile->PK}} {{$profile->getProfileValue('NAME')}}
                                        [{{$profile->getProfileValue('WHMCSCONTACTPK')}}]
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group" element="div"><label>Alternate</label>

                            <select name="alternateflag" class="form-control">
                                <option value="yes" @if(request()->get('alternateflag')=="yes") selected @endif>yes
                                </option>
                                <option value="no" @if(request()->get('alternateflag')=="no") selected @endif>no
                                </option>
                            </select>
                        </div>

                        <div class="form-group" element="div"><label>Username: </label>
                            <input type="text" class="form-control" value="{{request()->get('username')}}"
                                   name="username">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>
                        <div class="form-group" element="div"><label>Password: </label>
                            <input type="text" class="form-control" value="{{request()->get('password')}}"
                                   name="password">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>
                        <div class="form-group" element="div"><label>Provider: </label>
                            <input type="text" class="form-control" value="{{request()->get('provider')}}"
                                   name="provider">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>

                        <div class="form-group" element="div"><label>Endpoint</label>

                            <select name="endpoint" class="form-control">
                                <option value="url" @if(request()->get('endpoint')=="url") selected @endif>Production
                                </option>
                                <option value="devurl" @if(request()->get('endpoint')=="devurl") selected @endif>Dev
                                </option>
                            </select>
                        </div>


                            <button type="submit" class="btn btn-success" id="switch_supplier">
                                <span class="la la-save" role="presentation" aria-hidden="true"></span> &nbsp;
                                <span data-value="submit">Switch to supplier</span>
                            </button>

                    </div>
                </div>


                @if(isset($supplier))
                    <div class="card">
                        <div class="card-header">Order options</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="instr">Shipping Instruction:</label>
                                        <textarea type="text" class="form-control" id="instr" name="comments[ship]"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group ">
                                        <label for="notes">Notes to Warehouse:</label>
                                        <textarea type="text" class="form-control" id="notes" name="comments[general]"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <div class="card">
                    <div class="card-header">Line items from {{$supplier->profile->NAME??''}} ( {{$supplier->profile->getProfileValue('WHMCSCONTACTPK')??''}} )</div>
                    <div class="card-body">

                        <table class="table">
                            <tr>
                                <th>Select</th>
                                <th>Order Information</th>
                                <th>Warehouse Information</th>
                            </tr>

                            @foreach($invoice->lineitem as $lineitem)
                                @if( isset($itemList[$lineitem->pk]->sources[$contactpk]))
                                    @php
                                        $warehouse = $itemList[$lineitem->pk]->sources[$contactpk];
                                    @endphp
                                    <tr>
                                        <td>
                                            @if(in_array($lineitem->pk,$result['fulfilable']) ||  request()->get('ManagerApproval')=='2')
                                                <div class="form-check"><input type="checkbox" name="lineitem[]"
                                                                               value="{{$lineitem->pk}}" class="lineitem_checkbox">
                                                    <input type="hidden" name="rtsc[{{$lineitem->pk}}]" value="{{json_encode($warehouse->rtsc['attributes']??[])}}">
                                                    @endif
                                                </div>
                                        </td>
                                        <td>
                                            <table class="table">
                                                <tr>
                                                    <td></td>
                                                    <td>       @if(isset($lineitem->product->image_url) && $lineitem->product->image_url!="")
                                                            <img src="{{$lineitem->product->image_url}}" style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                                        @endif</td>
                                                </tr>
                                                <tr>
                                                    <td>SKU</td>
                                                    <td>{{$lineitem->sku}}</td>
                                                </tr>
                                                <tr>
                                                    <td>linecode</td>
                                                    <td>{{$warehouse->linecode}}</td>
                                                </tr>
                                                <tr>
                                                    <td>Mfgcode</td>
                                                    <td>{{$lineitem->linecode}}</td>
                                                </tr>

                                                <tr>
                                                    <td>Brand</td>
                                                    <td>{{$lineitem->mfr}}</td>
                                                </tr>
                                                <tr>
                                                    <td>Sell Price</td>
                                                    <td>{{$lineitem->price}}</td>
                                                </tr>
                                                <tr>
                                                    <td>Description</td>
                                                    <td>{{$lineitem->descript}}</td>
                                                </tr>
                                                <tr><td colspan="2">
                                                        @if(strcmp($lineitem->sup_ord_id,"")!=0)
                                                            <button class="btn btn-sm btn-warning">Already ordered this part!</button>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <ul class="list-unstyled">
                                                            @foreach(\Sophio\Common\Services\LogTrack::get('lineitem.'.$lineitem->pk) as $log)
                                                                <li>{{$log}}</li>
                                                            @endforeach
                                                        </ul>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td>


                                            <table class="table">

                                                @if($warehouse->rtsc)
                                                    @if(isset($productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])])
                                         && $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])]->getImage()!='')
                                                        <tr>
                                                            <td></td>
                                                            <td> <img src="{{$productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'],$warehouse->rtsc['attributes']['partno'])]->getImage()}}" style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                                            </td>
                                                        </tr>
                                                    @endif
                                                    @if(strtolower($warehouse->rtsc['attributes']['errcode'])!='success')
                                                        <tr class="table-danger" >
                                                            <td>Error code</td>
                                                            <td>{{$warehouse->rtsc['attributes']['errcode']??''}}</td>
                                                        </tr>
                                                        @if(isset($warehouse->rtsc['attributes']['errmsg']))
                                                            <tr class="table-danger">
                                                                <td>Error message</td>
                                                                <td>{{$warehouse->rtsc['attributes']['errmsg']??''}}</td>
                                                            </tr>
                                                        @endif
                                                    @endif
                                                    <tr>
                                                        <td>SKU</td>
                                                        <td>{{$warehouse->rtsc['attributes']['partno']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Linecode</td>
                                                        <td>{{$warehouse->rtsc['attributes']['linecode']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>MfgCode</td>
                                                        <td>{{$warehouse->rtsc['attributes']['mfgcode']??''}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Cost</td>
                                                        <td>{{$warehouse->rtsc['attributes']['cost']}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['core']>0) class="table-warning" @endif>
                                                        <td>Core</td>
                                                        <td>{{$warehouse->rtsc['attributes']['core']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Description</td>
                                                        <td>{{$warehouse->rtsc['attributes']['desc']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Qty req.</td>
                                                        <td>{{$warehouse->rtsc['attributes']['qtyreq']}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['qtyreq']>$warehouse->rtsc['attributes']['qtyavail']) class="table-danger" @else class="table-success" @endif >
                                                        <td>Qty avail</td>
                                                        <td>{{$warehouse->rtsc['attributes']['qtyavail']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Branch Name</td>
                                                        <td>{{$warehouse->rtsc['attributes']['branchname']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>MinQty</td>
                                                        <td>{{$warehouse->rtsc['attributes']['minqty']}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Delivery time</td>
                                                        <td>{{$warehouse->rtsc['attributes']['deliverytime']}}</td>
                                                    </tr>
                                                    <tr @if($warehouse->rtsc['attributes']['alternateflag']=='yes') class="table-warning" @endif>
                                                        <td>Alternate</td>
                                                        <td>{{$warehouse->rtsc['attributes']['alternateflag']}}</td>
                                                    </tr>
                                                    @if(isset($warehouse->rtsc['comment']))
                                                        <tr>
                                                            <td>Comment</td>
                                                            <td>{{implode('<br>',$warehouse->rtsc['comment'])}}</td>
                                                        </tr>
                                                    @endif
                                                    @if(isset($warehouse->rtsc['qtybreakdetails']))

                                                        <tr>
                                                            <td>Min. Qty break</td>
                                                            <td>{{$warehouse->rtsc['qtybreakdetails']['qtybreak']['attributes']['minqty']}}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Cost break</td>
                                                            <td>{{$warehouse->rtsc['qtybreakdetails']['qtybreak']['attributes']['cost']}}</td>
                                                        </tr>

                                                    @endif
                                                    <tr>
                                                        <td>Branch</td>
                                                        <td>
                                                            <select name="branch[{{$lineitem->pk}}]" class="form-control">
                                                                <option value="{{$warehouse->rtsc['attributes']['branch']}}">{{$warehouse->rtsc['attributes']['branchname']}}
                                                                    #{{$warehouse->rtsc['attributes']['branch']}} - default
                                                                </option>
                                                                @if(isset($warehouse->rtsc['altbranch']))
                                                                    @foreach($warehouse->rtsc['altbranch'] as $altbranch)
                                                                        <option value="{{$altbranch['alt_branch']}}"
                                                                                @if( (int)$altbranch['branch_qtyavail']< (int)$lineitem->qty_ord)
                                                                                    disabled
                                                                                @endif;
                                                                        >{{$altbranch['branch_name']}}
                                                                            #{{$altbranch['alt_branch']}}
                                                                            Qty: {{$altbranch['branch_qtyavail']}}  {{$altbranch['viewonly']=="yes"?"View Only!":""}} </option>
                                                                    @endforeach
                                                                @endif
                                                            </select>
                                                        </td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td colspan="2">
                                                        <textarea class="form-control">{{$warehouse->rtsc_raw}}</textarea>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                            <tr><td colspan="2">
                                    <ul class="list-unstyled">
                                        @foreach(\Sophio\Common\Services\LogTrack::get('invoice') as $log)
                                            <li>{{$log}}</li>
                                        @endforeach
                                        @foreach(\Sophio\Common\Services\LogTrack::get('suppliers') as $log)
                                            <li>{{$log}}</li>
                                        @endforeach
                                    </ul>
                                </td></tr>
                            <tr>
                                <td colspan="3" class="text-center"><input type="submit" class="btn btn-primary" value="Finish" id="finishcheckorder">&nbsp;
                                    <a href="{{ backpack_url('dbs/'.session('database').'/fbs/fbsorder/manualcheck?id='.$invoice->pk) }}"  class="btn btn-default">Back</a>
                                </td>
                            </tr>
                        </table>

                    </div>
                </div>

@endif
            </form>
        </div>
    </div>

@endsection

@section('after_styles')
    @include('admin.inc.css')

@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {

            $('select[name=pk]').select2();
        });
    </script>
    @if(isset($supplier))
    <script>
        $(document).ready(function () {
            $('#suppliersLineItems').dataTable();
            $('select[name=pk]').select2();
            $('#switch_supplier').click(function(){

                if($('.lineitem_checkbox:checked').length > 0) {
                    $('.lineitem_checkbox:checked').prop('checked',false);


                }

            });
            $('#finishcheckorder').click(function(){
                if($('.lineitem_checkbox:checked').length > 0) {

                }else {
                    alert("You need to select at least one line item");
                    return false;
                }
            });
        });
    </script>
@endif
@endpush