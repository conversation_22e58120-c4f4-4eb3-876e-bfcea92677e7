@extends(backpack_view('blank'))

@section('content')
    <h2>
        <span class="text-capitalize">Order Link Stock Check</span>
    </h2>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"></div>
                <div class="card-body">
                    <form method="POST" id="walmart_form"
                          action="{{ url()->full()}}">
                        @csrf
                        <div class="form-group" element="div"><label>Supplier</label>

                            <select name="pk" class="form-control" required>

                                @foreach($suppliers as $profile)
                                    <option value="{{$profile->PK}}"
                                            @if($profile->PK == request()->input('pk')) selected @endif>
                                        {{$profile->PK}} {{$profile->NAME}}
                                        [{{$profile->SETTINGS['WHMCSCONTACTPK']??''}}]
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group" element="div"><label>Part Number: </label>
                            <input type="text" class="form-control" value="{{request()->get('partno')}}" name="partno"
                                   required="required">
                        </div>
                        <div class="form-group" element="div"><label>Linecode: </label>
                            <input type="text" class="form-control" value="{{request()->get('linecode')}}"
                                   name="linecode">
                        </div>
                        <div class="form-group" element="div"><label>Mfgcode: </label>
                            <input type="text" class="form-control" value="{{request()->get('mfgcode')}}"
                                   name="mfgcode">
                        </div>
                        <div class="form-group" element="div"><label>Qty: </label>
                            <input type="number" class="form-control" value="{{request()->get('qtyreq',1)}}"
                                   name="qtyreq">
                        </div>
                        <div class="form-group" element="div"><label>Alternate</label>

                            <select name="alternateflag" class="form-control">
                                <option value="yes" @if(request()->get('alternateflag')=="yes") selected @endif>yes
                                </option>
                                <option value="no" @if(request()->get('alternateflag')=="no") selected @endif>no
                                </option>
                            </select>
                        </div>

                        <div class="form-group" element="div"><label>Username: </label>
                            <input type="text" class="form-control" value="{{request()->get('username')}}"
                                   name="username">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>
                        <div class="form-group" element="div"><label>Password: </label>
                            <input type="text" class="form-control" value="{{request()->get('password')}}"
                                   name="password">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>
                        <div class="form-group" element="div"><label>Provider: </label>
                            <input type="text" class="form-control" value="{{request()->get('provider')}}"
                                   name="provider">
                            <small   class="form-text text-muted">Leave blank for supplier's default</small>
                        </div>

                        <div class="form-group" element="div"><label>Endpoint</label>

                            <select name="endpoint" class="form-control">
                                <option value="url" @if(request()->get('endpoint')=="url") selected @endif>Production
                                </option>
                                <option value="devurl" @if(request()->get('endpoint')=="devurl") selected @endif>Dev
                                </option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-success" id="walmart_start_feed">
                            <span class="la la-save" role="presentation" aria-hidden="true"></span> &nbsp;
                            <span data-value="submit">Run</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        @if(count($parts)>0)
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header"></div>
                    <div class="card-body">
                        <table class="table">

                            @foreach($parts as $pk=>$rtsc)

                                <table class="table">

                                    @if(strtolower($rtsc['attributes']['errcode'])!='success')
                                        <tr class="table-danger">
                                            <td>Error code</td>
                                            <td>{{$rtsc['attributes']['errcode']??''}}</td>
                                        </tr>
                                        @if(isset($rtsc['attributes']['errmsg']))
                                            <tr class="table-danger">
                                                <td>Error message</td>
                                                <td>{{$rtsc['attributes']['errmsg']??''}}</td>
                                            </tr>
                                        @endif
                                    @endif
                                    <tr>
                                        <td>SKU</td>
                                        <td>{{$rtsc['attributes']['partno']??''}}</td>
                                    </tr>
                                    <tr>
                                        <td>Linecode</td>
                                        <td>{{$rtsc['attributes']['linecode']??''}}</td>
                                    </tr>
                                    <tr>
                                        <td>MfgCode</td>
                                        <td>{{$rtsc['attributes']['mfgcode']??''}}</td>
                                    </tr>
                                    <tr>
                                        <td>Cost</td>
                                        <td>{{$rtsc['attributes']['cost']}}</td>
                                    </tr>
                                    <tr @if($rtsc['attributes']['core']>0) class="table-warning" @endif>
                                        <td>Core</td>
                                        <td>{{$rtsc['attributes']['core']}}</td>
                                    </tr>
                                    <tr>
                                        <td>Description</td>
                                        <td>{{$rtsc['attributes']['desc']}}</td>
                                    </tr>
                                    <tr>
                                        <td>Qty req.</td>
                                        <td>{{$rtsc['attributes']['qtyreq']}}</td>
                                    </tr>
                                    <tr @if($rtsc['attributes']['qtyreq']>$rtsc['attributes']['qtyavail']) class="table-danger"
                                        @else class="table-success" @endif >
                                        <td>Qty avail</td>
                                        <td>{{$rtsc['attributes']['qtyavail']}}</td>
                                    </tr>
                                    <tr>
                                        <td>Branch Name</td>
                                        <td>{{$rtsc['attributes']['branchname']??''}}</td>
                                    </tr>
                                    <tr>
                                        <td>MinQty</td>
                                        <td>{{$rtsc['attributes']['minqty']}}</td>
                                    </tr>
                                    <tr>
                                        <td>Delivery time</td>
                                        <td>{{$rtsc['attributes']['deliverytime']??""}}</td>
                                    </tr>
                                    <tr @if($rtsc['attributes']['alternateflag']=='yes') class="table-warning" @endif>
                                        <td>Alternate</td>
                                        <td>{{$rtsc['attributes']['alternateflag']}}</td>
                                    </tr>
                                    @if(isset($rtsc['comment']))
                                        <tr>
                                            <td>Comment</td>
                                            <td>{{implode('<br>',$rtsc['comment'])}}</td>
                                        </tr>
                                    @endif
                                    @if(isset($rtsc['qtybreakdetails']))

                                        <tr>
                                            <td>Min. Qty break</td>
                                            <td>{{$rtsc['qtybreakdetails']['qtybreak']['attributes']['minqty']}}</td>
                                        </tr>
                                        <tr>
                                            <td>Cost break</td>
                                            <td>{{$rtsc['qtybreakdetails']['qtybreak']['attributes']['cost']}}</td>
                                        </tr>

                                    @endif
                                    <tr>
                                        <td>Branch</td>
                                        <td>
                                            <select class="form-control">
                                                <option value="{{$rtsc['attributes']['branch']}}">{{$rtsc['attributes']['branchname']??''}}
                                                    #{{$rtsc['attributes']['branch']}} - default
                                                </option>
                                                @if(isset($rtsc['altbranch']))
                                                    @foreach($rtsc['altbranch'] as $altbranch)
                                                        <option value="{{$altbranch['alt_branch']}}"
                                                                @if( (int)$altbranch['branch_qtyavail']< 1)
                                                                    disabled
                                                                @endif;
                                                        >{{$altbranch['branch_name']}}
                                                            #{{$altbranch['alt_branch']}}
                                                            Qty: {{$altbranch['branch_qtyavail']}}  {{$altbranch['viewonly']=="yes"?"View Only!":""}} </option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        </td>
                                    </tr>


                                </table>

                                </td>
                                </tr>

                            @endforeach

                        </table>

                    </div>
                </div>

            </div>
        @endif
    </div>

    @if (count(\Sophio\Common\Services\LogTrack::get('orderlink'))>0)
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header"></div>
                    <div class="card-body">
                        @foreach(\Sophio\Common\Services\LogTrack::get('orderlink') as $log)
                            <div class="language-xml" >
                                <pre><code>{{$log}}</code></pre>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('#suppliersLineItems').dataTable();
            $('#myform').submit(function () {
                if ($('.lineitem_checkbox:checked').length > 0) {

                } else {
                    alert("You need to select at least one line item");
                    return false;
                }
            });
        });
    </script>
@endpush