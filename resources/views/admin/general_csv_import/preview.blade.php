@extends(backpack_view('blank'))

@section('header')
    <div class="container-fluid">
        <h2>
            <span class="text-capitalize">CSV Import Preview</span>
            <small>Map columns and configure import settings</small>
        </h2>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-file-csv"></i> 
                    File Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>File:</strong> {{ $file_info['name'] }}
                    </div>
                    <div class="col-md-3">
                        <strong>Size:</strong> {{ number_format($file_info['size'] / 1024, 2) }} KB
                    </div>
                    <div class="col-md-3">
                        <strong>Total Rows:</strong> {{ number_format($preview['total_rows']) }}
                    </div>
                    <div class="col-md-3">
                        <strong>Target Model:</strong> {{ class_basename($target_model) }}
                    </div>
                </div>
            </div>
        </div>

        <form action="{{ route('general-csv-import.import') }}" method="POST" id="importForm">
            @csrf
            <input type="hidden" name="filename" value="{{ $filename }}">
            <input type="hidden" name="target_model" value="{{ $target_model }}">
            <input type="hidden" name="original_filename" value="{{ $file_info['name'] }}">

            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fa fa-columns"></i> 
                        Column Mapping
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Instructions:</strong> Map each CSV column to the corresponding database field. 
                        Leave unmapped if you don't want to import that column.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>CSV Column</th>
                                    <th>Sample Data</th>
                                    <th>Map to Database Field</th>
                                    <th>Validation Rules</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($preview['headers'] as $index => $header)
                                <tr>
                                    <td>
                                        <strong>{{ $header }}</strong>
                                        <br><small class="text-muted">Column {{ $index + 1 }}</small>
                                    </td>
                                    <td>
                                        @if(isset($preview['preview_data'][0][$index]))
                                            <code>{{ Str::limit($preview['preview_data'][0][$index], 50) }}</code>
                                        @else
                                            <span class="text-muted">No data</span>
                                        @endif
                                    </td>
                                    <td>
                                        <select name="column_mapping[{{ $index }}]" class="form-control column-mapping">
                                            <option value="">-- Don't Import --</option>
                                            @foreach($preview['fillable_fields'] as $field)
                                                <option value="{{ $field }}" 
                                                    {{ isset($preview['suggested_mapping'][$index]) && $preview['suggested_mapping'][$index] == $field ? 'selected' : '' }}>
                                                    {{ $field }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" 
                                               name="validation_rules[{{ $index }}]" 
                                               class="form-control form-control-sm" 
                                               placeholder="e.g., required|email|max:255"
                                               title="Laravel validation rules">
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <strong>Suggested Mappings:</strong> The system has automatically suggested column mappings based on field names. 
                        Please review and adjust as needed.
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fa fa-cogs"></i> 
                        Import Options
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="skip_header" value="1" class="form-check-input" id="skip_header" checked>
                                    <label class="form-check-label" for="skip_header">
                                        Skip Header Row
                                    </label>
                                </div>
                                <small class="form-text text-muted">Skip the first row if it contains column headers.</small>
                            </div>

                            <div class="form-group">
                                <label for="batch_size">Batch Size</label>
                                <input type="number" name="batch_size" id="batch_size" class="form-control" value="100" min="1" max="1000">
                                <small class="form-text text-muted">Number of records to process at once (1-1000).</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unique_fields">Unique Fields (for duplicate detection)</label>
                                <select name="unique_fields[]" id="unique_fields" class="form-control" multiple>
                                    @foreach($preview['fillable_fields'] as $field)
                                        <option value="{{ $field }}">{{ $field }}</option>
                                    @endforeach
                                </select>
                                <small class="form-text text-muted">
                                    Select fields that should be unique. Records with matching values will be skipped.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fa fa-eye"></i> 
                        Data Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    @foreach($preview['headers'] as $header)
                                        <th>{{ $header }}</th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($preview['preview_data'] as $row)
                                <tr>
                                    @foreach($row as $cell)
                                        <td>{{ Str::limit($cell, 30) }}</td>
                                    @endforeach
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if(count($preview['preview_data']) < $preview['total_rows'])
                        <div class="alert alert-info mt-3">
                            Showing first {{ count($preview['preview_data']) }} rows of {{ number_format($preview['total_rows']) }} total rows.
                        </div>
                    @endif
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fa fa-arrow-left"></i> Back
                            </button>
                            <button type="button" class="btn btn-danger" id="cancelImport">
                                <i class="fa fa-times"></i> Cancel
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="submit" class="btn btn-success btn-lg" id="startImport">
                                <i class="fa fa-play"></i> Start Import
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@section('after_scripts')
<script>
$(document).ready(function() {
    // Initialize multiple select for unique fields
    $('#unique_fields').select2({
        placeholder: 'Select fields for duplicate detection...',
        allowClear: true
    });

    // Validate column mapping
    function validateMapping() {
        var mappedFields = [];
        var duplicates = [];
        var hasMapping = false;

        $('.column-mapping').each(function() {
            var value = $(this).val();
            if (value) {
                hasMapping = true;
                if (mappedFields.includes(value)) {
                    duplicates.push(value);
                } else {
                    mappedFields.push(value);
                }
            }
        });

        if (!hasMapping) {
            alert('Please map at least one column to a database field.');
            return false;
        }

        if (duplicates.length > 0) {
            alert('Duplicate field mappings detected: ' + duplicates.join(', ') + '. Each field can only be mapped once.');
            return false;
        }

        return true;
    }

    // Handle form submission
    $('#importForm').submit(function(e) {
        if (!validateMapping()) {
            e.preventDefault();
            return false;
        }

        // Show confirmation
        var totalRows = {{ $preview['total_rows'] }};
        var mappedColumns = $('.column-mapping').filter(function() { return $(this).val() !== ''; }).length;
        
        var message = 'Are you sure you want to import ' + totalRows.toLocaleString() + ' rows with ' + mappedColumns + ' mapped columns?';
        
        if (!confirm(message)) {
            e.preventDefault();
            return false;
        }

        // Show loading state
        $('#startImport').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Starting Import...');
    });

    // Handle cancel import
    $('#cancelImport').click(function() {
        if (confirm('Are you sure you want to cancel this import? The uploaded file will be deleted.')) {
            // Delete the uploaded file
            $.ajax({
                url: '{{ route("general-csv-import.delete-file") }}',
                method: 'POST',
                data: {
                    filename: '{{ $filename }}',
                    _token: '{{ csrf_token() }}'
                },
                success: function() {
                    window.location.href = '{{ route("general-csv-import.index") }}';
                },
                error: function() {
                    window.location.href = '{{ route("general-csv-import.index") }}';
                }
            });
        }
    });

    // Highlight column mapping changes
    $('.column-mapping').change(function() {
        var $row = $(this).closest('tr');
        if ($(this).val()) {
            $row.addClass('table-success');
        } else {
            $row.removeClass('table-success');
        }
    });

    // Initialize row highlighting
    $('.column-mapping').each(function() {
        if ($(this).val()) {
            $(this).closest('tr').addClass('table-success');
        }
    });

    // Auto-suggest validation rules based on field names
    $('.column-mapping').change(function() {
        var field = $(this).val();
        var $validationInput = $(this).closest('tr').find('input[name^="validation_rules"]');
        
        if (field && !$validationInput.val()) {
            var suggestions = {
                'email': 'required|email',
                'phone': 'nullable|string|max:20',
                'name': 'required|string|max:255',
                'title': 'required|string|max:255',
                'description': 'nullable|string',
                'price': 'nullable|numeric|min:0',
                'cost': 'nullable|numeric|min:0',
                'qty': 'nullable|integer|min:0',
                'quantity': 'nullable|integer|min:0',
                'weight': 'nullable|numeric|min:0',
                'height': 'nullable|numeric|min:0',
                'width': 'nullable|numeric|min:0',
                'length': 'nullable|numeric|min:0'
            };
            
            for (var key in suggestions) {
                if (field.toLowerCase().includes(key)) {
                    $validationInput.val(suggestions[key]);
                    break;
                }
            }
        }
    });
});
</script>
@endsection
