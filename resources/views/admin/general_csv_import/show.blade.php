@extends(backpack_view('blank'))

@section('header')
    <div class="container-fluid">
        <h2>
            <span class="text-capitalize">Import Details</span>
            <small>Import #{{ $import->id }} - {{ $import->original_filename }}</small>
        </h2>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-info-circle"></i> 
                    Import Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th>Import ID:</th>
                                <td>{{ $import->id }}</td>
                            </tr>
                            <tr>
                                <th>Original Filename:</th>
                                <td>{{ $import->original_filename }}</td>
                            </tr>
                            <tr>
                                <th>Target Table:</th>
                                <td><code>{{ $import->target_table }}</code></td>
                            </tr>
                            <tr>
                                <th>Target Model:</th>
                                <td><code>{{ $import->target_model }}</code></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    @if($import->status === 'completed')
                                        <span class="badge badge-success">
                                            <i class="fa fa-check"></i> Completed
                                        </span>
                                    @elseif($import->status === 'failed')
                                        <span class="badge badge-danger">
                                            <i class="fa fa-times"></i> Failed
                                        </span>
                                    @elseif($import->status === 'processing')
                                        <span class="badge badge-warning">
                                            <i class="fa fa-spinner fa-spin"></i> Processing
                                        </span>
                                    @else
                                        <span class="badge badge-secondary">
                                            {{ ucfirst($import->status) }}
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th>User:</th>
                                <td>
                                    @if($import->user)
                                        {{ $import->user->name }}
                                    @else
                                        <span class="text-muted">Unknown</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Started:</th>
                                <td>
                                    @if($import->started_at)
                                        {{ $import->started_at->format('Y-m-d H:i:s') }}
                                        <br><small class="text-muted">{{ $import->started_at->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">Not started</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Completed:</th>
                                <td>
                                    @if($import->completed_at)
                                        {{ $import->completed_at->format('Y-m-d H:i:s') }}
                                        <br><small class="text-muted">{{ $import->completed_at->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">Not completed</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Duration:</th>
                                <td>
                                    @if($import->duration)
                                        {{ $import->duration }}
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-chart-bar"></i> 
                    Import Statistics
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($import->total_rows) }}</h3>
                                <p class="mb-0">Total Rows</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($import->processed_rows) }}</h3>
                                <p class="mb-0">Processed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($import->successful_imports) }}</h3>
                                <p class="mb-0">Successful</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($import->failed_imports) }}</h3>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if($import->skipped_duplicates > 0)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($import->skipped_duplicates) }}</h3>
                                <p class="mb-0">Skipped Duplicates</p>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if($import->processed_rows > 0)
                <div class="mt-4">
                    <h5>Success Rate</h5>
                    @php
                        $successRate = ($import->successful_imports / $import->processed_rows) * 100;
                    @endphp
                    <div class="progress" style="height: 30px;">
                        <div class="progress-bar 
                            @if($successRate >= 90) bg-success 
                            @elseif($successRate >= 70) bg-warning 
                            @else bg-danger @endif" 
                            role="progressbar" 
                            style="width: {{ $successRate }}%">
                            {{ number_format($successRate, 1) }}%
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        @if($import->column_mapping)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-columns"></i> 
                    Column Mapping
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>CSV Column Index</th>
                                <th>Database Field</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($import->column_mapping as $csvIndex => $dbField)
                            <tr>
                                <td>{{ $csvIndex }}</td>
                                <td><code>{{ $dbField }}</code></td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        @if($import->validation_rules)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-check-circle"></i> 
                    Validation Rules
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Rules</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($import->validation_rules as $field => $rules)
                            <tr>
                                <td><code>{{ $field }}</code></td>
                                <td><code>{{ $rules }}</code></td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        @if($import->errors && count($import->errors) > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-exclamation-triangle text-danger"></i> 
                    Errors ({{ count($import->errors) }})
                </h3>
            </div>
            <div class="card-body">
                <div style="max-height: 400px; overflow-y: auto;">
                    <ul class="list-group">
                        @foreach($import->errors as $error)
                        <li class="list-group-item list-group-item-danger">
                            <i class="fa fa-times-circle"></i> {{ $error }}
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        @endif

        @if($import->warnings && count($import->warnings) > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-exclamation-triangle text-warning"></i> 
                    Warnings ({{ count($import->warnings) }})
                </h3>
            </div>
            <div class="card-body">
                <div style="max-height: 300px; overflow-y: auto;">
                    <ul class="list-group">
                        @foreach($import->warnings as $warning)
                        <li class="list-group-item list-group-item-warning">
                            <i class="fa fa-exclamation-triangle"></i> {{ $warning }}
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        @endif

        @if($import->summary)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-file-text"></i> 
                    Summary
                </h3>
            </div>
            <div class="card-body">
                <p>{{ $import->summary }}</p>
            </div>
        </div>
        @endif
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-cogs"></i> 
                    Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('general-csv-import.history') }}" class="btn btn-secondary">
                        <i class="fa fa-arrow-left"></i> Back to History
                    </a>
                    
                    <a href="{{ route('general-csv-import.index') }}" class="btn btn-primary">
                        <i class="fa fa-upload"></i> New Import
                    </a>
                    
                    <button type="button" class="btn btn-info" onclick="window.print()">
                        <i class="fa fa-print"></i> Print Details
                    </button>
                    
                    <button type="button" class="btn btn-success" onclick="exportImportLog()">
                        <i class="fa fa-download"></i> Export Log
                    </button>
                </div>
            </div>
        </div>

        @if($import->isCompleted())
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-lightbulb"></i> 
                    Recommendations
                </h3>
            </div>
            <div class="card-body">
                @if($import->isSuccessful())
                    <div class="alert alert-success">
                        <strong>Excellent!</strong> Your import was 100% successful. All records were imported without issues.
                    </div>
                @elseif($import->success_rate >= 90)
                    <div class="alert alert-info">
                        <strong>Great job!</strong> Your import had a {{ number_format($import->success_rate, 1) }}% success rate. 
                        Review the errors to improve future imports.
                    </div>
                @elseif($import->success_rate >= 70)
                    <div class="alert alert-warning">
                        <strong>Good, but could be better.</strong> Your import had a {{ number_format($import->success_rate, 1) }}% success rate. 
                        Consider reviewing your data format and validation rules.
                    </div>
                @else
                    <div class="alert alert-danger">
                        <strong>Needs attention.</strong> Your import had a low success rate of {{ number_format($import->success_rate, 1) }}%. 
                        Please review the errors and consider reformatting your data.
                    </div>
                @endif

                @if($import->failed_imports > 0)
                <div class="mt-3">
                    <h6>Common Issues:</h6>
                    <ul class="small">
                        <li>Invalid data format</li>
                        <li>Missing required fields</li>
                        <li>Data too long for database field</li>
                        <li>Foreign key constraint violations</li>
                        <li>Duplicate unique field values</li>
                    </ul>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@section('after_scripts')
<script>
function exportImportLog() {
    var importData = @json($import);
    var filename = 'import_log_' + importData.id + '_' + new Date().toISOString().slice(0,10) + '.json';
    
    var json = JSON.stringify(importData, null, 2);
    downloadFile(json, filename, 'application/json');
}

function downloadFile(content, filename, contentType) {
    var blob = new Blob([content], { type: contentType });
    var url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Auto-refresh if import is still processing
@if($import->status === 'processing')
$(document).ready(function() {
    setTimeout(function() {
        location.reload();
    }, 5000); // Refresh every 5 seconds
});
@endif
</script>

<style>
@media print {
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .btn, .alert {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .col-md-4 {
        display: none;
    }
    
    .col-md-8 {
        width: 100% !important;
    }
}

.progress {
    background-color: #e9ecef;
}

.table th {
    border-top: none;
}

.badge {
    font-size: 0.875em;
}
</style>
@endsection
