<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{\Illuminate\Support\Str::replace(['http://','/','www.'],'',$data['content']['store']->virtual)}}</title>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #3a4454 0%, #2c3440 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        .email-container {
            max-width: 600px;
            margin: 40px auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header-section {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .logo-container {
            position: relative;
            z-index: 2;
        }
        .company-logo {
            max-width: 180px;
            height: auto;
            filter: brightness(0) invert(1);
        }
        .welcome-text {
            color: white;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }
        .content-section {
            padding: 50px 40px;
        }
        .welcome-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 2rem;
        }
        .welcome-message {
            color: #5a6c7d;
            line-height: 1.7;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        .login-button {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            border: none;
            padding: 16px 40px;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(237, 137, 54, 0.3);
        }
        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(237, 137, 54, 0.4);
            color: white;
        }
        .features-section {
            background: #f7fafc;
            padding: 40px;
            margin: 0 -40px;
        }
        .feature-item {
            text-align: center;
            margin-bottom: 30px;
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 1.5rem;
        }
        .footer-section {
            background: #2d3748;
            color: white;
            padding: 30px 40px;
            text-align: center;
            margin: 0 -40px -50px;
        }
        .social-links {
            margin-top: 20px;
        }
        .social-links a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }
        .social-links a:hover {
            opacity: 1;
        }
        @media (max-width: 576px) {
            .email-container {
                margin: 20px;
                border-radius: 12px;
            }
            .content-section {
                padding: 30px 25px;
            }
            .features-section {
                padding: 30px 25px;
                margin: 0 -25px;
            }
            .footer-section {
                padding: 25px;
                margin: 0 -25px -30px;
            }
        }
    </style>
</head>
<body>
<div class="email-container">
    <!-- Header Section -->
    <div class="header-section">
        <div class="logo-container">
            <!-- Replace with your company logo -->
            <img src="https://www.networktoolcat.com/assets/img/te-logo-dark-bg.png" alt="NetworkToolCat.com Logo" class="company-logo">
        </div>
        <div class="welcome-text">
            <h2 class="mb-0">Welcome to {{\Illuminate\Support\Str::replace(['http://','/','www.'],'',$data['content']['store']->virtual)}}</h2>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content-section">
        <h1 class="welcome-title">Welcome Aboard! 🎉</h1>
        <p class="welcome-message">
            We're thrilled to have you join our community! Your account has been successfully created, and you're now ready to explore what we have to offer!
        </p>
        <p class="welcome-message">
            Get started by logging into your account by clicking the link below.
        </p>

        <div class="text-center mb-4">
            <a href="{{$data['content']['store']->virtual}}invite/{{$data['content']->invite->token}}" class="login-button">Login to Your Account</a>
        </div>

        <!-- Features Section -->
        <div class="features-section">
            <h3 class="text-center mb-4" style="color: #2c3e50;">What's Waiting for You</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <span>⚡</span>
                        </div>
                        <h5 style="color: #2c3e50;">Lightning Fast</h5>
                        <p style="color: #5a6c7d;">Experience blazing-fast performance with our optimized platform.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <span>🔒</span>
                        </div>
                        <h5 style="color: #2c3e50;">Secure & Safe</h5>
                        <p style="color: #5a6c7d;">Your data is protected with enterprise-grade security measures.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <span>🎯</span>
                        </div>
                        <h5 style="color: #2c3e50;">User-Focused</h5>
                        <p style="color: #5a6c7d;">Every feature is designed with your success in mind.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p style="color: #5a6c7d; font-size: 0.95rem;">
                Need help getting started? <a href="{{$data['content']['store']->virtual}}contact" style="color: #667eea;">Contact our support team</a> - we're here to help!
            </p>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer-section">
        <p class="mb-2">© 2025 The Pronto Network. All rights reserved.</p>
        <p style="opacity: 0.8; font-size: 0.9rem;">
            You're receiving this email because either you or a colleague created an account with us.
        </p>
        <div class="social-links">
            <a href="#">Twitter</a>
            <a href="https://www.linkedin.com/company/theprontonetwork/">LinkedIn</a>
            <a href="https://www.facebook.com/TheProntoNetwork/">Facebook</a>
            <a href="#">Support</a>
        </div>
    </div>
</div>
</body>
</html>