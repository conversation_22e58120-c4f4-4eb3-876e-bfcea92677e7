@if(! ($entry instanceof  \Sophio\FBSReturns\src\Models\ReturnHeader))
    @php($return= $entry->return)
@else
    @php($return= $entry)
@endif
@if($return)
    @switch($return->status)
        @case('A')
        @case('P')
            @if(!backpack_user()  || backpack_user()->hasRole('customer'))
                In Progress
                @else
            <a href="{{ backpack_url('dbs/'.\Route::current()->parameter('database').'/fbs/returns/'.$return->pk.'/credit') }}">Refund
                Now</a>
            @endif
            @break;
        @case('C')
            Credited<br>
            @if( ($entry instanceof  \Sophio\FBSReturns\src\Models\ReturnHeader))
                {{$return->pk.'-'.$return->returnline->first()->pk}}
            @else
                {{$return->pk.'-'.$entry->pk}}
            @endif
            @break;
        @case('R')
            Rejected
            @break;
        @case('B')
            Bad Tracking
            @break;
        @case('X')
            Cancelled
            @break;
        @case('W')
            Write Off <br>
            @if( ($entry instanceof  \Sophio\FBSReturns\src\Models\ReturnHeader))
                {{$return->pk.'-'.$return->returnline->first()->pk}}
            @else
                {{$return->pk.'-'.$entry->pk}}
            @endif
            @break;
        @default
            @if( ($entry instanceof  \Sophio\FBSReturns\src\Models\ReturnHeader))
            {{\Sophio\Common\Models\FBS\Lookups::where('type','RETCOND')->where('cdata',$return->returnline->first()->condition)->first()->cdata1??$return->status}}
            @else
                {{\Sophio\Common\Models\FBS\Lookups::where('type','RETCOND')->where('cdata',$entry->condition)->first()->cdata1??$return->status}}
            @endif
    @endswitch
@else
    N/A
@endif