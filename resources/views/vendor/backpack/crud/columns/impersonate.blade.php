@if($entry->user)
    <form class="d-inline-block" method="post" action="{{ route('user.impersonateUser',['id'=> $entry->user->id]) }}">
        @csrf
        <button type="submit" class="btn btn-link"><i class="la la-unlock"></i> {{ __('impersonate_user::messages.btn_impersonate') }}</button>
    </form>
@elseif(count($entry->users->filter(function($user,$key) {  return !$user->hasRole('salesrep');}))>0)

    <form class="d-inline-block" method="post" action="{{ route('user.impersonateUser',['id'=> $entry->users->filter(function($user,$key) {  return !$user->hasRole('salesrep');})->first()->id]) }}">
        @csrf
        <button type="submit" class="btn btn-link"><i class="la la-unlock"></i> {{ __('impersonate_user::messages.btn_impersonate') }}</button>
    </form>
@else
    <a class="btn btn-link" href="{{sophio_route('customer/{custpk}/users.create', ['custpk' => $entry->pk])}}"><i class="la la-lock"></i> Create User</a>
@endif