<?php

$dom = new \DOMDocument('1.0');
$dom->preserveWhiteSpace = false;
$dom->formatOutput = true;
$is_xml = true;
if (isset($entry->{$column['name']}) && isset($entry->{$column['name']}[0]) && $entry->{$column['name']}[0] == '<') {
    try {
        $dom->loadXML($entry->{$column['name']});

        $xml_pretty = $dom->saveXML();
    } catch (Exception $exception) {
        $is_xml = false;
        $xml_pretty = $entry->{$column['name']};
    }

} else {
    $is_xml = false;
    $xml_pretty = $entry->{$column['name']};
}
?>

<div class="language-{{($is_xml)?'xml':'json'}}" style="max-width: 1000px"><pre><code >{{$xml_pretty}}</code></pre></div>
