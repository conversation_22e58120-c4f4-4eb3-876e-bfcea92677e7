@php
    $access = $button->meta['access'] ?? Str::of($button->name)->studly();
    $icon = $button->meta['icon'] ?? '';
    $label = $button->meta['label'] ?? Str::of($button->name)->headline();

    $defaultHref = url($crud->route. ($entry?->getKey() ? '/'.$entry?->getKey().'/' : '/') . Str::of($button->name)->kebab());
    $defaultClass = match ($button->stack) {
        'line' => 'btn btn-sm btn-link',
        'top' => 'btn btn-outline-primary',
        'bottom' => 'btn btn-sm btn-secondary',
        default => 'btn btn-outline-primary',
    };

    $wrapper = $button->meta['wrapper'] ?? [];
    $wrapper['element'] = $wrapper['element'] ?? 'a';
    if(isset($wrapper['href'] ) && is_callable($wrapper['href'] )) {
        $wrapper['href']  = $wrapper['href']($entry);
    }
    $wrapper['href'] = $wrapper['href'] ?? $defaultHref;

    $wrapper['class'] = $wrapper['class'] ?? $defaultClass;
@endphp

@if ($access == true || $crud->hasAccess($access))
    <{{ $wrapper['element'] }}
    @foreach ($wrapper as $attribute => $value)
        @if (is_string($attribute))
            {{ $attribute }}="{{ $value }}"
        @endif
    @endforeach
    >
    @if ($icon) <i class="{{ $icon }}"></i> @endif
    {{ $label }}
    </{{ $wrapper['element'] }}>
@endif
