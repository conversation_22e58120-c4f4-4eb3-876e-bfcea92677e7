@include('crud::fields.inc.wrapper_start')
<label>{!! $field['label'] !!}</label>
<div class="input-group "   style="display: none" id="{{ $field['name'] }}_group">
<input
        type="text"
        name="{{ $field['name'] }}"
        value="{{ isset($value) ? json_encode($value) : ''}}"
/>
    <div class="input-group-append" >
        <a href="#" class="btn  btn-info bulk-button " id="{{ $field['name'] }}_switch_names" >Switch to Names</a>
    </div>
</div>
<div class="input-group" id="{{ $field['name'] }}_group_modal">
    <span class="form-control" id="{{ $field['name'] }}_text">
@if($entry->taxonomy && $entry->taxonomy->PartTerminologyID!==0)
            {{$entry->taxonomy->CategoryName}} \ {{$entry->taxonomy->SubCategoryName}}
            \ {{$entry->taxonomy->PartTerminologyName}}
        @else
            Not set
        @endif
    </span>
    <div class="input-group-append" >
        <a href="#" class="btn  btn-primary bulk-button" data-bs-toggle="modal" data-bs-target="#parttypemove_modal"><i
                    class="la la-copy"></i>Change</a>
        <a href="#" class="btn  btn-secondary bulk-button" id="{{ $field['name'] }}_unset" >Unset</a>
        <a href="#" class="btn  btn-info bulk-button" id="{{ $field['name'] }}_switch_id" >Switch to ID</a>
    </div>

</div>


@include('crud::fields.inc.wrapper_end')
@push('after_scripts')

    {{-- DATA TABLES SCRIPT --}}
    @basset('https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js')
    @basset('https://cdn.datatables.net/1.13.1/js/dataTables.bootstrap5.min.js')
    @basset('https://cdn.datatables.net/responsive/2.4.0/js/dataTables.responsive.min.js')
    @basset('https://cdn.datatables.net/responsive/2.4.0/css/responsive.dataTables.min.css')
    @basset('https://cdn.datatables.net/fixedheader/3.3.1/js/dataTables.fixedHeader.min.js')
    @basset('https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.dataTables.min.css')

    @basset(base_path('vendor/backpack/crud/src/resources/assets/img/spinner.svg'))

    <div class="modal" tabindex="-1" role="dialog" id="parttypemove_modal">
        <div class="modal-dialog modal-xl" role="document" style="min-width:60%;">
            <div class="modal-content">
                <div class="modal-body p-2">
                    <table class="taxonomy dataTable table table-striped table-hover display compact table-condensed"
                           id="taxonomy_table">

                        <thead>
                        <tr>
                            <th>CategoryID</th>
                            <th>Category Name</th>

                            <th>SubCategoryName</th>
                            <th>PartTerminologyID</th>
                            <th>PartTerminologyName</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>loading...</td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <th>CategoryID</th>
                            <th>Category Name</th>

                            <th>SubCategoryName</th>
                            <th>PartTerminologyID</th>
                            <th>PartTerminologyName</th>
                            <th></th>
                        </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        $('#{{ $field['name'] }}_unset').on('click',function(){
            $('input[name={{ $field['name'] }}]').val(0);
            $('#{{ $field['name'] }}_text').html('Not Set');
        });
        $('#{{ $field['name'] }}_switch_id').on('click',function(){
            $('#{{$field['name']}}_group_modal').hide();
            $('#{{$field['name']}}_group').show();
        });
        $('#{{ $field['name'] }}_switch_names').on('click',function(){
            $('#{{$field['name']}}_group_modal').show();
            $('#{{$field['name']}}_group').hide();
        });
        $('#parttypemove_modal').on('show.bs.modal', function (e) {

            $('.taxonomy tfoot th').each(function () {
                var title = $(this).text();
                $(this).html('<input type="text" class="input is-small" placeholder="Search ' + title + '" />');
            });
            if ($.fn.DataTable.isDataTable('#taxonomy_table')) {

                $('.taxonomy tfoot input').on('keyup change', function () {
                    parttypemove_modal
                        .column($(this).parent().index() + ':visible')
                        .search(this.value)
                        .draw();
                });
            } else {
                var parttypemove_modal = $('#taxonomy_table').DataTable({
                    pageLength: 10,
                    serverSide: true,
                    order: [[4, 'asc']],
                    responsive: false,
                    search: {
                        search: $('#datatable_search_stack input').val()
                    },
                    ajax: '{{sophio_route('flattenpartshare.taxonomy')}}',
                    columnDefs: [
                        {
                            defaultContent: '<button class="btn btn-primary btn-sm">Assign</button>',
                            targets: -1
                        }
                    ]
                });
                parttypemove_modal.on('click', 'button', function (e) {
                    let data = parttypemove_modal.row(e.target.closest('tr')).data();
                    $('input[name={{ $field['name'] }}]').val(data[3]);
                    $('#{{ $field['name'] }}_text').html(data[1] + ' / ' + data[3] + ' / ' + data[4]);
                    $('#parttypemove_modal').modal('hide');
                });
                $('.taxonomy tfoot input').on('keyup change', function () {
                    parttypemove_modal
                        .column($(this).parent().index() + ':visible')
                        .search(this.value)
                        .draw();
                });
            }
        });

        $('#bulk_pt_num').select2({
            ajax: {
                url: '{{sophio_route('flattenpartshare.parttypesearch')}}',
                data: function (params) {
                    var query = {
                        search: params.term,
                        type: 'public'
                    }

                    // Query parameters will be ?search=[term]&type=public
                    return query;
                }
            }
        }).css("max-height", "400px");

    </script>
@endpush