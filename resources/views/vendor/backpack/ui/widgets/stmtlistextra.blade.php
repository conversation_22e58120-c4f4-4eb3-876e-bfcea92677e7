<div class="alert alert-primary mb-2 py-2 text-center " id="stmttotal_alert">
    {{$crud->query?$crud->query->count():0}}  orders  @if(count(request()->all())>0) valued  at  ${{our_format((clone $crud->query)->selectRaw('sum(amount) as total')->first()->total??0)}} @endif
</div>
@push('after_scripts')
    <script type="text/javascript">
        function stmtlist_after_table_load() {

            if($('#crudTable').DataTable().ajax.json().stmttotal>0) {
                $('#stmttotal_alert').show();
                $('#stmttotal_alert').html($('#crudTable').DataTable().ajax.json().actual_total+' statements valued at $'+$('#crudTable').DataTable().ajax.json().stmttotal)
            }else{
                $('#stmttotal_alert').hide();
            }

        }
        $(document).ready(function(){
            window.crud.addFunctionToDataTablesDrawEventQueue('stmtlist_after_table_load');
        });
    </script>
@endpush