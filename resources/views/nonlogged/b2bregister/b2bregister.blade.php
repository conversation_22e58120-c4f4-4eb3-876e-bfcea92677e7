@extends(backpack_view('layouts.auth'))

@section('content')
    <div class="page">
        <div class="container-xl">
            <div class="card p-5">
                <div class="page-wrapper">
                    <div class="page-header">
                        <div class="row g-2 align-items-center">
                            <div class="col text-center mb-4">
                                <h1 class="hero-title aos-init aos-animate">
                                    Sophio FBS
                                </h1>
                                <h2 class="page-title">
                                    Wholesale Account Registration
                                </h2>
                            </div>

                        </div>
                    </div>


                    <div class="page-body">
                        <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                            @csrf
                            <div class="row row-cards">
                                <div class="col-12">
                                    <div class="card border-0">
                                        <div class="card-header bg-secondary-lt text-dark">
                                            <h3 class="card-title"> Upload Tax Exemption Certificate</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-danger">Important! An invalid id or missing
                                                exemption certificate will cause us to charge or back charge tax on
                                                previous orders.
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Upload file:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="file" class="form-control" name="file" required>
                                                </div>
                                            </div>

                                            <h3 class="card-title">Purchaser Agreement</h2>
                                                <p class="   align-items-center lh-lg   fs-3 align-bottom align-text-bottom">
                                                    <input type="text" placeholder="Company name"
                                                           name="agreement-company" value="{{old('agreement-company')}}"
                                                           class="mt-1  border-bottom  border-top-0  border border-start-0 border-end-0">
                                                    of

                                                    <input type="text" placeholder="Street Address"
                                                           name="agreement-address" value="{{old('agreement-address')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">

                                                    <input type="text" placeholder="City" name="agreement-city"
                                                           value="{{old('agreement-city')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">
                                                    ,
                                                    <input type="text" placeholder="State/Province"
                                                           name="agreement-state" value="{{old('agreement-state')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">

                                                    <input type="text" placeholder="Zip / Postal" name="agreement-zip"
                                                           value="{{old('agreement-zip')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">
                                                    is purchasing
                                                    <input type="text" placeholder="Purchaser Described Items"
                                                           name="agreement-described-items"
                                                           value="{{old('agreement-described-items')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">

                                                    from Sophio FBS of 2601 Heritage Avenue, Grapevine, TX 76051 , and is exempt from sales tax on these purchases. Purchases
                                                    are made for repairing automotibiles
                                                    <input type="text" placeholder="Purchaser Described Exemption"
                                                           name="agreement-described-exemption"
                                                           value="{{old('agreement-described-exemption')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">
                                                    and my account identification number is
                                                    <input type="text" placeholder="  Tax ID Number "
                                                           name="agreement-taxid" value="{{old('agreement-taxid')}}"
                                                           class="mt-1 border-bottom  border-top-0  border border-start-0 border-end-0">
                                                    . This blanket certificate will be deemed valid until the account
                                                    identification number is expired, or statute renders this exemption
                                                    invalid. Further, if this exemption is deemed invalid after
                                                    purchase, purchaser agrees to pay the sales tax liability to the seller. My
                                                    digital signature below acknowledges my acceptance of these
                                                    conditions and that I declare, under penalty of perjury, that the
                                                    information provided is true, that I have consulted the statutes,
                                                    administrative rules, and other sources of law applicable to my
                                                    exemption, and have exercised reasonable care in assuring that my
                                                    claim of exemption is valid.
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Digital Signature:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" value="{{old('agreement-signature')}}"
                                                               class="form-control mt-1 border-bottom  border-top-0  border border-start-0 border-end-0 w-100"
                                                               name="agreement-signature">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Agreement Date:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="date" value="{{old('agreement-date')}}"
                                                               class="form-control mt-1 border-bottom  border-top-0  border border-start-0 border-end-0 w-100"
                                                               name="agreement-date">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Purchaser Title:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" value="{{old('agreement-purchaser-title')}}"
                                                               class="form-control mt-1 border-bottom  border-top-0  border border-start-0 border-end-0 w-100"
                                                               name="agreement-purchaser-title">
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="card border-0">
                                        <div class="card-header bg-secondary-lt text-dark"><h3 class="card-title">
                                                Contact & Ordering Information</h3></div>
                                        <div class="card-body ">
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Company Name*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100" name="company"
                                                           value="{{old('company')}}" required>
                                                    @error('company')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Account Number:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100"
                                                           name="accountnum" value="{{old('accountnum')}}">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Company Website*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100 @error('url') is-invalid @enderror" name="url"
                                                           placeholder="https://" required value="{{old('url')}}">
                                                    @error('url')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">First Name*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100 @error('firstname') is-invalid @enderror"
                                                           name="firstname" required value="{{old('firstname')}}">
                                                    @error('firstname')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">last Name*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100 @error('lastname') is-invalid @enderror" name="lastname"
                                                           required value="{{old('lastname')}}">
                                                    @error('lastname')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">E-mail*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text"
                                                           class="form-control mt-1   w-100 @error('email') is-invalid @enderror"
                                                           name="email" required value="{{old('email')}}">
                                                    @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Mobile Phone Number*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100" name="phone"
                                                           required value="{{old('phone')}}">
                                                    @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Account Type*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <select name="custtype" id="custtype" class="form-select">
                                                        <option selected="" value="">[Choose One]</option>
                                                        <option value="AP">Auto Parts Store</option>
                                                        <option value="BS">Body Shop</option>
                                                        <option value="NCD">Car Dealer</option>
                                                        <option value="B2B">eTailer</option>
                                                        <option value="FLT">Fleet</option>
                                                        <option value="GA">Government Agency</option>
                                                        <option value="IR">Independent Garage</option>
                                                        <option value="MS">Machine Shop</option>
                                                        <option value="AF">Sophio Affiliate</option>
                                                    </select>
                                                    @error('custtype')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Tax Exempt Number*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="text" class="form-control mt-1   w-100" name="taxid"
                                                           required value="{{old('taxid')}}">
                                                    @error('taxid')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Tax Number Expiration
                                                    Date*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <input type="date" class="form-control mt-1   w-100"
                                                           name="taxexpire" required value="{{old('taxexpire')}}">
                                                    @error('taxexpire')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Country/Region*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <select name="countryid" id="countryid" required
                                                            class="form-select">
                                                        <option value="">[Choose One]</option>
                                                        <option value="US" selected="selected">United States</option>
                                                        <option value="AL">Albania</option>
                                                        <option value="AR">Argentina</option>
                                                        <option value="AM">Armenia</option>
                                                        <option value="AU">Australia</option>
                                                        <option value="AT">Austria</option>
                                                        <option value="BE">Belgium</option>
                                                        <option value="BO">Bolivia</option>
                                                        <option value="BR">Brazil</option>
                                                        <option value="BG">Bulgaria</option>
                                                        <option value="KH">Cambodia</option>
                                                        <option value="CA">Canada</option>
                                                        <option value="CL">Chile</option>
                                                        <option value="CN">China</option>
                                                        <option value="CO">Colombia</option>
                                                        <option value="CR">Costa Rica</option>
                                                        <option value="HR">Croatia</option>
                                                        <option value="CU">Cuba</option>
                                                        <option value="CZ">Czech Republic</option>
                                                        <option value="DK">Denmark</option>
                                                        <option value="EC">Ecuador</option>
                                                        <option value="EG">Egypt</option>
                                                        <option value="EE">Estonia</option>
                                                        <option value="ET">Ethiopia</option>
                                                        <option value="FI">Finland</option>
                                                        <option value="FR">France</option>
                                                        <option value="DE">Germany</option>
                                                        <option value="GR">Greece</option>
                                                        <option value="GL">Greenland</option>
                                                        <option value="GD">Grenada</option>
                                                        <option value="GU">Guam</option>
                                                        <option value="GT">Guatemala</option>
                                                        <option value="HT">Haiti</option>
                                                        <option value="HN">Honduras</option>
                                                        <option value="HK">Hong Kong</option>
                                                        <option value="HU">Hungary</option>
                                                        <option value="IS">Iceland</option>
                                                        <option value="IN">India</option>
                                                        <option value="ID">Indonesia</option>
                                                        <option value="IE">Ireland</option>
                                                        <option value="IL">Israel</option>
                                                        <option value="IT">Italy</option>
                                                        <option value="JM">Jamaica</option>
                                                        <option value="JP">Japan</option>
                                                        <option value="LB">Lebanon</option>
                                                        <option value="LI">Liechtenstein</option>
                                                        <option value="LT">Lithuania</option>
                                                        <option value="LU">Luxembourg</option>
                                                        <option value="MG">Madagascar</option>
                                                        <option value="MY">Malaysia</option>
                                                        <option value="MX">Mexico</option>
                                                        <option value="MC">Monaco</option>
                                                        <option value="MA">Morocco</option>
                                                        <option value="NL">Netherlands</option>
                                                        <option value="NZ">New Zealand</option>
                                                        <option value="NI">Nicaragua</option>
                                                        <option value="KP">North Korea</option>
                                                        <option value="NO">Norway</option>
                                                        <option value="PK">Pakistan</option>
                                                        <option value="PA">Panama</option>
                                                        <option value="PE">Peru</option>
                                                        <option value="PH">Philippines</option>
                                                        <option value="PL">Poland</option>
                                                        <option value="PT">Portugal</option>
                                                        <option value="PR">Puerto Rico</option>
                                                        <option value="RO">Romania</option>
                                                        <option value="RU">Russian Federation</option>
                                                        <option value="SA">Saudi Arabia</option>
                                                        <option value="SG">Singapore</option>
                                                        <option value="ZA">South Africa</option>
                                                        <option value="KR">South Korea</option>
                                                        <option value="ES">Spain</option>
                                                        <option value="SE">Sweden</option>
                                                        <option value="CH">Switzerland</option>
                                                        <option value="TW">Taiwan</option>
                                                        <option value="TH">Thailand</option>
                                                        <option value="TR">Turkey</option>
                                                        <option value="UA">Ukraine</option>
                                                        <option value="AE">United Arab Emirates</option>
                                                        <option value="GB">United Kingdom</option>
                                                        <option value="US">United States</option>
                                                        <option value="VE">Venezuela</option>
                                                        <option value="VN">Vietnam</option>
                                                        <option value="VG">Virgin Islands (British)</option>
                                                        <option value="ZZ">Other-Not Shown</option>
                                                    </select>
                                                    @error('countryid')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Primary ordering method:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <select name="ordmethod" class="form-select">
                                                        <option selected="selected" value="Online">Online</option>
                                                        <option value="Email">Email</option>
                                                        <option value="phone">Phone</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Referred By*:</label>
                                                <div class="col-sm-10 col-lg-6 col-md-6">
                                                    <select name="referral" id="referral" class="form-select" required>
                                                        <option selected="" value="">[Choose One]</option>
                                                        <option value="SALES">Fulfillment by Sophio Sales</option>
                                                        <option value="WHI">WHI/eBay</option>
                                                        <option value="Inet_Search">Internet Search</option>
                                                        <option value="Magazine">Magazine Advertisement</option>
                                                        <option value="Jobber_Wd_Manuf">Parts Jobber, WD or
                                                            Manufacturer
                                                        </option>
                                                        <option value="Trade_Associantion">Trade Association (IATN, ASA,
                                                            etc. .)
                                                        </option>
                                                        <option value="Other">Other</option>
                                                    </select>
                                                    @error('referral')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="card border-0">
                                            <div class="card-header bg-secondary-lt text-dark"><h3 class="card-title">
                                                    Payment Information</h3></div>
                                            <div class="card-body ">
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Payment Type:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="radio" name="cctype"
                                                                   id="cctype-COD" value="CO">
                                                            <label class="form-check-label" for="cctype-cod">COD</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="radio" name="cctype"
                                                                   id="cctype-ACH" value="AC">
                                                            <label class="form-check-label" for="cctype-ACH">ACH</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="radio" name="cctype"
                                                                   id="cctype-CC" value="CC">
                                                            <label class="form-check-label" for="cctype-CC">Credit
                                                                Card</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="radio" name="cctype"
                                                                   id="cctype-PP" value="PP">
                                                            <label class="form-check-label"
                                                                   for="cctype-PP">Paypal</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="radio" name="cctype"
                                                                   id="cctype-OP" value="OP">
                                                            <label class="form-check-label" for="cctype-OP">Open
                                                                Account</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Street Address:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               name="address" value="{{old('address')}}">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Address 2:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               name="address2" value="{{old('address2')}}">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">City:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               value="{{old('city')}}"
                                                               name="city">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">State/Province:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <select name="state" id="state" class="form-select">
                                                            <option value="">[Choose One]</option>
                                                            <option value="AL">Alabama</option>
                                                            <option value="AK">Alaska</option>
                                                            <option value="AS">American Samoa</option>
                                                            <option value="AZ">Arizona</option>
                                                            <option value="AR">Arkansas</option>
                                                            <option value="CA">California</option>
                                                            <option value="CO">Colorado</option>
                                                            <option value="CT">Connecticut</option>
                                                            <option value="DE">Delaware</option>
                                                            <option value="DC">District of Columbia</option>
                                                            <option value="FL">Florida</option>
                                                            <option value="GA">Georgia</option>
                                                            <option value="GU">Guam</option>
                                                            <option value="HI">Hawaii</option>
                                                            <option value="ID">Idaho</option>
                                                            <option value="IL">Illinois</option>
                                                            <option value="IN">Indiana</option>
                                                            <option value="IA">Iowa</option>
                                                            <option value="KS">Kansas</option>
                                                            <option value="KY">Kentucky</option>
                                                            <option value="LA">Louisiana</option>
                                                            <option value="ME">Maine</option>
                                                            <option value="MD">Maryland</option>
                                                            <option value="MA">Massachusetts</option>
                                                            <option value="MI">Michigan</option>
                                                            <option value="MN">Minnesota</option>
                                                            <option value="MS">Mississipi</option>
                                                            <option value="MO">Missouri</option>
                                                            <option value="MT">Montana</option>
                                                            <option value="NE">Nebraska</option>
                                                            <option value="NV">Nevada</option>
                                                            <option value="NH">New Hamshire</option>
                                                            <option value="NJ">New Jersey</option>
                                                            <option value="NM">New Mexico</option>
                                                            <option value="NY">New York</option>
                                                            <option value="NC">North Carolina</option>
                                                            <option value="ND">North Dakota</option>
                                                            <option value="CM">Northern Mariana Islands</option>
                                                            <option value="OH">Ohio</option>
                                                            <option value="OK">Oklahoma</option>
                                                            <option value="OR">Oregon</option>
                                                            <option value="PA">Pennsylvania</option>
                                                            <option value="PR">Puerto Rico</option>
                                                            <option value="RI">Rhode Island</option>
                                                            <option value="SC">South Carolina</option>
                                                            <option value="SD">South Dakota</option>
                                                            <option value="TN">Tennessee</option>
                                                            <option value="TX">Texas</option>
                                                            <option value="TT">Trust Territories</option>
                                                            <option value="UT">Utah</option>
                                                            <option value="VT">Vermont</option>
                                                            <option value="VI">Virgin Islands</option>
                                                            <option value="VA">Virginia</option>
                                                            <option value="WA">Washington</option>
                                                            <option value="WV">West Virgina</option>
                                                            <option value="WI">Wisconsin</option>
                                                            <option value="WY">Wyoming</option>
                                                        </select>

                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Zip/Postal Code:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               value="{{old('zip')}}"
                                                               name="zip">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="card border-0">
                                            <div class="card-header bg-secondary-lt text-dark"><h3 class="card-title">
                                                    Nexpart/WebShop Information</h3></div>
                                            <div class="card-body ">

                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Username:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               value="{{old('b2busername')}}"
                                                               name="b2busername">
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Password:</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <input type="text" class="form-control mt-1   w-100"
                                                               value="{{old('b2bpassword')}}"
                                                               name="b2bpassword">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="card border-0">
                                            <div class="card-header bg-secondary-lt text-dark"><h3 class="card-title">
                                                    General Information</h3></div>
                                            <div class="card-body ">

                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Estimated Monthly
                                                        Volume::</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <select name="annualpvol" class="form-select">
                                                            <option selected="selected" value="">Select One</option>
                                                            <option value="$0 – $100,000 USD">$0 – $100,000 USD</option>
                                                            <option value="$100,000 – $500,000 USD">$100,000 – $500,000
                                                                USD
                                                            </option>
                                                            <option value="$500,000 – $1 Million USD">$500,000 – $1
                                                                Million USD
                                                            </option>
                                                            <option value="$1 Million+ USD">$1 Million+ USD</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group row">
                                                    <label class="col-sm-2 col-form-label">Comment::</label>
                                                    <div class="col-sm-10 col-lg-6 col-md-6">
                                                        <textarea type="text" class="form-control mt-1   w-100"
                                                                  name="notes">{{old('notes')}}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12  p-4 d-flex justify-content-center align-items-center flex-wrap">
                                    <div class="mx-auto w-100 ">
                                        <div class="btn-list justify-content-center">
                                            <input type="submit" class="btn btn-primary"
                                                   value="Submit">&nbsp;
                                            <input type="reset" value="Reset" class="btn btn-light">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
    </div>

@endsection
@push('after_scripts')
    <script type="text/javascript">

        $(document).ready(function () {

            $('form input').focus(function () {
                $(this).removeClass('is-invalid');
                $(this).siblings(".invalid-feedback").hide();
            });
            $('form select').focus(function () {
                $(this).removeClass('is-invalid');
                $(this).siblings(".invalid-feedback").hide();
            });
        });
    </script>
@endpush