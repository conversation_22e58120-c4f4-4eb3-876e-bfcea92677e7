<?php
Route::get('/taxreportdetail.wws', function () {
    return redirect()->to(sophio_route('fbs/report.taxreportdetail',request()->all()));
});
Route::group(
    [
        'namespace' => 'Backpack\CRUD\app\Http\Controllers',
        'middleware' => config('backpack.base.web_middleware', 'web'),
        'prefix' => config('backpack.base.route_prefix'),
    ],
    function () {
        // if not otherwise configured, setup the auth routes
        if (config('backpack.base.setup_auth_routes')) {
            // Authentication Routes...

            Route::get('login', 'Auth\LoginController@showLoginForm')->name('backpack.auth.login');
            Route::post('login', 'Auth\LoginController@login');
            Route::get('logout', 'Auth\LoginController@logout')->name('backpack.auth.logout');
            Route::post('logout', 'Auth\LoginController@logout');

            // Registration Routes...
            Route::get('register', 'Auth\RegisterController@showRegistrationForm')->name('backpack.auth.register');
            Route::post('register', 'Auth\RegisterController@register');

            // if not otherwise configured, setup the password recovery routes
            if (config('backpack.base.setup_password_recovery_routes', true)) {
                Route::get('password/reset', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('backpack.auth.password.reset');
                Route::post('password/reset', 'Auth\ResetPasswordController@reset');
                Route::get('password/reset/{token}', 'Auth\ResetPasswordController@showResetForm')->name('backpack.auth.password.reset.token');
                Route::post('password/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('backpack.auth.password.email')->middleware('backpack.throttle.password.recovery:' . config('backpack.base.password_recovery_throttle_access'));
            }
        }

        // if not otherwise configured, setup the dashboard routes
        if (config('backpack.base.setup_dashboard_routes')) {
            Route::get('dashboard', '\App\Http\Controllers\Admin\DashboardController@dashboard')->name('backpack.dashboard');
            Route::get('/', 'AdminController@redirect')->name('backpack');
        }

        // if not otherwise configured, setup the "my account" routes
        if (config('backpack.base.setup_my_account_routes')) {

            # User account related
            Route::get('edit-account-info', 'MyAccountController@getAccountInfoForm')->name('backpack.account.info');
            Route::post('edit-account-info', 'MyAccountController@postAccountInfoForm')->name('backpack.account.info.store');
            Route::post('change-password', 'MyAccountController@postChangePasswordForm')->name('backpack.account.password');
            Route::get('my-settings', 'MyAccountController@getMySettings');
            Route::post('my-settings', 'MyAccountController@postMySettings');
        }
    });


//---
// Admin area
//---
Route::group([
    'prefix' => config('backpack.base.route_prefix', 'admin'),
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin')],
    'namespace' => 'App\Http\Controllers\Admin',
], function () { // custom admin routes
    Route::any('/amzn/consent', '\App\Http\Controllers\Api\AmznController@consent')->name('amzn/consent');
    Route::any('/amzn/redirecturi', '\App\Http\Controllers\Api\AmznController@redirecturi');
    ### Non-tenant
    Route::group(['middleware' => ['role:super-admin']], function () {
        Route::any('activity', 'AdminActivityController@index')->name('admin.activity');
    });
    Route::group(['middleware' => ['role:super-admin|store-admin']], function () {
        # Website configs
        Route::crud('website', 'WebsiteCrudController');
        Route::crud('theme', 'ThemeCrudController');
        #Catalogs
        Route::crud('catalog', 'CatalogCrudController');
        Route::any('catalog/dashboard', 'Catalog\DashboardController@index');
        Route::any('catalog/dashboard/execute', 'Catalog\DashboardController@execute');
        Route::any('catalog/dashboard/awda', 'Catalog\DashboardController@awda');
        Route::crud('aaia/brands', 'AAIA\BrandsCrudController');
        Route::crud('aaia/brandsmix', 'AAIA\BrandsMixCrudController');
        Route::crud('aaia/brandslogo', 'AAIA\BrandLogosCrudController');
        Route::crud('aaia/part', 'AAIA\PartsCrudController');
        Route::crud('aaia/subcategory', 'AAIA\SubCategoryCrudController');
        Route::crud('aaia/category', 'AAIA\CategoryCrudController');
        #UI for Amzn price check
        Route::any('amazonpricecheck', 'Orders\DashboardController@amazonpricecheck');

        # Zoro
        Route::any('zoroexport/contactpk', 'ZoroExportController@contactpk');
        Route::any('zoroexport/npicontactpk', 'ZoroExportController@npicontactpk');
        Route::any('zoroexport/npiinventory', 'ZoroExportController@npiinventory');

        #TractorSupply
        Route::any('tractor/prepareimages', 'TractorController@prepareimages');
        Route::any('tractor/stats', 'TractorController@stats');
        Route::any('tractor/startonboard', 'TractorController@startonboard');
        Route::any('tractor/countnewproducts', 'TractorController@countnewproducts');
        Route::any('tractor/refreshproducts', 'TractorController@refreshproducts');
        Route::any('tractor/index', 'TractorController@index');

        #CommerceHub (Homedepot)
        Route::any('commercehub/index', 'HomeDepotController@index');
        Route::any('commercehub/startonboard', 'HomeDepotController@startonboard');

        #Walmart
        Route::any('walmart/sendfeed', 'Walmart\WalmartExportController@sendfeed')->name('walmart.sendfeed');
        Route::any('walmart/refresh', 'Walmart\WalmartExportController@refresh');
        Route::any('walmart/onboard', 'Walmart\WalmartExportController@onboard');
        Route::any('walmart/dashboard', 'Walmart\DashboardController@index');
        Route::any('walmart/usps', 'Walmart\DashboardController@usps');
        Route::any('walmart/penalties', 'Walmart\DashboardController@penalties');
        Route::post('walmart/startfeed', 'Walmart\DashboardController@startfeed');
        Route::post('walmart/publishprogress', 'Walmart\DashboardController@publishprogress');
        Route::any('walmart/checkproductsreport', 'Walmart\WalmartExportController@checkproductsreport');
        Route::crud('walmart/itemfeed', 'Walmart\ItemsFeedsCrudController');
        Route::any('walmart/itemsfeed/checkstatus/{id}', 'Walmart\ItemsFeedsCrudController@checkfeedstatus');
        Route::crud('walmart/feediteminventoryhistory', 'Walmart\FeedItemsInventoryHistoryCrudController');
        Route::crud('walmart/feeditemcosthistory', 'Walmart\FeedItemsCostHistoryCrudController');
        Route::any('walmart/marketplaceitem/{id}/check', 'Walmart\MarketplaceitemsCrudController@check');
        Route::crud('walmart/marketplaceitem', 'Walmart\MarketplaceitemsCrudController');
        Route::crud('walmart/costfeed', 'Walmart\CostFeedsCrudController');
        Route::any('walmart/costfeed/checkstatus/{id}', 'Walmart\CostFeedsCrudController@checkfeedstatus');
        Route::crud('walmart/inventoryfeed', 'Walmart\InventoryFeedsCrudController');
        Route::any('walmart/inventoryfeed/checkstatus/{id}', 'Walmart\InventoryFeedsCrudController@checkfeedstatus');
        Route::crud('walmart/feeditem', 'Walmart\FeedItemsCrudController');
        Route::crud('walmart/transaction', 'Walmart\ApiTransactionLogCrudController');
        Route::crud('walmart/dsv', 'Walmart\DSVCrudController');
        Route::crud('walmart/remit', 'Walmart\RemitCrudController');
        # Orderlink test
        Route::any('orderlink/stockcheck', 'OrderLinkController@stockcheck');
        #Faceted API admin
        Route::any('nonappcatalog', 'FacetedApiController@nonappcatalog');
        Route::any('createfacetedcatalogapiuser', 'FacetedApiController@createfacetedcatalogapiuser');
        Route::any('createfacetedinventorydatabase', 'FacetedApiController@createfacetedinventorydatabase');

        #Products (centralized product information)
        Route::crud('pim/product', 'PIM\ProductCrudController');
        Route::crud('pim/amazon', 'PIM\AmazonCrudController');
        Route::crud('pim/image', 'PIM\ImageCrudController');
        Route::crud('needscontent', 'Catalog\NeedsContentCrudController');
        Route::crud('visiondcf', 'Catalog\VisionDcfCrudController');
        Route::crud('adnextract', 'Catalog\AdnExtractCrudController');;
        Route::crud('adnextractdcf', 'Catalog\AdnExtractDcfCrudController');;
        Route::crud('flattenpartsharelinecode', 'Catalog\FlattenedPartShareLineCrudController');
        Route::crud('flattenpartshare', 'Catalog\FlattenedPartShareCrudController');

        Route::any('product/contactpk', 'PIM\ProductCrudController@contactpk');
        Route::any('product/tractor', 'PIM\ProductCrudController@tractor');
        Route::crud('aaia/images', 'Catalog\AcesImagesCrudController');
        Route::any('manticore', 'Catalog\ManticoreController@index');
        Route::any('manticore/reindex', 'Catalog\ManticoreController@reindex');
        #Boxes used in Boxing API
        Route::crud('box', 'BoxCrudController');

        # Lookups (term definitions)
        Route::crud('lookups', 'LookupsCrudController');

        # General CSV Import Tool
        Route::get('general-csv-import', 'GeneralCsvImportController@index')->name('general-csv-import.index');
        Route::post('general-csv-import/upload', 'GeneralCsvImportController@upload')->name('general-csv-import.upload');
        Route::post('general-csv-import/import', 'GeneralCsvImportController@import')->name('general-csv-import.import');
        Route::get('general-csv-import/history', 'GeneralCsvImportController@history')->name('general-csv-import.history');
        Route::get('general-csv-import/statistics', 'GeneralCsvImportController@statistics')->name('general-csv-import.statistics');
        Route::get('general-csv-import/{id}', 'GeneralCsvImportController@show')->name('general-csv-import.show');
        Route::get('general-csv-import/download-template', 'GeneralCsvImportController@downloadTemplate')->name('general-csv-import.download-template');
        Route::post('general-csv-import/model-fields', 'GeneralCsvImportController@getModelFields')->name('general-csv-import.model-fields');
        Route::post('general-csv-import/delete-file', 'GeneralCsvImportController@deleteFile')->name('general-csv-import.delete-file');

        # Charts for API orders
        Route::get('charts/{database}/acceptedorders', 'Charts\AcceptedOrdersChartController@response');
        Route::get('charts/{database}/dailyorders', 'Charts\DailyOrdersChartController@response');
        Route::get('charts/{database}/ordersalesmonth', 'Charts\OrderSalesMonthChartController@response');
        Route::get('charts/{database}/ordersalesweek', 'Charts\OrderSalesWeekChartController@response');
        Route::get('charts/{database}/ordersalesday', 'Charts\OrderSalesDayChartController@response');
        Route::get('charts/{database}/ordersalesyear', 'Charts\OrderSalesYearlyDayChartController@response');
        Route::get('charts/{database}/orderstockcheckyear', 'Charts\OrderStockChecksYearChartController@response');
        Route::get('charts/{database}/ratioordercheck', 'Charts\RatioOrderCheckChartController@response');

        #Charts for invoices
        Route::get('charts/{database}/fbs/fbsorderdashboard/dailysales', 'Charts\FBS\DailySalesChartController@response');
        Route::get('charts/{database}/fbs/fbsorderdashboard/monthlysales', 'Charts\FBS\MonthlySalesChartController@response');
        Route::get('charts/{database}/fbs/fbsorderdashboard/nonshipped', 'Charts\FBS\NonShippedChartController@response');
    });
    #WHI Product feed
    Route::crud('productfeed', 'ProductFeedCrudController');


    ### Tenant area
    Route::group([
        'prefix' => 'dbs/{database}'
    ], function () {
        #FBS dashboard
        Route::get('dashboard', 'AdminController@dashboard');
        Route::group(['middleware' => ['role:customer|super-admin|store-admin']], function () {
            Route::crud('customer/order', 'Customer\CustomerOrderCrudController');
            Route::crud('customer/return', 'Customer\CustomerReturnCrudController');
            Route::crud('customer/account', 'Customer\CustomerAccountController');
            Route::crud('customer/{custpk}/users', 'Customer\CustomerUsersCrudController');
            Route::crud('customer/users', 'Customer\CustomerUsersCrudController');
            Route::crud('customer/{custpk}/buyers', 'Customer\BuyersCrudController');
            Route::crud('customer/buyers', 'Customer\BuyersCrudController');
            Route::crud('customer/{custpk}/custshipto', 'Customer\CustShipToCrudController');
            Route::crud('customer/custshipto', 'Customer\CustShipToCrudController');


        });


        Route::group(['middleware' => ['role:super-admin|store-admin|returns|supplier']], function () {
            #Supplier
            Route::crud('fbs/supplier', 'FBS\SupplierCrudController');
            Route::get('fbs/supplier/{id}/downloaddcf', 'FBS\SupplierCrudController@downloaddcf');
            Route::get('fbs/supplier/{id}/rawdcf', 'FBS\SupplierCrudController@rawdcf');
            Route::crud('fbs/supplier/{sid}/shipstation/warehouse', 'FBS\ShipstationWarehouseCrudController');
            #Invoices
            Route::crud('fbs/purchaseorder', 'FBS\PurchaseOrdersCrudController');
            Route::crud('fbs/statement', 'FBS\StatementCrudController');
            Route::crud('fbs/shippingrule', 'FBS\ShippingRuleCrudController');
            Route::crud('fbs/statementraw', 'FBS\StatementRawCrudController');
            Route::get('fbs/fbsorderdashboard/openorders', 'FBS\OrderDashboardController@openorders')->name('fbs/fbsorderdashboard.openorders');
            Route::get('fbs/fbsorderdashboard/fix', 'FBS\OrderDashboardController@fix')->name('fbs/fbsorderdashboard.fix');
            Route::get('fbs/fbsorderdashboard/findmissingautofills', 'FBS\OrderDashboardController@findmissingautofills')->name('fbs/fbsorderdashboard.findmissingautofills');
            Route::get('fbs/fbsorderdashboard/trytodayreview', 'FBS\OrderDashboardController@trytodayreview')->name('fbs/fbsorderdashboard.trytodayreview');
            Route::resource('fbs/fbsorderdashboard', 'FBS\OrderDashboardController');
            #Invoice line items
            Route::crud('fbs/fbslineitems', 'FBS\LineItemCrudController');
            Route::crud('fbs/fbslineitem', 'FBS\LineItemCrudController');
            Route::crud('catalog/parttypedcf', 'Catalog\ParttypeDcfCrudController');
            Route::crud('catalog/supplierdcf', 'Catalog\SupplierDcfCrudController');
            Route::crud('catalog/brands', 'Catalog\BrandsCrudController');
            Route::any('catalog/sales/upload', 'Catalog\SalesController@upload')->name('catalog/sales.upload');

            #Returns
            Route::crud('fbs/returnreasons', 'FBS\ReturnReasonsCrudController');
            Route::crud('fbs/returns', 'FBS\ReturnsCrudController');

            Route::crud('fbs/returnlines', 'FBS\ReturnLinesCrudController');
            Route::crud('fbs/missingcredits', 'FBS\MissingCreditsCrudController');
            Route::crud('fbs/manifest', 'FBS\ManifestCrudController');
            Route::crud('fbs/imports', 'FBS\ImportsCrudController');
            Route::crud('fbs/importedinventory', 'FBS\ImportedInventoryCrudController');
            #Reports
            Route::any('fbs/report/opportunities', 'FBS\ReportsController@opportunities')->name('fbs/report.opportunities');
            Route::any('fbs/report/salesbycusttype', 'FBS\ReportsController@salesbycusttype')->name('fbs/report.salesbycusttype');
            Route::any('fbs/report/returnallowance', 'FBS\ReportsController@returnallowance')->name('fbs/report.returnallowance');

            Route::any('fbs/report/penalties', 'FBS\ReportsController@penalties')->name('fbs/report.penalties');
            Route::any('fbs/report/salesbymonth', 'FBS\ReportsController@salesbymonth')->name('fbs/report.salesbymonth');
            Route::crud('fbs/productwarehouseoverride', 'FBS\ProductWarehouseOverrideCrudController');
            Route::get('fbs/dcfsupplier', 'FBS\DCFSupplierController@index')->name('fbs/dcfsupplier.index');
            Route::get('fbs/dcfsupplier/togglelinecode', 'FBS\DCFSupplierController@togglelinecode')->name('fbs/dcfsupplier.togglelinecode');
            Route::get('fbs/dcfsupplier/exportunmapped', 'FBS\DCFSupplierController@exportUnmapped')->name('fbs/dcfsupplier.exportUnmapped');
            Route::any('fbs/report/tebrands', 'FBS\ReportsController@tebrands')->name('fbs/report.tebrands');
            Route::any('fbs/report/localbrands', 'FBS\ReportsController@localbrands')->name('fbs/report.localbrands');
        });

        Route::group(['middleware' => ['role:super-admin|store-admin']], function () {
            Route::crud('fbs/fbsorder', 'FBS\OrderCrudController');
            Route::crud('fbs/pricing/{db}/db', 'FBS\PricingCrudController');
            Route::crud('fbs/pricing/{accountnum}/accountnum', 'FBS\PricingCrudController');
            Route::crud('fbs/discount/{db}/db', 'FBS\DiscountCrudController');
            Route::any('fbs/pricing/dashboard', 'AdminController@pricing')->name('fbs/pricing.dashboard');
            # Product Warehouse
            Route::crud('productwarehouse', 'FBS\ProductwarehouseCrudController');
            Route::crud('historyfeed', 'FBS\HistoryFeedCrudController');
            Route::crud('maillog', 'FBS\MailLogController');
            Route::crud('partshare', 'FBS\PartsShareCrudController');
            Route::get('productwarehouse/{id}/rtsc', 'FBS\ProductwarehouseCrudController@rtsc');
            Route::get('productwarehouse/{id}/blacklist', 'FBS\ProductwarehouseCrudController@blacklist');
            Route::get('productwarehouse/{id}/whitelist', 'FBS\ProductwarehouseCrudController@whitelist');

            Route::any('fbs/fbsorder/processneworder', 'FBS\OrderCrudController@processneworder');
            Route::any('fbs/fbsorder/{id}/main', 'FBS\OrderCrudController@main')->name('fbs/fbsorder.main');
            Route::any('fbs/fbsorder/main', 'FBS\OrderCrudController@main')->name('fbs/fbsorder.main2');
            Route::any('fbs/fbsorder/shipping', 'FBS\OrderCrudController@shipping');
            Route::any('fbs/fbsorder/whdmanualcheck', 'FBS\OrderCrudController@whdmanualcheck');
            Route::any('fbs/fbsorder/whdautocheck', 'FBS\OrderCrudController@whdautocheck');
            Route::any('fbs/fbsorder/whdorder', 'FBS\OrderCrudController@whdorder')->name('fbs/fbsorder.whdorder');
         //   Route::any('fbs/fbsorder/ordesp', 'FBS\OrderCrudController@ordesp')->name('fbs/fbsorder.ordesp');
            Route::any('fbs/fbsorder/whdconfirm', 'FBS\OrderCrudController@whdconfirm');

            Route::any('fbs/fbsorder/duptosophio', 'FBS\OrderCrudController@duptosophio');
            Route::any('fbs/fbsorder/lastsupplierlogs', 'FBS\OrderCrudController@lastsupplierlogs')->name('fbs/fbsorder.lastsupplierlogs');
            Route::any('fbs/fbsorder/orderprint', 'FBS\OrderCrudController@orderprint');
            Route::any('fbs/fbsorder/orderfromsophio', 'FBS\OrderCrudController@orderfromsophio');
            Route::get('fbs/fbsorder/shiptrackstatus', 'FBS\OrderCrudController@shiptrackstatus');
            Route::get('fbs/fbsorder/sellerfulfillmentstatus', 'FBS\OrderCrudController@sellerfulfillmentstatus');
            Route::get('fbs/fbsorder/updateshiptracking', 'FBS\OrderCrudController@updateshiptracking');
            Route::get('fbs/fbsorder/getratesforsupplier', 'FBS\OrderCrudController@getratesforsupplier');
            Route::get('fbs/fbsorder/sendordertoshipstation', 'FBS\OrderCrudController@sendordertoshipstation');
            #Data Exports
            Route::any('fbs/datafeed/index', 'FBS\DataFeedsController@index')->name('fbs.datafeed.index');
            Route::post('fbs/datafeed/refresh', 'FBS\DataFeedsController@refresh')->name('fbs.datafeed.refresh');
            Route::crud('fbs/dataexport', 'FBS\DataFeedExportController');
            Route::any('fbs/dataexport/download', 'FBS\DataFeedExportController@download');
            Route::any('fbs/dataexport/send', 'FBS\DataFeedExportController@send');

            #DCF
            Route::any('fbs/dcf/index', 'FBS\DCFController@index')->name('fbs/dcf.index');
            Route::any('fbs/dcf/exportmarket', 'FBS\DCFController@exportmarket')->name('fbs/dcf.exportmarket');
            Route::any('fbs/dcf/downloadall', 'FBS\DCFController@downloadall')->name('fbs/dcf.downloadall');
            Route::any('fbs/dcf/downloadMasterDCf', 'FBS\DCFController@downloadMasterDCf')->name('fbs/dcf.downloadMasterDCF');

            Route::any('fbs/report/salesbydow', 'FBS\ReportsController@salesbydow')->name('fbs/report.salesbydow');
            Route::any('fbs/report/salesbydows', 'FBS\ReportsController@salesbydows')->name('fbs/report.salesbydows');
            Route::any('fbs/report/salesbyuser', 'FBS\ReportsController@salesbyuser')->name('fbs/report.salesbyuser');
            Route::any('fbs/report/csrreport', 'FBS\ReportsController@csrreport')->name('fbs/report.csrreport');

            Route::any('fbs/report/endofday', 'FBS\ReportsController@endofday')->name('fbs/report.endofday');
            Route::any('fbs/report/whmcsendofday', 'FBS\ReportsController@whmcsendofday')->name('fbs/report.whmcsendofday');
            Route::any('fbs/report/salesprojection', 'FBS\ReportsController@salesprojection')->name('fbs/report.salesprojection');
            Route::any('fbs/report/trackingreportitemsales', 'FBS\ReportsController@trackingreportitemsales')->name('fbs/report.trackingreportitemsales');
            Route::any('fbs/report/salesbymfr', 'FBS\ReportsController@salesbymfr')->name('fbs/report.salesbymfr');
            Route::any('fbs/report/salesbyparttype', 'FBS\ReportsController@salesbyparttype')->name('fbs/report.salesbyparttype');
            Route::any('fbs/report/networksalebystate', 'FBS\ReportsController@networksalebystate')->name('fbs/report.salesbystate');
            Route::any('fbs/report/corereport', 'FBS\ReportsController@corereport')->name('fbs/report.corereport');

            Route::any('fbs/report/salesbycategory', 'FBS\ReportsController@salesbycategory')->name('fbs/report.salesbycategory');
            Route::any('fbs/report/trackingreporttopcust', 'FBS\ReportsController@trackingreporttopcust')->name('fbs/report.trackingreporttopcust');
            Route::any('fbs/report/getoutemails', 'FBS\ReportsController@getoutemails')->name('fbs/report.getoutemails');
            Route::any('fbs/report/projection', 'FBS\ReportsController@projection')->name('fbs/report.projection');
            Route::any('fbs/report/cancellations', 'FBS\ReportsController@cancellations')->name('fbs/report.cancellations');
            Route::any('fbs/report/salesbyinvstatus', 'FBS\ReportsController@salesbyinvstatus')->name('fbs/report.salesbyinvstatus');
            Route::any('fbs/report/networkbelowcost', 'FBS\ReportsController@networkbelowcost')->name('fbs/report.networkbelowcost');
            Route::any('fbs/report/returnperformance', 'FBS\ReportsController@returnperformance')->name('fbs/report.returnperformance');
            Route::any('fbs/report/networkdashboard', 'FBS\ReportsController@networkdashboard')->name('fbs/report.networkdashboard');
            Route::any('fbs/report/refundanaylsis', 'FBS\ReportsController@refundanalysis')->name('fbs/report.refundanalysis');
            Route::any('fbs/report/taxreportdetail', 'FBS\ReportsController@taxreportdetail')->name('fbs/report.taxreportdetail');
            Route::any('fbs/report/salesbycustomer', 'FBS\ReportsController@salesbycustomer')->name('fbs/report.salesbycustomer');
            Route::any('fbs/report/shippinganalysis', 'FBS\ReportsController@shippinganalysis')->name('fbs/report.shippinganalysis');
            Route::any('fbs/report/networkmonthend', 'FBS\ReportsController@networkmonthend')->name('fbs/report.networkmonthend');

            #Inventory Imports
            Route::any('fbs/inventoryimport/index', 'FBS\InventoryImportController@index')->name('fbs/inventoryimport.index');
            Route::post('fbs/inventoryimport/import', 'FBS\InventoryImportController@import')->name('fbs/inventoryimport.import');
            Route::post('fbs/inventoryimport/importall', 'FBS\InventoryImportController@importall');
            Route::post('fbs/inventoryimport/warehouseactions', 'FBS\InventoryImportController@warehouseactions');
            Route::post('fbs/inventoryimport/postimport', 'FBS\InventoryImportController@postimport');
            Route::crud('fbs/importsbatch', 'FBS\ImportsBatchCrudController');
            #Customers

            Route::crud('fbs/customer', 'FBS\CustomerCrudController');

            #Marketplace
            Route::crud('fbs/marketplace', 'FBS\MarketplaceCrudController');
            Route::crud('fbs/apikey', 'FBS\ApikeyCrudController');

            #Marketplace Suppliers
            Route::crud('fbs/marketplacesupplier', 'FBS\MarketplaceSupplierCrudController');


            #Partshare

            #Store
            Route::crud('fbs/store', 'FBS\StoreCrudController');
            #Payment Details
            Route::crud('fbs/paymentdetail', 'FBS\PaymentDetailCrudController');
            #Order link
            Route::any('orderlink/stockcheck', 'OrderLinkController@stockcheck')->name('orderlink/stockcheck');
            Route::any('orderlink/order', 'OrderLinkController@order');

            #System log
            Route::crud('fbs/systemlog', 'FBS\SystemLogCrudController');

            #Actions (non-important)
            Route::crud('fbs/action', 'FBS\ActionCrudController');
            Route::any('fbs/action/{id}/execute', 'FBS\ActionCrudController@execute')->name('fbs.action.execute');
            ## Non-FBS routes
            # API orders
            Route::crud('rejectedorder', 'Orders\RejectedOrderCrudController');
            Route::any('order/transferordertofbs', 'Orders\OrderCrudController@transferordertofbs');
            Route::crud('order', 'Orders\OrderCrudController');
            Route::crud('logcreate', 'Orders\LogCreateCrudController');
            Route::crud('logstock', 'Orders\LogStockCrudController');
            Route::crud('logcheck', 'Orders\LogCheckCrudController');
            Route::crud('logorderlink', 'Orders\LogOrderLinkCrudController');
            Route::crud('supplierlog', 'Orders\SupplierLogCrudController');
            Route::get('orderdashboard', 'Orders\DashboardController@index');

        });
        Route::group(['middleware' => ['role:super-admin|store-admin|marketing']], function () {
            # Non-Apps catalogs
            Route::crud('wwsitem', 'WwsitemCrudController');
            Route::crud('nonappscategory', 'NonApps\CategoryCrudController');
            Route::crud('nonappssubcategory', 'NonApps\SubCategoryCrudController');
            Route::crud('nonappspart', 'NonApps\PartCrudController');
            Route::crud('nonappsmanufacturer', 'NonApps\ManufacturerCrudController');
            Route::crud('partmaster', 'NonApps\PartMasterCrudController');


        });
    });
});

