<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\RocketShipIt\src\Library\RocketShipItManager;
use Sophio\Common\ShipStation\src\Library\Actions\CreateOrder;
use Sophio\Common\ShipStation\src\Library\Actions\NewShippingOrdersFromInvoice;
use Sophio\Common\ShipStation\src\Library\Service\Warehouses;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);
$sm = new ShipStationManager(new \Sophio\Common\Repository\Settings());
$sm->setCredentials('87b2914c676245b882e81cffad62b150', '6390d0ec1bcf47189f65a7ccddac8629');
//$sm->setSupplierCreds(\Sophio\Common\Models\FBS\Supplier::find(982));
$sm->createClient();
print_r (json_encode($sm->getClient()->stores->get()));
exit();
$settings = new \Sophio\Common\Repository\Settings([['tenant_db'=>'sophio_fbs']]);
$seller = \Sophio\Common\Models\FBS\Customer::find(288065);
$settings->setSettingsByCustomer($seller);
$suppliers = \Sophio\Common\Models\FBS\Supplier::where('ACTIVE',1)->where('sup','WHD')->where('PK',971)
    ->whereHas('marketplacesupplier', function ($query) {
        $query->where('market','WAL');
    })->get();
foreach($suppliers as $supplier) {
    if(isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && $supplier->SETTINGS['TRACKINGNUMBERTYPE']==='SHIPSTATIONCUSTOM'){}else{
        echo $supplier->NAME."\n";
        $ssManager = new ShipStationManager(new \Sophio\Common\Repository\Settings());
        $ssManager->setSupplierCreds($supplier);
        $ssManager->createClient();
        $warehouseManager = new Warehouses($ssManager);
        //$warehouseid = $warehouseManager->createWarehouse($settings->getStore(), 'WAL', $seller, $supplier);
        $ws = $ssManager->listWarehouses();
        $wares = [];

        foreach($ws as $w) {
            if(Str::contains($w->warehouseName, 'WALMART', true) && $w->warehouseId!=424411 && $w->warehouseId!==424411 && $w->originAddress->postalCode=='76712') {
                $wares[] = $w;

                $ssManager->deleteWarehouse($w->warehouseId);
            }
        }
        dd(count($wares));
        dd($wares);
        echo $warehouseid."\n";
    }

}
dd('exit');



$ssManager->setSupplierCreds($supplier);
$ssManager->createClient();
$warehouseManager = new Warehouses($ssManager);
dd($warehouseManager->createWarehouse($settings->getStore(), 'WAL', $seller, $supplier));

dd($ssManager->getClient()->stores->get());
$invoice = Invoice::find(10020569);
$ssManager->assignUser($invoice, $supplier, $supplier->SETTINGS['SHIPSTATIONUSERKEY']);
dd((string)Carbon::now()->subMinutes(30)->setTimezone('PST')->toIso8601String());
$settings = new \Sophio\Common\Repository\Settings();
$find  = new \Sophio\Common\ShipStation\src\Library\Actions\FindTrackingBySupplierShipments($settings);
$invoices = Invoice::
whereBetween('invdate', [Carbon::now()->subDays(30),Carbon::now()->subMinutes(30)])
    ->where('invstatus', 'W')
    ->whereHas('lineitem', function ($query) {
        $query->where('sup_ord_id', '<>', '')
            ->where('track_num', '');
    })
    ->orderBy('invdate', 'ASC')->get();
$find($invoices);
exit();
config(['logging.default'=>'shipstation']);
$settings = new \Sophio\Common\Repository\Settings();
$invoice = \Sophio\Common\Models\FBS\Invoice::find(21571140);
(new NewShippingOrdersFromInvoice())($invoice, $settings);
exit();
$shipstationManager = new ShipStationManager($settings);
$supplier = \Sophio\Common\Models\FBS\Supplier::find(753);
$shipstationManager->setSupplierCreds($supplier, false);
$shipstationManager->createClient();
$rocketShipItManager = new RocketShipItManager($settings);


$order = (new CreateOrder($shipstationManager, $rocketShipItManager))($invoice, $supplier,$invoice->lineitem, null, 'PRIMARY', false, $settings);
echo "HELLO";

dd($order);
config(['logging.default'=>'shipstation']);
$settings = new \Sophio\Common\Repository\Settings(['tenant_db'=>'sophio_fbs','fbs_database'=>'sophio_fbs']);
$settings->setSettingsByStore(\Sophio\Common\Models\FBS\Store::where('STOREPK',config('sophio.admin.default_store'))->first());
/*
(new \Sophio\Common\ShipStation\src\Library\Actions\GetCancelledOrders($settings)) ();
exit();
*/
/*
$shipstationManager = new ShipStationManager($settings);
$shipstationManager->setSupplierCreds(\Sophio\Common\Models\FBS\Supplier::where('PK',753)->first());
$shipstationManager->createClient();
print_r($shipstationManager->listUsers());
foreach($shipstationManager->listWarehouses() as $warehouse ) {
    echo " ".$warehouse->warehouseName." ";
}
$warehouseService = new Warehouses($shipstationManager);
print_r($warehouseService->getWarehouse('B2B', '226160'));
exit();
*/
config(['tenant_db'=>'sophio_fbs']);

config(['logging.default'=>'shipstation']);
$settings = new \Sophio\Common\Repository\Settings();
$shipstationManager = new ShipStationManager($settings);
$rocketShipItManager = new RocketShipItManager($settings);
$invoice = \Sophio\Common\Models\FBS\Invoice::find(21571127);
 $supplier = \Sophio\Common\Models\FBS\Supplier::find(753);
$order = (new CreateOrder($shipstationManager, $rocketShipItManager))($invoice, $supplier,$invoice->lineitem, null, 'PRIMARY', false, $settings);
dd($order);
exit();
(new NewShippingOrdersFromInvoice())($invoice,$settings);
print_r(\Sophio\Common\Services\LogTrack::getAll());
exit();
$invoices = \Sophio\Common\Models\FBS\Invoice::where('invdate','>',\Illuminate\Support\Carbon::yesterday())->where('invstatus','F')->orderBy('invdate','desc')->get();
foreach($invoices as $invoice) {
    try {
    Config::set('tenant_db' , 'sophio_fbs');
    $old_log = \Sophio\Common\Models\FBS\SupplierLog::where('invpk',(string)$invoice->pk)->where('timein','>',\Illuminate\Support\Carbon::yesterday())->where('action','orders/createorder')->first();
    Config::set('tenant_db' , 'sophio_fbs');
    if(!isset($old_log->request)) {
        continue;
    }
    $old_req = json_decode(substr($old_log->request,strpos($old_log->request,'{')));

    $settings = new \Sophio\Common\Repository\Settings([]);

    $settings->setSettingsByStore(\Sophio\Common\Models\FBS\Store::where('STOREPK',$invoice->storepk)->first());
    $c  =new \Sophio\Common\ShipStation\src\Library\Actions\ManualToShipstation();
    $responses = $c($invoice,$settings,true);
    $new_request = json_decode(json_encode(reset( $responses)));
    if(!isset($new_request->orderNumber)) {
        continue;
    }
    if($new_request->orderNumber!=$old_req->orderNumber) {
        echo "orderNumber not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->orderKey!=$old_req->orderKey) {
        echo "orderKey not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->shipTo->residential!=$old_req->shipTo->residential) {
        echo "residential not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->internalNotes!=$old_req->internalNotes) {
        echo "internalNotes not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->carrierCode!=$old_req->carrierCode) {
        echo "carrierCode not the same for invoice ".$invoice->pk. " / ". $invoice->custtype. " ".$new_request->carrierCode.'<>'.$old_req->carrierCode. " old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->serviceCode!=$old_req->serviceCode) {
        echo "serviceCode not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }

    if($new_request->advancedOptions->warehouseId!=$old_req->AdvancedOptions->warehouseId) {
        echo "advancedOptions->warehouseId not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->advancedOptions->storeId!=$old_req->AdvancedOptions->storeId) {
        echo "advancedOptions->storeId not the same for invoice ".$invoice->pk. " / ". $invoice->custtype." old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->advancedOptions->billToParty!=$old_req->AdvancedOptions->billToParty) {
        echo "advancedOptions->billToParty not the same for invoice ".$invoice->pk." / ". $invoice->custtype. " old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->advancedOptions->billToAccount!=$old_req->AdvancedOptions->billToAccount) {
        echo "advancedOptions->billToAccount not the same for invoice ".$invoice->pk." / ". $invoice->custtype. " old log id ".$old_log->id."\n";
        continue;
    }
    if($new_request->advancedOptions->billToPostalCode!=$old_req->AdvancedOptions->billToPostalCode) {
        echo "advancedOptions->billToPostalCode not the same for invoice ".$invoice->pk." / ". $invoice->custtype. " old log id ".$old_log->id."\n";
        continue;
    }
    echo "invoice ok ". $invoice->pk."\n";
    }catch (Exception $e) {
        print_r($e->getMessage());
    }
}

exit();

$invoice = \Sophio\Common\Models\FBS\Invoice::where('pk',9469942)->first();
$settings = new \Sophio\Common\Repository\Settings([]);

$settings->setSettingsByStore(\Sophio\Common\Models\FBS\Store::where('STOREPK',$invoice->storepk)->first());

$shipstationManager = new ShipStationManager($settings);
$rocketshipit = new \Sophio\Common\RocketShipIt\src\Library\RocketShipItManager($settings);

$rocketshipit->verifyAddressForInvoice($invoice);
exit();

$c  =new \Sophio\Common\ShipStation\src\Library\Actions\NewShippingOrdersFromInvoice();

$suppliers = [];
foreach ($invoice->lineitem as $lineitem) {
    $suppliers[] =$lineitem->supplier;
}
foreach($suppliers as $supplier) {

    $shipstationManager = new ShipStationManager($settings);
    $shipstationManager->setSupplierCreds($supplier);
    $shipstationManager->createClient();
  //  $shipstationManager->listWarehouses();
    print_r($shipstationManager->listPackages('fedex'));
    print_r($shipstationManager->listPackages('ups'));
    print_r($shipstationManager->listPackages('usps'));
}
exit();

$responses = $c($invoice,$settings,true);
foreach($responses as $r) {
    echo json_encode($r);echo "\n";exit();
}