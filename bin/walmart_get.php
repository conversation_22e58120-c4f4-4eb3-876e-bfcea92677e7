<?php


use Sophio\Walmart\Actions\CheckItemWithFix;
use Sophio\Walmart\Library\Items;
use Sophio\Walmart\Library\Orders;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$wo = new Orders();
$invoice = \Sophio\Common\Models\FBS\Invoice::find(10000022);
dd($wo->sendAcknowledge(108831068435290, 8245718, $invoice));

$item = new Items();
dd($item->getItem('430907584'));

$client = new \Sophio\Walmart\Library\WalmartClient();
$client->setMethod('GET');
$client->setEndpoint('/v4/items/'."594776212?productIdType=ItemId");
$response = $client->execute();
dd($response);
exit();
dd(\Sophio\Common\Services\IsoCodesService::extractUpcGtin(['00087295149836']));

session(['database', 'sophio_fbs']);
\Config::set('tenant_db', 'sophio_fbs');
//$marketitems = \Sophio\Walmart\Models\MarketplaceItem::where('status','UNPUBLISHED')->whereNull('marketSku');
$marketitems = \Sophio\Walmart\Models\MarketplaceItem::where('pk',92776);
$settings = ['tenant_db'=>'sophio_fbs','status'=>'PUBLISHED'];
foreach($marketitems->cursor() as $mi)
{
    $checkitemwithfix = new CheckItemWithFix($settings);
  print_r(  $checkitemwithfix($mi));
}
exit();
$client = new \Sophio\Walmart\Library\WalmartClient();
$client->setMethod('GET');
$client->setEndpoint('/v3/items/'."BCGL-PLP-2357RLED");
$response = $client->execute();
dd($response);
$items = new Items();
$response = $items->getItem('BCZC-MOE-172599R');
dd($response);
$generalfeeds = new \Sophio\Walmart\Library\GeneralFeeds();
$generalfeeds->setSettings(['tenant_db'=>'sophio_fbs']);
$generalfeeds->checkFixItemByCase('FBHK-STA-as321');
exit();
$client = new \Sophio\Walmart\Library\WalmartClient();
$client->setMethod('GET');
$client->setEndpoint('/v3/items/'."FLHQ-MHL-F20252");
$response = $client->execute();
print_r($response);
echo $client->getCorrelationid();
