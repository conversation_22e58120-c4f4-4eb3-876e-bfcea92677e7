<?php

use Illuminate\Support\Facades\DB;
use Sophio\Common\Models\FBS\BuyerProfile;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSFeedExporter\Library\DataFeedExportManager;
use Sophio\FBSFeedExporter\Library\DataFeedManager;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$settings = ['tenant_db'=>'sophio_fbs','emails'=>['<EMAIL>']];
$bp = BuyerProfile::find( 13);
$de_manager = new DataFeedExportManager('sophio_fbs',$settings);
$de_manager->generateForBuyerProfile($bp);
exit();
\Config::set('tenant_db', 'sophio_fbs');
//$dataexport = new \Sophio\FBSFeedExporter\Library\DataExporter(2);
//echo $dataexport->generateDataFile();
/*
$bp = BuyerProfile::where('id',1)->first();
$de_manager = new DataFeedExportManager();
$export = $de_manager->generateForBuyerProfile($bp);
$files = $export->getFiles();
*/
$activedatafeeds = DB::table('sophio_fbs.dailyfeeds')->where('active','=',1)->get();
foreach($activedatafeeds as $df)
{
    $d = new DataFeedManager($df->accountnum,'sophio_fbs');
    $d->updateFeedTable();
  //  LogTrack::add('updated daily feed for account '.$df->accountnum);
}