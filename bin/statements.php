<?php

use App\Events\AdminQueueMailEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Sophio\Common\Models\FBS\StatementRaw;
use Sophio\Common\Services\DimensionsService;
use Sophio\FBSInventoryImporter\Library\Jobs\PushInventoriesToWarehouse;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);

foreach(\Sophio\Common\Models\FBS\StatementHeader::where('stmntdate','=','2023-08-31')->get() as $statement) {
    $statementService = new \Sophio\FBSStatements\Library\StatementService();

    $statementService->penalties($statement);
    $statement->save();
}
exit();
/*
$importer = new \Sophio\FBSStatements\Library\Importer('sophio_fbs', new \Sophio\Common\Repository\Settings([]));
$importer->setSupplier(\Sophio\Common\Models\FBS\Supplier::find(831));
$importer->setDate(\Illuminate\Support\Carbon::parse('2023-05-30'));
$importer->newStatement();
$importer->runSupplier();

 */

$stmtraw = new StatementRaw;

dd( $stmtraw->getTableColumns());
$statementService = New \Sophio\FBSStatements\Library\StatementService();
$statementHeader =\Sophio\Common\Models\FBS\StatementHeader::find(200032);
$statementService->paymentRemittanceAdvice($statementHeader);
