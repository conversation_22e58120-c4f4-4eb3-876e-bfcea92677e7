<?php

namespace So<PERSON>o\FBSOrder\Library\Actions;

use App\Exceptions\ExceptionMailAction;
use App\Library\Sophio\Jobs\FBS\UpdateQtyInWarehouse;
use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSOrder\Library\Repository\SupplierForOrder;
use Sophio\OrderLink\HttpOrderLinkManager;
use Sophio\OrderLink\Library\Utils;
use Sophio\OrderLink\OrderLinkManager;
use Sophio\PartsAuthority\src\Library\PAStockCheckProcessor;

/**
 * Handles stock checking via Orderlink
 * It can perform checks to multiple suppliers (launches parallel WSDL requests)
 * The processor launches an orderlink request for each supplier
 * and then updates the warehouseItem of the supplier attached to the Item
 * It will accept a response if return code is success and depending on settings:
 * qty can be changed to zero if
 * - only view branches have qty but supplier don't allow them,
 * - there is mismatch between mfgcode/linecode and part number
 * - alternateflag is not allowed (please note some suppliers ignore this)
 * - malformed response
 * - supplier doesn't provide orderlink credentials
 * - cost is zero or negative leads to zero qty
 *  If comment includes supersed info we mark the Item (please note PIES info provide better supersed info)
 */
class OrderLinkStockCheckProcessor
{
    protected $wsdl;
    protected Settings $settings;
    protected Customer|null $customer;
    protected Invoice|null $invoice;
    protected $updateList = [];

    public function __construct($settings, $customer = null, $invoice = null)
    {
        $this->settings = $settings;
        $this->customer = $customer;
        $this->invoice = $invoice;


    }

    /**
     * It can modify the item and supplier lists
     * There is no return on invoker as the purporse of this class is to perform only the orderlink requests
     * @param $suppliers
     * @param $itemList
     * @return void
     */
    public function __invoke(&$suppliers, &$itemList)
    {

        $has_pa = false;
        $custom_wsdl_responses = [];
        $wsdl_responses = [];
        foreach ($suppliers as $supplier) {
            if ($supplier->profile->SUP == "WHD") {
                if (!$this->wsdl) {
                    $this->wsdl = new OrderLinkManager($this->settings);
                }
                $supplier->resetLineKeys();

                if ($supplier->lines->count() > 0) {

                    if (!isset($supplier->data['account'])) {

                        $this->handleBadSupplier($suppliers, $supplier->contactpk, $itemList);
                    } else {


                        $this->wsdl->prepairAddPriceNAvaliability($supplier, $itemList);
                    }
                }
            } elseif ($supplier->profile->SUP == "PAR") {
                if (config('sophio.admin.paapi_enable') === 1) {
                    $has_pa = true;
                } else {
                    if ($supplier->lines->count() > 0) {
                        if (!isset($supplier->data['account'])) {
                            $this->handleBadSupplier($suppliers, $supplier->contactpk, $itemList);
                        } else {
                            $this->wsdl->prepairAddPriceNAvaliability($supplier, $itemList);
                        }
                    }
                }

            } elseif ($supplier->profile->SUP === 'UAS') {
                $s = clone $this->settings;
                $s->set('CUSTOMORDERLINK', $supplier->profile->SETTINGS['CUSTOMORDERLINK']);
                $s->set('parallel_soap', 0);

                $mywsdl = new HttpOrderLinkManager($s);

                $mywsdl->prepairAddPriceNAvaliability($supplier, $itemList);
                $custom_wsdl_responses = $mywsdl->execute();
                if($mywsdl->getRequest($supplier->profile->contactpk)!=="") {
                    try{
                        $custom_wsdl_response_xml = $custom_wsdl_responses[$supplier->profile->contactpk]->asXML();
                    }catch(\Throwable $e){
                        (new ExceptionMailAction())($e,['contactpk'=>$supplier->profile->contactpk,'invoice'=>isset($this->invoice) ? $this->invoice->pk:'','wsdl_request'=>$mywsdl->getRequest($supplier->profile->contactpk)]);
                        $custom_wsdl_response_xml ="";
                    }
                    (new SupplierLogCreate($this->settings->get('fbs_database')))([
                        'storepk' => isset($this->invoice) ? $this->invoice->storepk : (isset($this->customer) ? $this->customer->storepk : config('sophio.admin.default_store')),
                        'timein' => now(),
                        'accountnum' => isset($this->invoice) ? $this->invoice->seller->accountnum : (isset($this->customer) ? $this->customer->accountnum : ''),
                        'apitype' => 'UAS',
                        'action' => 'stockcheck',
                        'request' => $mywsdl->getRequest($supplier->profile->contactpk),
                        'contactpk' => $supplier->profile->contactpk,
                        'profilepk' => $supplier->profile->PK,
                        'response' => $custom_wsdl_response_xml ?? '',
                        'invpk' => isset($this->invoice) ? $this->invoice->pk : '',
                        'clientip' => request()->server('REMOTE_ADDR')
                    ]);
                }

            }

        }
        if ($has_pa == true) {
            $PA = new PAStockCheckProcessor($this->settings, $this->customer, $this->invoice);
            ($PA)($suppliers, $itemList);
        }
        if ($this->wsdl) {

            $wsdl_responses = $this->wsdl->execute();
        }


        if (is_array($wsdl_responses)) {

            if (is_array($custom_wsdl_responses) && count($custom_wsdl_responses) > 0) {
                $wsdl_responses = $wsdl_responses + $custom_wsdl_responses;
            }
        } elseif (is_array($custom_wsdl_responses) && count($custom_wsdl_responses) > 0) {
            $wsdl_responses = $custom_wsdl_responses;
        }

        $this->logToSupplier($suppliers);

        if (is_array($wsdl_responses))
            foreach ($wsdl_responses as $contactpk => $response) {
                $selected_branch = null;
                if (isset($response->stockcheckresp)) {

                    $wparts = [];
                    $i = 0;

                    foreach ($response->stockcheckresp->part as $part) {
                        $pattrs = $part->attributes();
                        $wparts[(int)$pattrs['linenum']] = $part;
                        $i++;
                    }

                    $clines = 0;

                    foreach ($suppliers[$contactpk]->lines as $k => $line) {

                        if (isset($wparts[$line['linenum']])) {
                            $itemList[$line['pk']]->sources[$contactpk]->usable = 1;
                            $itemList[$line['pk']]->sources[$contactpk]->qty_found = 0;

                            $this->processPart($wparts[$line['linenum']], $itemList, $suppliers[$contactpk], $line);

                        } else {

                            LogTrack::add('Warning ' . $contactpk . ' could not match the lineKey in response for ' . $line['mfgsku'], 'suppliers');
                            $itemList[$line['pk']]->sources[$contactpk]->qty_found = 0;
                            $itemList[$line['pk']]->sources[$contactpk]->usable = 0;
                        }
                        $clines++;
                    }

                    if ($this->settings->get('check_branches_sum')) {
                        $selected_branch = null;
                        $selected_branches = [];
                        $main_branch = 0;
                        $main_branch_id = "";
                        foreach ($itemList as $linepk => $lineitem) {
                            if (isset($itemList[$line['pk']]->sources[$contactpk]) && $itemList[$line['pk']]->sources[$contactpk]->usable !== 0) {
                                $item = $itemList[$line['pk']]->sources[$contactpk]->rtsc;
                                if (isset($item['attributes'])) {
                                    if ((int)$item['attributes']['qtyavail'] >= (int)$item['attributes']['qtyreq']) {
                                        $main_branch++;
                                        $main_branch_id = $item['attributes']['branch'];
                                    }
                                    if (isset($item['altbranch'])) {
                                        foreach ($item['altbranch'] as $ab) {
                                            if (isset($item['attributes'])) {
                                                if ((int)$ab['branch_qtyavail'] >= (int)$item['attributes']['qtyreq']) {
                                                    if (!isset($selected_branches[$ab['alt_branch']])) {
                                                        $selected_branches[$ab['alt_branch']] = 0;
                                                    }
                                                    if ($ab['viewonly'] == "yes") {

                                                        if ($this->settings->get('ManagerApproval') !== 2 || (isset($supplier->data) && $supplier->data != false && $supplier->data['reject_view_only'] == true)) {

                                                        } else {
                                                            $selected_branches[$ab['alt_branch']]++;
                                                        }

                                                    } else {
                                                        $selected_branches[$ab['alt_branch']]++;
                                                    }

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if ($main_branch == $clines) {
                            $selected_branch = $main_branch_id;
                            foreach ($itemList as $linepk => $lineitem) {
                                if (isset($itemList[$linepk]->sources[$contactpk])) {
                                    $itemList[$linepk]->sources[$contactpk]->qty_found = $itemList[$linepk]->sources[$contactpk]->rtsc['attributes']['qtyavail'] ?? 0;
                                    $itemList[$linepk]->sources[$contactpk]->selected_branch = $selected_branch;
                                }
                            }
                        } else {
                            foreach ($selected_branches as $alt => $c) {
                                if ($c == $clines) {
                                    $selected_branch = $alt;
                                    break;
                                }
                            }
                            if ($selected_branch !== null) {
                                foreach ($itemList as $linepk => $lineitem) {
                                    if (isset($itemList[$linepk]->sources[$contactpk])) {
                                        if (isset($itemList[$linepk]->sources[$contactpk]->rtsc['altbranch']))
                                            foreach ($itemList[$linepk]->sources[$contactpk]->rtsc['altbranch'] as $branch) {
                                                if ($branch['alt_branch'] == $selected_branch) {

                                                    if (
                                                        $branch['viewonly'] == "yes" &&
                                                        ((!($this->settings->get('ManagerApproval') == 2)) || (isset($supplier->data) && $supplier->data != false && $supplier->data['reject_view_only'] == true))
                                                    ) {

                                                    } else {
                                                        $itemList[$linepk]->sources[$contactpk]->qty_found = $branch['branch_qtyavail'];
                                                        $itemList[$linepk]->sources[$contactpk]->selected_branch = $selected_branch;
                                                    }

                                                }
                                            }

                                    }
                                }
                            }
                        }
                        if ($selected_branch === null) {
                            foreach ($itemList as $linepk => $lineitem) {
                                if (isset($itemList[$linepk]->sources[$contactpk])) {
                                    $itemList[$linepk]->sources[$contactpk]->qty_found = 0;
                                }
                            }
                        }
                    }
                } else {

                    $this->handleBadSupplier($suppliers, $contactpk, $itemList);

                }
                if ($selected_branch !== null) {
                    $unique_parts = [];
                    foreach ($itemList as $linepk => $lineitem) {
                        if (isset($itemList[$linepk]->sources[$contactpk])) {
                            if (!isset($unique_parts[$lineitem->mfgsku])) {
                                $unique_parts[$lineitem->mfgsku] = 0;
                            }
                            $unique_parts[$lineitem->mfgsku]++;
                        }
                    }
                    foreach ($itemList as $linepk => $lineitem) {
                        if (isset($itemList[$linepk]->sources[$contactpk])) {
                            if ($itemList[$linepk]->sources[$contactpk]->qty_found < $unique_parts[$lineitem->mfgsku]) {
                                $itemList[$linepk]->sources[$contactpk]->qty_found = 0;
                                $itemList[$linepk]->sources[$contactpk]->selected_branch = null;
                            }
                        }
                    }
                }

            }

        if (count($this->updateList) > 0) {
            $updateList = $this->updateList;
            UpdateQtyInWarehouse::dispatch(
                $updateList
            );
        }
    }

    public function handleResponse($response, &$itemList, &$supplers)
    {

        $selected_branch = null;
        if (isset($response->stockcheckresp)) {
            $wparts = [];
            $i = 0;

            foreach ($response->stockcheckresp->part as $part) {
                $pattrs = $part->attributes();
                $wparts[(int)$pattrs['linenum']] = $part;
                $i++;
            }

            $clines = 0;
            foreach ($suppliers[$contactpk]->lines as $k => $line) {
                if (isset($wparts[$line['linenum']])) {
                    $itemList[$line['pk']]->sources[$contactpk]->usable = 1;
                    $itemList[$line['pk']]->sources[$contactpk]->qty_found = 0;
                    $this->processPart($wparts[$line['linenum']], $itemList, $suppliers[$contactpk], $line);
                } else {
                    LogTrack::add('Warning ' . $contactpk . ' could not match the lineKey in response for ' . $line['mfgsku'], 'suppliers');
                    $itemList[$line['pk']]->sources[$contactpk]->qty_found = 0;
                    $itemList[$line['pk']]->sources[$contactpk]->usable = 0;
                }
                $clines++;
            }
            if ($this->settings->get('check_branches_sum')) {
                $selected_branch = null;
                $selected_branches = [];
                $main_branch = 0;
                $main_branch_id = "";
                foreach ($itemList as $linepk => $lineitem) {
                    if (isset($itemList[$line['pk']]->sources[$contactpk]) && $itemList[$line['pk']]->sources[$contactpk]->usable !== 0) {
                        $item = $itemList[$line['pk']]->sources[$contactpk]->rtsc;
                        if (isset($item['attributes'])) {
                            if ((int)$item['attributes']['qtyavail'] >= (int)$item['attributes']['qtyreq']) {
                                $main_branch++;
                                $main_branch_id = $item['attributes']['branch'];
                            }
                            if (isset($item['altbranch'])) {
                                foreach ($item['altbranch'] as $ab) {
                                    if (isset($item['attributes'])) {
                                        if ((int)$ab['branch_qtyavail'] >= (int)$item['attributes']['qtyreq']) {
                                            if (!isset($selected_branches[$ab['alt_branch']])) {
                                                $selected_branches[$ab['alt_branch']] = 0;
                                            }
                                            if ($ab['viewonly'] == "yes") {
                                                if ($this->settings->get('ManagerApproval') !== 2 || (isset($supplier->data) && $supplier->data != false && $supplier->data['reject_view_only'] == true)) {

                                                } else {
                                                    $selected_branches[$ab['alt_branch']]++;
                                                }

                                            } else {
                                                $selected_branches[$ab['alt_branch']]++;
                                            }

                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if ($main_branch == $clines) {
                    $selected_branch = $main_branch_id;
                    foreach ($itemList as $linepk => $lineitem) {
                        if (isset($itemList[$linepk]->sources[$contactpk])) {
                            $itemList[$linepk]->sources[$contactpk]->qty_found = $itemList[$linepk]->sources[$contactpk]->rtsc['attributes']['qtyavail'] ?? 0;
                            $itemList[$linepk]->sources[$contactpk]->selected_branch = $selected_branch;
                        }
                    }
                } else {
                    foreach ($selected_branches as $alt => $c) {
                        if ($c == $clines) {
                            $selected_branch = $alt;
                            break;
                        }
                    }
                    if ($selected_branch !== null) {
                        foreach ($itemList as $linepk => $lineitem) {
                            if (isset($itemList[$linepk]->sources[$contactpk])) {
                                if (isset($itemList[$linepk]->sources[$contactpk]->rtsc['altbranch']))
                                    foreach ($itemList[$linepk]->sources[$contactpk]->rtsc['altbranch'] as $branch) {
                                        if ($branch['alt_branch'] == $selected_branch) {
                                            $itemList[$linepk]->sources[$contactpk]->qty_found = $branch['branch_qtyavail'];
                                            $itemList[$linepk]->sources[$contactpk]->selected_branch = $selected_branch;
                                        }
                                    }

                            }
                        }
                    }
                }
                if ($selected_branch === null) {
                    foreach ($itemList as $linepk => $lineitem) {
                        if (isset($itemList[$linepk]->sources[$contactpk])) {
                            $itemList[$linepk]->sources[$contactpk]->qty_found = 0;
                        }
                    }
                }
            }
        } else {

            $this->handleBadSupplier($suppliers, $contactpk, $itemList);

        }
        if ($selected_branch !== null) {
            $unique_parts = [];
            foreach ($itemList as $linepk => $lineitem) {
                if (isset($itemList[$linepk]->sources[$contactpk])) {
                    if (!isset($unique_parts[$lineitem->mfgsku])) {
                        $unique_parts[$lineitem->mfgsku] = 0;
                    }
                    $unique_parts[$lineitem->mfgsku]++;
                }
            }
            foreach ($itemList as $linepk => $lineitem) {
                if (isset($itemList[$linepk]->sources[$contactpk])) {
                    if ($itemList[$linepk]->sources[$contactpk]->qty_found < $unique_parts[$lineitem->mfgsku]) {
                        $itemList[$linepk]->sources[$contactpk]->qty_found = 0;
                        $itemList[$linepk]->sources[$contactpk]->selected_branch = null;
                    }
                }
            }
        }


    }

    public function logToSupplier($suppliers)
    {
        if (!$this->wsdl) {
            return true;
        }
        $old_tenant = config('tenant_db');
        config(['tenant_db' => $this->settings->get('fbs_database')]);
        $requests = $this->wsdl->getRequests();
        $responses = $this->wsdl->getXMLResponses();

        if (is_array($requests))
            foreach ($requests as $k => $request) {
                if (isset($suppliers[$k])) {
                    if ($this->settings->get('supplierlog_database')) {
                        $db = $this->settings->get('supplierlog_database');
                    } else {
                        $db = $this->settings->get('fbs_database');
                    }
                    (new SupplierLogCreate($db))([
                        'storepk' => isset($this->invoice) ? $this->invoice->storepk : (isset($this->customer) ? $this->customer->storepk : config('sophio.admin.default_store')),
                        'timein' => now(),
                        'accountnum' => isset($this->invoice) ? $this->invoice->seller->accountnum : (isset($this->customer) ? $this->customer->accountnum : ''),
                        'apitype' => 'WHD',
                        'action' => 'stockcheck',
                        'request' => $request,
                        'profilepk' => $suppliers[$k]->profile->PK,
                        'contactpk' => $k,
                        'response' => $responses[$k] ?? '',
                        'invpk' => isset($this->invoice) ? $this->invoice->pk : '',
                        'clientip' => request()->server('REMOTE_ADDR')
                    ]);
                }
            }

        config(['tenant_db' => $old_tenant]);
    }

    public function prepairSupplierWSDL($supplier, $itemList)
    {
        if ($supplier->profile->SUP !== "WHD") {
            //   return;
        }
        $stockcheck_header = [
            'username' => $supplier->data['account'],
            'password' => $supplier->data['password'],
            'provider' => $supplier->data['provider'],
            'ordertype' => '',
            'branch' => '',
            'alternateflag' => $this->settings->get('alternateflag'),
            'chooseflag' => "summary",
            'dcftype' => '',
            'contactpk' => $supplier->contactpk
        ];
        $stockcheck_parts = [];

        foreach ($supplier->lines->all() as $k => $line) {
            if ($itemList[$line['pk']]->sources[$supplier->contactpk]->usable !== 0 && $itemList[$line['pk']]->sources[$supplier->contactpk]->require_rtsc == 1) {
                $lineitem = $itemList[$line['pk']]->sources[$supplier->contactpk];
                $stockcheck_parts[$k] = [
                    'linenum' => $line['linenum'],
                    'mfgcode' => $lineitem->mfgcode,
                    'linecode' => $lineitem->linecode,
                    'partno' => $lineitem->part_number_unformatted,
                    'qtyreq' => $itemList[$line['pk']]->qty_req,
                    'parttype' => "",
                    'dcftype' => ""
                ];
            }

        }

        if (count($stockcheck_parts) > 0) {

            $this->wsdl->AddPriceNAvaliability($stockcheck_parts, $stockcheck_header, $supplier->contactpk);
        }
    }

    public function handleBadSupplier(&$suppliers, $contactpk, &$itemList)
    {
        LogTrack::add('Warning: contactpk ' . $contactpk . ' returned a malformed response', 'suppliers', 'suppliers');
        $suppliers[$contactpk]->failed = true;
        foreach ($itemList as $ms => $item) {
            if (isset($item->sources[$contactpk])) {
                $itemList[$ms]->sources[$contactpk]->qty_found = 0;
                $itemList[$ms]->sources[$contactpk]->require_rtsc = 1;
            }
        }
    }


    public function processPart($part, &$itemList, SupplierForOrder $supplier, $actual_part)
    {


        $contactpk = $supplier->contactpk;
        if (strtolower($part['alternateflag']) === "yes" && $this->settings->get('ManagerApproval') != 2) {
            if (mkMgfSku($itemList[$actual_part['pk']]->sources[$contactpk]->linecode, $itemList[$actual_part['pk']]->sku) != mkMgfSku($part['linecode'], trim($part['partno']))) {
                LogTrack::add('Warning ' . $contactpk . ' provides ' . mkMgfSku($part['linecode'], trim($part['partno'])) . '  as alternate for ' . mkMgfSku($itemList[$actual_part['pk']]->sources[$contactpk]->linecode, $itemList[$actual_part['pk']]->sku), 'lineitem.' . $actual_part['pk']);
                if (!$this->settings->get('allow_not_matched')) {
                    return false;
                }
            } else {
                LogTrack::add('Warning ' . $contactpk . ' provides ' . $actual_part['mfgsku'] . '  has alternateflag=yes', 'lineitem.' . $actual_part['pk']);
                // if the linecode and partno are the same then the line is ok - alternateflag=yes means they return alternatives, but also have the part in stock
                //   $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
            }

        } else {
            // record superseed
            if (stripos($part->comment, 'super') !== false) {
                $itemList[$actual_part['pk']]->sources[$contactpk]->super = 1;
                $itemList[$actual_part['pk']]->super = 1;
            }

        }
        //We have a problem when: SKU is not the same and none of linecode or mfgcode are not the same.
        //Note for mfgcode we allow picking from brand mfgcodes and not the requrested mfgcodes
        //dd([$itemList[$actual_part['pk']]]);

        if (unformatString(strtoupper($itemList[$actual_part['pk']]->sku)) == unformatString(strtoupper(trim($part['partno']))) &&
            ((is_array($itemList[$actual_part['pk']]->mfgcodes) && in_array($part['mfgcode'], $itemList[$actual_part['pk']]->mfgcodes)) || (strtoupper($part['linecode']) == strtoupper($itemList[$actual_part['pk']]->sources[$contactpk]->linecode)))
        ) {
        } else {

            LogTrack::add('Warning: For ' . $itemList[$actual_part['pk']]->mfgsku . ' the supplier ' . $supplier->profile->NAME . ' returned a different linecode/mfgcode/sku: ' . $part['linecode'] .
                '(' . $itemList[$actual_part['pk']]->sources[$contactpk]->linecode . ')' .
                '/' . $part['mfgcode'] . '(' . implode(',', $itemList[$actual_part['pk']]->mfgcodes) . ')' . '/' . $part['partno'] . '(' . $itemList[$actual_part['pk']]->sku . ')', 'lineitem.' . $actual_part['pk']);
            $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
            if ($this->settings->get('check_branches_sum')) $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
            if (!$this->settings->get('allow_not_matched')) {
                return false;
            }
        }

        ['qtyrtsc' => $qtyrtsc, 'costrtsc' => $costrtsc, 'corertsc' => $corertsc, 'rtsc' => $rtsc, 'rtsc_branches' => $rtsc_branches] = $this->parseRTSCPart($part, $actual_part, $supplier);

        $itemList[$actual_part['pk']]->sources[$contactpk]->rtsc_raw = $part->asXML();
        $itemList[$actual_part['pk']]->sources[$contactpk]->rtsc = $rtsc;
        $itemList[$actual_part['pk']]->sources[$contactpk]->rtsc_branches = $rtsc_branches;

        if (strcmp($part['errcode'], "success") == 0) {


            $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = $qtyrtsc;
            $itemList[$actual_part['pk']]->sources[$contactpk]->qty_rtsc = $qtyrtsc;
            $itemList[$actual_part['pk']]->sources[$contactpk]->cost_rtsc = $costrtsc;
            $itemList[$actual_part['pk']]->sources[$contactpk]->cost_found = $costrtsc;
            $itemList[$actual_part['pk']]->sources[$contactpk]->core_found = $corertsc;
            $now = \Illuminate\Support\Carbon::now();
            if ($now->hour >= 20 || $now->hour <= 6) {
            } else {

                $this->updateList[] = ['database' => config('tenant_db'),
                    'pk' => $itemList[$actual_part['pk']]->sources[$contactpk]->pk,
                    'mfgcode' => $itemList[$actual_part['pk']]->sources[$contactpk]->mfgcode,
                    'part_number_unformatted' => $itemList[$actual_part['pk']]->sources[$contactpk]->part_number_unformatted,
                    'contactpk' => $contactpk,
                    'qtyrtsc' => $qtyrtsc];
            }
            /*
                        if ($itemList[$mfgsku]->sources[$contactpk]->cost < $costrtsc * (float)$this->settings->get('rtsccost_mark_to_reject')) {
                            LogTrack::add('Rejecting '.$contactpk. ' for '.$mfgsku.' because '.$costrtsc. ' is bigger than  warehouse cost '.$itemList[$mfgsku]->sources[$contactpk]->cost.' using '.$this->settings->get('rtsccost_mark_to_reject'). ' factor');
                            $itemList[$mfgsku]->sources[$contactpk]->qty_found = 0;
                        }
            */
            if ($costrtsc <= 0) {
                LogTrack::add('Notice for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' returned cost=0 ', 'lineitem.' . $actual_part['pk']);
                $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
                if ($this->settings->get('check_branches_sum')) $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
            }


            if (isset($rtsc['attributes']) && isset($rtsc['attributes']['minqty'])) {
                if ((int)$rtsc['attributes']['minqty'] > $part['qtyreq']) {
                    $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
                    LogTrack::add('Warning for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' the min qty  ' . $rtsc['attributes']['minqty'] . ' is greater than qty req ' . $part['qtyreq'], 'lineitem.' . $actual_part['pk']);
                }
            }

            if ($this->settings->get('rtsccost_mark_to_reject') && $this->settings->get('rtsccost_mark_to_reject') > 0.0 && $this->settings->get('ManagerApproval') !== 2) {

                if ((float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost > 0) {
                    $difference = ($costrtsc - (float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost) / (float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost;
                    if ($difference > $this->settings->get('rtsccost_mark_to_reject')) {
                        $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
                        if ($this->settings->get('check_branches_sum')) $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
                        LogTrack::add('Warning: For ' . $itemList[$actual_part['pk']]->mfgsku . '(' . $actual_part['pk'] . ') the supplier ' . $supplier->profile->NAME . '(' . $contactpk . ') returned a cost higher with ' . our_format($difference * 100) . '% (accepted:' .
                            (100 * $this->settings->get('rtsccost_mark_to_reject')) . '%) than the cost in the data feed.' . $costrtsc . ' vs ' . our_format((float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost) . ' Skipping!', 'lineitem.' . $actual_part['pk']);
                    } elseif ($costrtsc - (float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost > 0) {
                        $difference = ($costrtsc - (float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost) / (float)$itemList[$actual_part['pk']]->sources[$contactpk]->cost;
                        LogTrack::add('Warning: For ' . $itemList[$actual_part['pk']]->mfgsku . ' the supplier ' . $supplier->profile->NAME . ' returned a higher cost that in data feed, but in accepted range of' . our_format($difference * 100) . '%' .
                            (100 * $this->settings->get('rtsccost_mark_to_reject')) . '%).', 'lineitem.' . $actual_part['pk']);
                    }
                }
                if ((float)$itemList[$actual_part['pk']]->price > 0) {
                    $difference = ($costrtsc - $itemList[$actual_part['pk']]->price) / $itemList[$actual_part['pk']]->price;
                    if ($difference > $this->settings->get('rtsccost_mark_to_reject')) {
                        $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
                        if ($this->settings->get('check_branches_sum')) $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
                        LogTrack::add('Warning: For ' . $itemList[$actual_part['pk']]->mfgsku . ' the supplier ' . $supplier->profile->NAME . ' returned a cost higher with ' . ($difference * 100) . '% (accepted:' .
                            (100 * $this->settings->get('rtsccost_mark_to_reject')) . '%) than the selling price. Skipping!', 'lineitem.' . $actual_part['pk']);
                    }
                    if ($costrtsc - $itemList[$actual_part['pk']]->price > 0) {
                        $difference = ($costrtsc - $itemList[$actual_part['pk']]->price) / $itemList[$actual_part['pk']]->price;
                        LogTrack::add('Warning: For ' . $itemList[$actual_part['pk']]->mfgsku . ' the supplier ' . $supplier->profile->NAME . ' returned a higher cost that the selling price, but in accepted range of' . ($difference * 100) . '%' .
                            (100 * $this->settings->get('rtsccost_mark_to_reject')) . '%).', 'lineitem.' . $actual_part['pk']);
                    }
                }

            }
            $itemList[$actual_part['pk']]->sources[$contactpk]->require_rtsc = 2;

        } else {
            LogTrack::add('Notice for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' returned error code ' . $part['errcode'], 'lineitem.' . $actual_part['pk']);
            $itemList[$actual_part['pk']]->sources[$contactpk]->qty_found = 0;
            if ($this->settings->get('check_branches_sum')) $itemList[$actual_part['pk']]->sources[$contactpk]->usable = 0;
            $itemList[$actual_part['pk']]->sources[$contactpk]->require_rtsc = 1;
        }

    }

    public function parseRTSCPart($part, $actual_part, $supplier)
    {
        $contactpk = $supplier->contactpk;

        $rtsc = Utils::parsePart($part);
        $qtyrtsc = (int)(string)$part['qtyavail'];
        $costrtsc = (float)$part['cost'];
        $corertsc = (float)$part['core'];
        $branchqty = 0;
        $branches_qty = [];
        if ((int)$part['qtyavail'] >= (int)$part['qtyreq']) {
            $branches_qty[] = $part['branch'];
        }
        $reject_view_only = false;
        if (isset($part->altbranch)) {
            foreach ($part->altbranch as $branch) {

                if ((int)(string)$branch['branch_qtyavail'] !== 0) {
                    // View only branches are only accepted if the supplier allows us to order from or if a manager overrides

                    if ((string)$branch['viewonly'] === "yes") {
                        LogTrack::add('Notice for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' branch ' . $branch['branch_name'] . ' has qty but is marked with viewonly=yes', 'lineitem.' . $actual_part['pk']);

                        if ($this->settings->get('ManagerApproval') == 2 || $supplier->data['reject_view_only'] == false) {
                            $branchqty = max((int)(string)$branch['branch_qtyavail'], $branchqty);
                            if ((int)$branch['branch_qtyavail'] >= (int)$part['qtyreq']) {
                                $branches_qty[] = (string)$branch['alt_branch'];
                            } else {
                                $reject_view_only = true;
                            }
                        }

                    } else {
                        $branchqty = max((int)(string)$branch['branch_qtyavail'], $branchqty);
                        if ((int)$branch['branch_qtyavail'] >= (int)$part['qtyreq']) {
                            $branches_qty[] = (string)$branch['alt_branch'];
                        }
                    }

                }

            }
        }

        if ($reject_view_only) {
            LogTrack::add('Notice for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' has qty but is marked with viewonly=yes and not allowed to buy from viewonly', 'lineitem.' . $actual_part['pk']);
        }
        if ($qtyrtsc < $branchqty && isset($part->altbranch)) {
            $qtyrtsc = $branchqty;
        }
        if ($qtyrtsc == 0) {
            LogTrack::add('Notice for ' . $actual_part['mfgsku'] . ' on contactpk ' . $supplier->profile->NAME . ' no qty was found', 'lineitem.' . $actual_part['pk']);
        }
        $rtsc['branchqty'] = $branchqty;
        return [
            'qtyrtsc' => $qtyrtsc,
            'costrtsc' => $costrtsc,
            'corertsc' => $corertsc,
            'rtsc' => $rtsc,
            'rtsc_branches' => $branches_qty
        ];
    }

    public function getWSDL()
    {
        return $this->wsdl;
    }
}