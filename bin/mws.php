<?php
use <PERSON><PERSON>rink\AmazonMws\AmazonProductInfo;
use Illuminate\Support\Facades\Config as Config;
use Sophio\Catlink\CatlinkClient;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);   $app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$profile = \Sophio\Common\Models\FBS\Supplier::where("PK","931")->first();
$config = [
    'user' => $profile->SETTINGS['SUPPLIERB2BSITEUSERNAME'],
    'password' => $profile->SETTINGS['SUPPLIERB2BSITEPASSWORD'],
    'provider' => 'SOPHOEUNI'
];
print_r($config);
$catlink = new CatlinkClient(array_merge(config('sophio.catlink'),$config));
echo $catlink->getToken(false); //no caching

/*
$amz = new AmazonProductInfo("store1");
$amz->setConfig([

            'merchantId'       => 'A3M6ZQ2QKELUL5',
            'marketplaceId'    => 'ATVPDKIKX0DER',
            'keyId'            => '********************',
            'secretKey'        => '+DmOCMSne26BqPNGF22Rm9T1H+RWf3fU1MMjmCmB',
            'mwsAuthToken'     => 'amzn.mws.9cb14689-9414-608a-e4be-8b7c811dfa86',
            'amazonServiceUrl' => 'https://mws.amazonservices.com/',
            'muteLog'          => false,

]);
$amz->setASINs(['B002KT3XQM','B00MBI2PK2']);
 $amz->fetchCompetitivePricing();
 $prices = $amz->getProduct();
foreach($prices as $price)
{
    $product =  $price->getData();
    print_r($product);
}
dd($amz->getLastError());
*/