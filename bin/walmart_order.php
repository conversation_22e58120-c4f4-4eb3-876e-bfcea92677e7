<?php

use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\BaseTypesHandler;
use GoetasWebservices\Xsd\XsdToPhpRuntime\Jms\Handler\XmlSchemaDateHandler;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use JMS\Serializer\Handler\HandlerRegistryInterface;
use JMS\Serializer\SerializerBuilder;
use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Models\PIM\Product;
use Sophio\Walmart\Library\Items;
use Sophio\Walmart\Library\Orders;
use Sophio\Walmart\Library\Serializers\OrderLineTypes;
use Sophio\Walmart\Models\MarketplaceItem;
set_time_limit(0);
require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);
$invoice = \Sophio\Common\Models\FBS\Invoice::find(1787536);
$wal = new Orders();
print_r($invoice->ponumber);

$res  = $wal->getOrder($invoice->ponumber,$invoice->lineitem->first()->supplier->SETTINGS['WALMARTSHIPNODE']);
dd($res);