<?php

use Illuminate\Support\Str;

require __DIR__ . '/../vendor/autoload.php';
$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
dd(Str::contains('Stamsps_com order', 'stamps',true) );
$string='Trans Id: ch_3Nf4kgJQgAIihRqA0676nsel
Stripe Invoice Id: in_1Nf4kfJQgAIihRqAH98drmHM
{
  "id": "in_1Nf4kfJQgAIihRqAH98drmHM",
  "object": "invoice",
  "account_country": "US",
  "account_name": "Sophio LLC",
  "account_tax_ids": null,
  "amount_due": 0,
  "amount_paid": 0,
  "amount_remaining": 0,
  "amount_shipping": 0,
  "application": null,
  "application_fee_amount": null,
  "attempt_count": 0,
  "attempted": false,
  "auto_advance": true,
  "automatic_tax": {
    "enabled": false,
    "status": null
  },
  "billing_reason": "manual",
  "charge": null,
  "collection_method": "charge_automatically",
  "created": **********,
  "currency": "usd",
  "custom_fields": null,
  "customer": "cus_LZj1pJMk0aUx1W",
  "customer_address": {
    "city": "FRANKLIN",
    "country": null,
    "line1": "502 Bancroft Way",
    "line2": null,
    "postal_code": "37064",
    "state": "TN"
  },
  "customer_email": "<EMAIL>",
  "customer_name": "Michael Birnholz",
  "customer_phone": "***********",
  "customer_shipping": null,
  "customer_tax_exempt": "none",
  "customer_tax_ids": [],
  "default_payment_method": null,
  "default_source": null,
  "default_tax_rates": [],
  "description": "Invoice: ******** Fulfillment by Sophio",
  "discount": null,
  "discounts": [],
  "due_date": null,
  "effective_at": null,
  "ending_balance": null,
  "footer": null,
  "from_invoice": null,
  "hosted_invoice_url": null,
  "invoice_pdf": null,
  "last_finalization_error": null,
  "latest_revision": null,
  "lines": {
    "object": "list",
    "data": [],
    "has_more": false,
    "total_count": 0,
    "url": "/v1/invoices/in_1Nf4kfJQgAIihRqAH98drmHM/lines"
  },
  "livemode": false,
  "metadata": {
    "order_id": "********"
  },
  "next_payment_attempt": 1692038425,
  "number": null,
  "on_behalf_of": null,
  "paid": false,
  "paid_out_of_band": false,
  "payment_intent": null,
  "payment_settings": {
    "default_mandate": null,
    "payment_method_options": null,
    "payment_method_types": null
  },
  "period_end": **********,
  "period_start": **********,
  "post_payment_credit_notes_amount": 0,
  "pre_payment_credit_notes_amount": 0,
  "quote": null,
  "receipt_number": null,
  "rendering_options": null,
  "shipping_cost": null,
  "shipping_details": null,
  "starting_balance": 0,
  "statement_descriptor": null,
  "status": "draft",
  "status_transitions": {
    "finalized_at": null,
    "marked_uncollectible_at": null,
    "paid_at": null,
    "voided_at": null
  },
  "subscription": null,
  "subscription_details": {
    "metadata": null
  },
  "subtotal": 0,
  "subtotal_excluding_tax": 0,
  "tax": null,
  "test_clock": null,
  "total": 0,
  "total_discount_amounts": [],
  "total_excluding_tax": 0,
  "total_tax_amounts": [],
  "transfer_data": null,
  "webhooks_delivered_at": **********
}

<STRIPEPAYRESPONSE>{
  "id": "in_1Nf4kfJQgAIihRqAH98drmHM",
  "object": "invoice",
  "account_country": "US",
  "account_name": "Sophio LLC",
  "account_tax_ids": null,
  "amount_due": 6082,
  "amount_paid": 6082,
  "amount_remaining": 0,
  "amount_shipping": 0,
  "application": null,
  "application_fee_amount": null,
  "attempt_count": 1,
  "attempted": true,
  "auto_advance": false,
  "automatic_tax": {
    "enabled": false,
    "status": null
  },
  "billing_reason": "manual",
  "charge": "ch_3Nf4kgJQgAIihRqA0676nsel",
  "collection_method": "charge_automatically",
  "created": **********,
  "currency": "usd",
  "custom_fields": null,
  "customer": "cus_LZj1pJMk0aUx1W",
  "customer_address": {
    "city": "FRANKLIN",
    "country": null,
    "line1": "502 Bancroft Way",
    "line2": null,
    "postal_code": "37064",
    "state": "TN"
  },
  "customer_email": "<EMAIL>",
  "customer_name": "Michael Birnholz",
  "customer_phone": "***********",
  "customer_shipping": null,
  "customer_tax_exempt": "none",
  "customer_tax_ids": [],
  "default_payment_method": null,
  "default_source": null,
  "default_tax_rates": [],
  "description": "Invoice: ******** Fulfillment by Sophio",
  "discount": null,
  "discounts": [],
  "due_date": null,
  "effective_at": 1692034826,
  "ending_balance": 0,
  "footer": null,
  "from_invoice": null,
  "hosted_invoice_url": "https://invoice.stripe.com/i/acct_1KqK0GJQgAIihRqA/test_YWNjdF8xS3FLMEdKUWdBSWloUnFBLF9PUnlpNHdFOTNYdXhvNElMYjBLazczbGdEclFxa1JPLDgyNTc1NjI40200l5hJsnj3?s=ap",
  "invoice_pdf": "https://pay.stripe.com/invoice/acct_1KqK0GJQgAIihRqA/test_YWNjdF8xS3FLMEdKUWdBSWloUnFBLF9PUnlpNHdFOTNYdXhvNElMYjBLazczbGdEclFxa1JPLDgyNTc1NjI40200l5hJsnj3/pdf?s=ap",
  "last_finalization_error": null,
  "latest_revision": null,
  "lines": {
    "object": "list",
    "data": [
      {
        "id": "il_1Nf4kgJQgAIihRqAYkovjx6t",
        "object": "line_item",
        "amount": 6082,
        "amount_excluding_tax": 6082,
        "currency": "usd",
        "description": "URO 13717627501 Engine Air Intake Hose",
        "discount_amounts": [],
        "discountable": true,
        "discounts": [],
        "invoice_item": "ii_1Nf4kgJQgAIihRqAhZ1a7sdy",
        "livemode": false,
        "metadata": {
          "lineitempk": "*********"
        },
        "period": {
          "end": 1692034826,
          "start": 1692034826
        },
        "plan": null,
        "price": {
          "id": "price_1Nf4kgJQgAIihRqAYbQU2Gye",
          "object": "price",
          "active": false,
          "billing_scheme": "per_unit",
          "created": 1692034826,
          "currency": "usd",
          "custom_unit_amount": null,
          "livemode": false,
          "lookup_key": null,
          "metadata": {},
          "nickname": null,
          "product": "uro-13717627501",
          "recurring": null,
          "tax_behavior": "unspecified",
          "tiers_mode": null,
          "transform_quantity": null,
          "type": "one_time",
          "unit_amount": 6082,
          "unit_amount_decimal": "6082"
        },
        "proration": false,
        "proration_details": {
          "credited_items": null
        },
        "quantity": 1,
        "subscription": null,
        "tax_amounts": [],
        "tax_rates": [],
        "type": "invoiceitem",
        "unit_amount_excluding_tax": "6082"
      }
    ],
    "has_more": false,
    "total_count": 1,
    "url": "/v1/invoices/in_1Nf4kfJQgAIihRqAH98drmHM/lines"
  },
  "livemode": false,
  "metadata": {
    "order_id": "********"
  },
  "next_payment_attempt": null,
  "number": "3821E42B-0194",
  "on_behalf_of": null,
  "paid": true,
  "paid_out_of_band": false,
  "payment_intent": "pi_3Nf4kgJQgAIihRqA06p0l6qy",
  "payment_settings": {
    "default_mandate": null,
    "payment_method_options": null,
    "payment_method_types": null
  },
  "period_end": **********,
  "period_start": **********,
  "post_payment_credit_notes_amount": 0,
  "pre_payment_credit_notes_amount": 0,
  "quote": null,
  "receipt_number": null,
  "rendering_options": null,
  "shipping_cost": null,
  "shipping_details": null,
  "starting_balance": 0,
  "statement_descriptor": null,
  "status": "paid",
  "status_transitions": {
    "finalized_at": 1692034826,
    "marked_uncollectible_at": null,
    "paid_at": 1692034827,
    "voided_at": null
  },
  "subscription": null,
  "subscription_details": {
    "metadata": null
  },
  "subtotal": 6082,
  "subtotal_excluding_tax": 6082,
  "tax": null,
  "test_clock": null,
  "total": 6082,
  "total_discount_amounts": [],
  "total_excluding_tax": 6082,
  "total_tax_amounts": [],
  "transfer_data": null,
  "webhooks_delivered_at": **********
}</STRIPEPAYRESPONSE>';
preg_match('/Trans Id:\s?(\w+)/', $string, $matches);
dd($matches);
$settings = new \Sophio\Common\Repository\Settings();
$settings->setSettingsByStore(\Sophio\Common\Models\FBS\Store::find(1));
$sid ='**********************************';
$token ='07609dbf4881566636f841d6d43d6490';

$client = new Twilio\Rest\Client($sid, $token);

$from =$settings->get('TWILIOSRC') ??$settings->get('TEXTPHONE');
dd($client->messages->create(
    '+400745670843',
    [
        'from' => '+16152351456',
        'body' => "hello from sophio"
    ]
));