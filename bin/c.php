<?php

use App\Library\Sophio\BarcodeLookup\Manager;
use App\Models\NeedsContent;
use Illuminate\Support\Facades\DB;
use Sophio\Common\Models\Catalog\AdnExtract;
use Sophio\Common\Models\PIM\Product;
use Sophio\Corcentric\Library\CorcentricManager;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$bnds = DB::select("SELECT wi.BrandID , COUNT(*)   FROM wws_items wi  JOIN aces_meta.aces_brands_mix abm ON abm.brand_id = wi.BrandID WHERE abm.path LIKE 'Pronto Network Cooperative/ Network Product%' GROUP BY wi.BrandID");
foreach ($bnds as $bnd) {
    $pp =  Product::with('images')->whereHas('wwsitem')->where('aaiabrandid',$entry->getOriginal('brandid'))->get();
    foreach($pp as $p) {
        $p->images()->update(['aaiabrandid'=>$entry->brandid]);
    }
    Product::whereHas('wwsitem')->where('aaiabrandid',$entry->getOriginal('brandid'))->update(['aaiabrandid'=>$entry->brandid,'mfg_code'=>$entry->brandid]);

    AdnExtract::where('aaiabrandid', $entry->getOriginal('brandid'))->update(['aaiabrandid' => $entry->brandid]);
    NeedsContent::whereHas('wwsitem')->where('aaiabrandid',$entry->getOriginal('brandid'))->update(['aaiabrandid'=>$entry->brandid]);
}