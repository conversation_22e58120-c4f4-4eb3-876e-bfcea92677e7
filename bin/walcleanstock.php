<?php


use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\PIM\Part;
use Sophio\Common\Models\FBS\WhiDcfAfmkt;
use Sophio\FBSFeedExporter\Library\Actions\WalmartInventoryFeedFile;
use Sophio\Walmart\Actions\CreateFeedInventoryHistory;
use Sophio\Walmart\Jobs\BatchFeedsSender;
use Sophio\Walmart\Jobs\CheckGeneralFeed;
use Sophio\Walmart\Jobs\SendFeed;
use Sophio\Walmart\Library\GeneralFeeds;
use Sophio\Walmart\Library\ItemsFeeds;
use Sophio\Walmart\Library\RequestBody\DSVInventoryFeed;
use Sophio\Walmart\Library\SupplierProduct;
use Sophio\Walmart\Library\WalmartBulkFeed;
use Sophio\Walmart\Models\FeedItemInventoryHistory;
use Sophio\Walmart\Models\MarketplaceItem;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

$sups=[];
foreach( \Sophio\Common\Models\FBS\Supplier::all() as $supplier)
{
    if(isset($supplier->SETTINGS['WALMARTSHIPNODE']) && $supplier->SETTINGS['WALMARTSHIPNODE']!=="") {
        $sups[$supplier->SETTINGS['WALMARTSHIPNODE']] = $supplier;
    }
}
$settings=[
    'request_params'=> ['feedType' => 'DSV_INVENTORY'],
    'type'=>'Inventory',
    'content_type'=>'application/json',
    'feed_delay'=>0
];
$ii=0;

\Illuminate\Support\Facades\DB::table('walmart.dsv')
    ->join('sophio_fbs.marketplaceitems',function($q){
        $q->on('marketplaceitems.marketSellerSKu','=','dsv.sku');
    })
    ->where('status','BADGTIN')
    ->selectRaw('dsv.*')
    ->orderBy('id','desc')->chunk(10000, function ($rows)use($sups,$settings,&$ii) {
    $settings['filename']='dataexport/288065/WAL_zeroed_inventory_badgtin_'.  Carbon::today()->toDateString().'_'.$ii.'.json';
    $settings['request_id'] = 'fbs_inventory_zero_' . date("Ymdh");
    $object = new DSVInventoryFeed();
    $inventory=[];
    $mi=[];
    foreach ($rows as $row) {
        $inventory[] = [
            'availabilityCode' => 'AC',
            'quantity' => 0,
            'productId' => (string)$row->gtin,
            'shipNode' => (string)$row->ship_node
        ];
        $mi[$row->gtin] = $row->sku;
    }
    echo count($inventory)." rows to be sent\n";
    $object->setData($inventory);
    (new WalmartInventoryFeedFile()) ($settings['filename'], $object);
    $invfeed = new GeneralFeeds();
    $invfeed->setSettings($settings);
    Log::channel('walmart')->error('startsending general feed');
    $feeddb = $invfeed->sendFeed($object, false);
    echo  " rows  sent\n";
    if ($feeddb && $feeddb->feedid != "" && $feeddb->feedid != null && $feeddb->feedid != 0) {

        Log::channel('walmart')->error('Dispatching to later check' . $feeddb->feedid);
        CheckGeneralFeed::dispatch($feeddb)->delay(now()->addMinutes(30));
    }
    $inserts = [];
    $toi = 0;
    DB::disableQueryLog();
    foreach ($object->getData() as $item) {
         
        $inserts[] = [
            'feed_id' => $feeddb->id,
            'contactpk' =>  isset($sups[$item['shipNode']])?$sups[$item['shipNode']]->contactpk:999,
            'supplier_pk' =>   isset($sups[$item['shipNode']])?$sups[$item['shipNode']]->PK:0,
            'marketSellerSku' => $mi[$item['productId']],
            'qty_avail' =>0
        ];
        $toi++;
        if ($toi == 1000) {
            FeedItemInventoryHistory::insert($inserts);
            $toi = 0;
            $inserts = [];
        }
    }
    if (count($inserts) > 0) {
        FeedItemInventoryHistory::insert($inserts);
    }
    DB::enableQueryLog();
    $ii++;
});