<?php

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

$ch = new \App\Library\Sophio\Exporters\CommerceHubBasicExporter();
$ch->exportNew(['limit'=>1000,'offset'=>0]);