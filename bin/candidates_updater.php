<?php

use App\Exceptions\ExceptionMailAction;
use Sophio\Common\Services\IsoCodesService;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

//$candidates = \Sophio\Common\Models\Products\Candidate::whereRaw('mod(id,4)=0')->where('updated_at','<',\Illuminate\Support\Carbon::today()->startOfDay());
$candidates = \Sophio\Common\Models\PIM\Product::where('oversized','=',0)->where('id','>',1767224)->orderBy('id','desc');
 $candidates->chunk(1000,function($cs){
    foreach($cs as $candidate) {
        echo $candidate->id."\n";
        $updater = new \App\Library\Sophio\ProductUpdater($candidate);
        $updater->initProductFeedSource();
        $changed= $updater->setDimensions();
        if($changed) {
            $dirty=$updater->getCandidate()->getDirty();
            if( (isset($dirty['width']) && round($dirty['width'],2)!=round($updater->getCandidate()->getOriginal('width'),2) )||
                (isset($dirty['height']) && round($dirty['height'],2)!=round($updater->getCandidate()->getOriginal('height'),2) )||
                (isset($dirty['length']) && round($dirty['length'],2)!=round($updater->getCandidate()->getOriginal('length'),2) )||
                (isset($dirty['weight']) && round($dirty['weight'],2)!=round($updater->getCandidate()->getOriginal('weight'),2) ) ||
                isset($dirty['overweight']) || isset($dirty['oversized'])

            ) {
                echo $candidate->mfg_code . ' ' . $candidate->part_number_unformatted .
                    print_r(['width' => $updater->getCandidate()->getOriginal('width'),
                        'height' => $updater->getCandidate()->getOriginal('height'),
                        'length' => $updater->getCandidate()->getOriginal('length'), 'weight' => $updater->getCandidate()->getOriginal('weight')

                    ], true) . print_r($updater->getCandidate()->getDirty(), true) . "\n";

                $updater->save();
            }
        }


    }
}) ;
exit();
\Illuminate\Support\Facades\Config::set('tenant_db','sophio_fbs');
$c = new \Sophio\Common\Models\PIM\Product();
$cs = $c->where('created_at','>','2023-05-01')->get();
foreach($cs as $candidate) {
    $candy = new \App\Library\Sophio\ProductUpdater($candidate);
    $candy->initProductFeedSource();
    $candy->refresh();
    $candy->save();
}
exit();
$cs = $c->whereNull('weight')->whereHas('partshareprice',function($query) { $query->where('Weight','>',0);})->get();
foreach($cs as $candidate) {

    $updater = new \App\Library\Sophio\ProductUpdater($candidate);
    $updater->initProductFeedSource();
    $changed= $updater->setDimensions();
    $updater->save();
    if($changed) {
        echo $candidate->mfg_code.' '.$candidate->part_number_unformatted."\n";
    }
}

exit();
foreach($c->where(function($query){
    $query->where('upc_source','')->whereNotNull('gtin');
})->lazy() as $candidate ) {
        if(\Sophio\Common\Services\IsoCodesService::extractUpc($candidate->gtin)){
            echo "update ".$candidate->part_number_unformatted." ".$candidate->gtin."\n";
            $candidate->upc = \Sophio\Common\Services\IsoCodesService::extractUpc($candidate->gtin);
            $candidate->upc_source = $candidate->gtin_source;
            $candidate->save();


        }
}
exit();
/*
config(['logging.default'=>'imageprocessing']);
foreach ($c->where('has_image','=',1)->where('image_source','<>','FBS')->cursor() as $candidate) {
    try {
        echo $candidate->mfg_code . ' ' . $candidate->part_number_unformatted . "\n";
        $updater = new \App\Library\Sophio\ProductUpdater($candidate);
        $updater->initProductFeedSource();
        $updater->setImage(false);
        $updater->save();
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::error($e->getMessage());
        (new ExceptionMailAction())($e);
    }
}
*/
$i=0;/*
foreach($c->where('warranty','[]')->where(function($query){
    $query->where('gtin_source','!=','')->orWhere('upc_source','!=','');
})->lazy() as $candidate ) {
*/
foreach($c->whereDoesntHave('package')->whereHas('productfeed',function($query){
    $query->where('pt_num',7212);
    $query->whereHas('pies',function($q) {
        $q->whereHas('packages',function($x) {
            $x->where('PackageLevelGTIN','<>','');
        });
    });
})->lazy() as $candidate ) {

    try {

            echo $candidate->mfg_code . ' ' . $candidate->part_number_unformatted . "\n";
            $updater = new \App\Library\Sophio\ProductUpdater($candidate);
            $updater->initProductFeedSource();
           // $updater->setDescription();
            $updater->setPackage();
            $updater->save();
      $i++;

    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::error($e->getMessage());
     //   (new ExceptionMailAction())($e);
    }
}
echo "Updated ".$i."\n";
/*
$feedItems = \Sophio\Walmart\Models\FeedItem::where('data','like','%3440047.jpg%')->select('mfg_code','part_number_unformatted');
foreach($feedItems->lazy() as $f) {
    $updater = new \App\Library\Sophio\ProductUpdater(\Sophio\Common\Models\Products\Candidate::where('mfg_code','=',$f->mfg_code)->where('part_number_unformatted','=',$f->part_number_unformatted)->first());
    $updater->initProductFeedSource();
    $updater->setImage(true);
    $updater->save();
}
*/