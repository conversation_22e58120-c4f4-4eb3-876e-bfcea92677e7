<?php

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Jobs\BatchFeedsSender;
use Sophio\Walmart\Jobs\CheckFeed;
use Sophio\Walmart\Jobs\SendFeed;
use Sophio\Walmart\Library\Feeds\InventoryFeeds;
use Sophio\Walmart\Library\ItemsFeeds;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['logging.default'=>'walmart']);
Config::set('tenant_db', 'sophio_fbs');
$buyer_profile = \Sophio\Common\Models\FBS\BuyerProfile::where('id', 1)->first();

$walmart = new \Sophio\FBSFeedExporter\Library\DataFeedExport\Walmart($buyer_profile, ['supplier' => 620 ,'tenant_db'=>'sophio_fbs','generateCost'=>false,'emails'=>['<EMAIL>',]]);
$walmart->generateFiles();