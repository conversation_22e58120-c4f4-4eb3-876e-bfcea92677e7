<?php

use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

config(['tenant_db'=>'sophio_fbs']);
$settings = new Settings(['fbs_database' => 'sophio_fbs', 'seller_database' => 'carparts_aces', 'parallel_soap' => 0]);
$lineitems = \Sophio\Common\Models\FBS\LineItem::where('timein','>=','2024-05-20')
    ->where('sku','3653')->where('custpk',289297)->where('track_num','')->get();

foreach($lineitems as $lineitem){
    if(isset($lineitem->list2['SHIPSTATIONORDERID']) && $lineitem->list2['SHIPSTATIONORDERID'] != ''){
        $shipstationManager = new ShipStationManager($settings);
        $shipstationManager->setSupplierCreds(Supplier::find($lineitem->profilepk));
        $shipstationManager->createClient();
        $r = $shipstationManager->deleteOrder($lineitem->list2['SHIPSTATIONORDERID']);
        $lineitem->list2['SHIPSTATIONORDERID'] = null;
        $lineitem->track_num = "";
        $lineitem->save();
        (new SupplierLogCreate())([
            'storepk' => $settings->getStore()->STOREPK,
            'timein' => now(),
            'invpk' => $lineitem->invpk,
            'accountnum' => $lineitem->seller->pk,
            'apitype' => 'SHIPSTATION',
            'action' => '/orders/delete',
            'request' => request()->get('shipstationid'),
            'profilepk' => $lineitem->profilepk,
            'response' => json_encode($r),
            'clientip' => request()->server('REMOTE_ADDR')
        ]);
        $invoice = $lineitem->invoice;
        $invoice->STORENOTES = "Deleted Shipstation order ID " . request()->get('shipstationid') . " by " .  'adrian' . ' ' . \Carbon\Carbon::now() . "\n" . $invoice->STORENOTES;
        $invoice->norecalc = true;
        $invoice->noforward_update = true;
        $invoice->save();
        echo $lineitem->invpk."\n";
        sleep(1);
    }
}