<?php

use App\Mail\GeneralMail;
use Illuminate\Support\Facades\Mail;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);
config(['sophio.admin.default_database'=>'sophio_fbs']);
Mail::to(['<EMAIL>','<EMAIL>']) ->cc(['<EMAIL>','<EMAIL>'])->queue(new GeneralMail([
    'subject' => "mail test again" ,
    'description'=>\Illuminate\Support\Carbon::now(),
    'view' => 'emails.generalmail',
    'content' => ['description'=>\Illuminate\Support\Carbon::now()],
    'invpk'=>1,
    'custpk'=>2,
    'profilepk'=>3

]));