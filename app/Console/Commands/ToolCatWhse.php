<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSInventoryImporter\Library\Toolcat\ImportWhse;
use Sophio\FBSInventoryImporter\Models\Imports;

class ToolCatWhse extends Command
{
    protected $signature = 'toolcat:whseimport {database}';

    protected $description = 'Import WHSE Data';

    public function handle()
    {
        config(['tenant_db'=>$this->argument('database')]);
        $supplier = Supplier::find(199);
        $iw = new ImportWhse($supplier,config('tenant_db'));
        $iw->importFeed();

    }
}