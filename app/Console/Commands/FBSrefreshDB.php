<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
class FBSrefreshDB extends Command
{
    protected $signature = 'sophio:refreshdb';

    protected $description = '';
    public function handle()
    {
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_sendprofiles; INSERT INTO  ".config('sophio.admin.default_database').".wws_sendprofiles SELECT * FROM b2ccentral_aces.wws_sendprofiles");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_invoice; INSERT INTO  ".config('sophio.admin.default_database').".wws_invoice SELECT * FROM b2ccentral_aces.wws_invoice");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_customers; INSERT INTO  ".config('sophio.admin.default_database').".wws_customers SELECT * FROM b2ccentral_aces.wws_customers");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_sendprofiles; INSERT INTO  ".config('sophio.admin.default_database').".wws_sendprofiles SELECT * FROM b2ccentral_aces.wws_sendprofiles");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_lineitems; INSERT INTO  ".config('sophio.admin.default_database').".wws_lineitems SELECT * FROM b2ccentral_aces.wws_lineitems");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".wws_pricing; INSERT INTO  ".config('sophio.admin.default_database').".wws_pricing SELECT * FROM b2ccentral_aces.wws_pricing");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".marketplaces; INSERT INTO  ".config('sophio.admin.default_database').".marketplaces SELECT * FROM b2ccentral_aces.marketplaces");
        DB::statement("TRUNCATE TABLE ".config('sophio.admin.default_database').".marketplaces_suppliers; INSERT INTO  ".config('sophio.admin.default_database').".marketplaces_suppliers SELECT * FROM b2ccentral_aces.marketplaces_suppliers");
    }
}