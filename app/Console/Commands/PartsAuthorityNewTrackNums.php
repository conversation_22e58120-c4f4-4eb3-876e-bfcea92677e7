<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Sophio\PartsAuthority\src\Library\Actions\GetOrdersWithTracking;

class PartsAuthorityNewTrackNums
    extends Command

{
    protected $signature = 'sophio:partsauthnewtracknums {when}';

    protected $description = 'Find orders already having a tracknum from Parts Authority';
    public function handle()
    {
        (new GetOrdersWithTracking())($this->argument('when'));
    }
}