<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Sophio\Common\Repository\Settings;

class ToolcatTracking  extends Command
{
    protected $signature = 'toolcat:tracking {date?}';
    protected $description = 'Retrieve daily tracking from NPW';
    public function handle()
    {
        config(['sophio.admin.default_database'=>'toolsequipments_aces']);
        config(['tenant_db'=>'toolsequipments_aces']);
        if($this->argument('date')) {
            $date = Carbon::createFromFormat('m-d-y',$this->argument('date'));

        }else{
            $date = Carbon::now();
        }
        $settings = new Settings(['fbs_database'=>'toolsequipments_aces']);
        $settings->set('no_update_seller', true);
        $tm  =  new \App\Library\Sophio\Catalog\TrackingManager($settings);
        if($tm->getFileFromFTP($date,'te-tracking-new-_','te-tracking-new-_06-13-25.csv'))
        {
            $tm->loadFileInTable();
            $tm->processTrackings();
         //   $tm->processTrackings();
        }
    }
}