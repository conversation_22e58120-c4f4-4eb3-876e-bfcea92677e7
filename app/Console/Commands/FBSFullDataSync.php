<?php

namespace App\Console\Commands;

use App\Library\Sophio\MigrateDataSync;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Console\Command;

class FBSFullDataSync extends Command
{
    protected $signature = 'sophio:fbsfulldatasync';
    protected $description = 'FBS FULL Data sync';

    public function handle()
    {
        $ds = new MigrateDataSync();
        $ds->syncfromB2C();
        $toSync = ['lineitem', 'invoice'];
        foreach (CarbonPeriod::create('2021-01-01', '1 month', Carbon::today()) as $month) {

            foreach ($toSync as $which) {
                echo $which . " " . $month->startOfMonth()->format('m/d/Y') . ' ' . $month->endOfMonth()->format('m/d/Y') . " \n";
                $ds->syncFromFox($which, $month->startOfMonth()->format('m/d/Y') . '|' . $month->endOfMonth()->format('m/d/Y'));
            }

        }
    }
}