<?php

namespace App\Console\Commands;

use App\Library\Sophio\NonAppProductUpdater;
use App\Library\Sophio\PIM\ProductsUtils;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\Partshare\FlattenedData;
use Sophio\Common\Models\PIM\Product;

class ProductsNonAppsRefresh
    extends Command
{
    protected $signature = 'sophio:productsrefreshnonapps';

    protected $description = 'Product nonapps  refresher';

    public function handle()
    {
        $pu = new ProductsUtils();
        $pu->findNewNonAppsLimitedByBrands();

    }
}