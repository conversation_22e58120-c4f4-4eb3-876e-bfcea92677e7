<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Sophio\Walmart\Actions\CheckItem;
use Sophio\Walmart\Jobs\CheckProgressPublishing;
use Sophio\Walmart\Models\MarketplaceItem;

class WalmartFixId     extends Command
{
    protected $signature = 'sophio:walmartfixid';

    protected $description = 'Scan for published Walmart';

    public function handle()
    {
        config(['logging.default'=>'walmart']);
        $settings = ['tenant_db'=>'sophio_fbs'];

        $checkitem = new  CheckItem($settings);
        $mis = MarketplaceItem::whereNotNull('marketSku')->where('pack_qty',1)->where('marketSku','>',0)->where('status','<>','NOTFOUND')
            ->groupBy('marketSku')->havingRaw('COUNT(DISTINCT marketSellerSku)>1');
        foreach($mis->cursor() as $mi) {
            sleep(2);
            $mixs = MarketplaceItem::where('marketSku',$mi->marketSku)->get();
            $found =false;
            $failed = [];
            foreach($mixs as $mix) {
                if($checkitem($mix)) {
                    echo "found  ".$mix->marketSellerSku."\n";
                    $found = true;
                }else{
                    $failed[] = $mix->pk;
                }

            }
            if($found) {
                foreach($mixs as $mix) {
                    if(in_array($mix->pk, $failed)) {
                        echo "deleting ".$mix->marketSellerSku."\n";
                        $mix->status='DELETE';
                        $mix->save();
                    }
                }
            }else{
                echo "not found\n";
            }

        }
    }
}