<?php

namespace App\Library\Sophio;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\Invoice;

class MigrateDataSync
{
    public function syncfromB2C()
    {
        DB::affectingStatement("REPLACE INTO  sophio_fbs.wws_lookups  SELECT * from sophio_core.wws_lookups");
        DB::affectingStatement("REPLACE INTO  sophio_fbs.wws_defects  SELECT * from b2ccentral_aces.wws_defects");
        DB::affectingStatement("REPLACE INTO sophio_fbs.marketplaces SELECT * FROM b2ccentral_aces.marketplaces");
        DB::affectingStatement("REPLACE INTO sophio_fbs.marketplaces_suppliers SELECT * FROM b2ccentral_aces.marketplaces_suppliers");
        DB::affectingStatement("REPLACE INTO sophio_fbs.product_warehouse_overrides SELECT * FROM b2ccentral_aces.product_warehouse_overrides");
    }

    public function syncToTestFBS()
    {
        DB::statement("DROP TABLE test_fbs.wws_invoice");
        DB::statement("CREATE TABLE test_fbs.wws_invoice LIKE sophio_fbs.wws_invoice");
        DB::affectingStatement("REPLACE INTO test_fbs.product_warehouse SELECT * FROM sophio_fbs.product_warehouse");
        DB::affectingStatement("REPLACE INTO test_fbs.wws_sendprofiles SELECT * FROM sophio_fbs.wws_sendprofiles");
        DB::affectingStatement("REPLACE INTO test_fbs.wws_customers SELECT * FROM sophio_fbs.wws_customers");
        DB::affectingStatement("REPLACE INTO test_fbs.partshare_pricing SELECT * FROM sophio_fbs.partshare_pricing");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_226160 SELECT * FROM sophio_fbs.datafeed_226160");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_281923 SELECT * FROM sophio_fbs.datafeed_281923");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_286455 SELECT * FROM sophio_fbs.datafeed_286455");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_288240 SELECT * FROM sophio_fbs.datafeed_288240");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_289235 SELECT * FROM sophio_fbs.datafeed_289235");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_289297 SELECT * FROM sophio_fbs.datafeed_289297");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_289367 SELECT * FROM sophio_fbs.datafeed_289367");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_289378 SELECT * FROM sophio_fbs.datafeed_289378");
        DB::affectingStatement("REPLACE INTO test_fbs.datafeed_289381 SELECT * FROM sophio_fbs.datafeed_289381");

    }

    public function syncFromFox($which, $when)
    {
        if ($which == "full") {
            /*
             * RENAME TABLE wws_invoice TO wws_invoice_old;RENAME TABLE wws_invoice_new TO wws_invoice;
             * RENAME TABLE wws_lineitems TO wws_lineitems_old;RENAME TABLE wws_lineitems_new TO wws_lineitems;
             * RENAME TABLE returnreason TO returnreason_old;RENAME TABLE returnreason_new TO returnreason;
             * RENAME TABLE wws_penalties TO wws_penalties_old;RENAME TABLE wws_penalties_new TO wws_penalties;
             * RENAME TABLE wws_returnlines TO wws_returnlines_old;RENAME TABLE wws_returnlines_new TO wws_returnlines;
             * RENAME TABLE wws_statements TO wws_statements_old;RENAME TABLE wws_statements_new TO wws_statements;
             *
             */
            //   $this->updateInvoiceFile('sophio_fbs.wws_invoice_new', '/files/FBS/migration/wws-invoice-20170909-20230910.txt');
            //  $this->updateLineItemFile('sophio_fbs.wws_lineitems_new', '/files/FBS/migration/wws-lineitems-20170909-20230910.txt');
            //   $this->updateReturnreasonFile('sophio_fbs.returnreason_new', '/files/FBS/migration/returnreason-20170909-20230910.txt');
            //   $this->updatePenaltiesFile('sophio_fbs.wws_penalties_new', '/files/FBS/migration/wws-penalties-20170909-20230910.txt');
            //    $this->updateReturnlinesFile('sophio_fbs.wws_returnlines_old', '/files/FBS/migration/wws-returnlines-20170911-20230912.txt');
            // $this->updateProfileFile('sophio_fbs.wws_sendprofiles_new', '/files/FBS/migration/wws-sendprofiles-20170909-20230910.txt');
            //   $this->updateCustomerFile('sophio_fbs.wws_customers_new', '/files/FBS/migration/wws-customers-20170909-20230910.txt');
            // $this->updateReturnallowancFile('sophio_fbs.wws_returnallowance_new', '/files/FBS/migration/wws-returnallowance-20170909-20230910.txt');
            //   $this->updateReturnsFile('sophio_fbs.wws_returns_new', '/files/FBS/migration/wws-returns-20170911-20230912.txt');
            //    $this->updateStatementsFile('sophio_fbs.wws_statements_old', '/files/FBS/migration/wws-statements-20170909-20230910.txt');
            //   $this->updateStatementsRawFile('sophio_fbs.wws_statements_raw', '/files/FBS/migration/wws-statements-raw-20170911-20230912.txt');
        }
        if ($which == '') {
            $which = ['invoice', 'lineitem', 'customer'];
        } else {
            $which = explode(',', $which);
        }
        if (in_array('invoice', $which)) {
            try {
                $this->doInvoice($when);
            } catch (\Exception $e) {
                report($e);
            }

        }
        if (in_array('authorize', $which)) {
            try {
                $this->doAuthorize($when);
            } catch (\Exception $e) {
                report($e);
            }

        }
        if (in_array('lineitem', $which)) {
            try {
                $this->doLineitem($when);
            } catch (\Exception $e) {
                report($e);
            }

        }
        if (in_array('customer', $which)) {
            try {
                $this->doCustomer($when);
            } catch (\Exception $e) {
                report($e);
            }

        }
        if (in_array('profile', $which)) {
            try {
                $this->doProfile($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('returnreason', $which)) {
            try {
                $this->doReturnreason($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('returns', $which)) {
            try {
                $this->doReturns($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('returnlines', $which)) {
            try {
                $this->doReturnlines($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('statements', $which)) {
            try {
                $this->doStatements($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('statements_raw', $which)) {
            try {
                $this->doStatementsRaw($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('penalties', $which)) {
            try {
                $this->doPenalties($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
        if (in_array('returnallowance', $which)) {
            try {
                $this->doReturnallowance($when);
            } catch (\Exception $e) {
                report($e);
            }
        }
    }

    public function doProfile($when)
    {
        echo "DOING PROFILE";
        $type = 'wws_sendprofiles';
        $invoice_table = "sophio_fbs.wws_sendprofiles";
        $template_name = $this->getExportMe($when, $type);
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
            return false;
        }
        echo $template_name."\n";
        $this->updateProfileFile($invoice_table, '/tmp/' . $template_name);

        //DB::affectingStatement("REPLACE $invoice_table SELECT * FROM  sophio_fbs.wws_sendprofiles_custom");
        $dates = ['TIMESTAMP'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $invoice_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }

    }

    public function updateProfileFile($invoice_table, $template_name)
    {
        $query = "load data local infile '" . $template_name . "' replace into table " . $invoice_table . "    COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES (PK,NAME,NOTES,LINECODE,SUP,SETTINGS,@TIMESTAMP,ACTIVE,ACCOUNT,PASSWORD,USERNAME,DCFID,SELLERID) 
        set TIMESTAMP=str_to_date(@TIMESTAMP,'%m/%d/%Y %h:%i:%s %p')";
        echo $query . "\n";
        $result = DB::affectingStatement($query);

    }

    public function doAuthorize($when)
    {
        $type = "authorize";
        $invoice_table = "sophio_fbs.authorize";
        $template_name = $this->getExportMe($when, $type, 'when');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateAuthorizeFile($invoice_table, '/tmp/' . $template_name);
        }

    }

    public function doInvoice($when)
    {

        $type = 'wws_invoice';
        $invoice_table = "sophio_fbs.wws_invoice";
        $template_name = $this->getExportMe($when, $type, 'invdate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateInvoiceFile($invoice_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'completed');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateInvoiceFile($invoice_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'when');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";

        } else {
            $this->updateInvoiceFile($invoice_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'timestamp');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";

        } else {
            $this->updateInvoiceFile($invoice_table, '/tmp/' . $template_name);
        }

        print_r(['invoice' => Invoice::filterDate(['invdate', $when])->count()]);

    }

    public function updateAuthorizeFile($table, $template)
    {
        $query = "load data local infile '" . $template . "' replace into table " . $table . "    COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES
     (`RESPONSECD`,`RESPONSESU`,`REASONCODE`,`REASONTEXT`,`APPROVALCD`,`AVSRESULT`,`TRANSID`,`INVPK`,`DESCRIPTION`,`AMOUNT`,`METHOD`,`TRANSTYPE`,`CUSTID`,`FIRSTNAME`,`LASTNAME`,`COMPANY`,`ADDRESS`,`CITY`,`STATE`,`ZIP`,
     `COUNTRY`,`PHONE`,`FAX`,`EMAIL`,`ST_FIRST`,`ST_LAST`,`ST_COMPANY`,`ST_ADDRESS`,`ST_CITY`,`ST_STATE`,`ST_ZIP`,`ST_COUNTRY`,`TAXAMT`,`DUTY`,
     `FREIGHT`,`TAXEXEMPT`,`PONUMBER`,`MD5`,`CVV2`,`CAVV`,@WHEN,`STOREPK`,`STATUS`,`GATEWAY`,`PROCESSOR`,`EXP_DATE`,`CARDNO`,
     `TRANSID_OR`,`CONTACTPK`,`RAWPOST`,`TRANSTAG`,`PK`,`ST_TYPE`,`CURRENCYCD`,`PAYEEEMAIL`,`MERCHANTID`,`PAYEREMAIL`,`PAYERID`,`CAPTUREID`,`PICKUP_PK`,`SUPSTMNTID`) 
        set `WHEN`=if(str_to_date(@WHEN,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@WHEN,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@WHEN,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        print_r($result);
        $dates = ['when'];

        foreach ($dates as $date) {
            $result = DB::affectingStatement("UPDATE $table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
            print_r([$date => $result]);
        }
    }

    public function updateInvoiceFile($invoice_table, $template_name)
    {
        $query = "load data local infile '" . $template_name . "' replace into table " . $invoice_table . "    COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES (pk,custpk,storepk,warehouse,invno,@invdate,@completed,@downloaded,invtotal,ponumber,tax,taxrate,tax1,tax2,tax3,tax4,taxrate1,taxrate2,taxrate3,taxrate4,handling,weight,cctype,cc,ccexp,cvv2,ccresult,ccresultx,ccprocfee,ccpaid,paymethod,notes,shipping,service,carrier,shipcosts,shipdisks,xml,invcode,@when,coretotal,firstname,lastname,company,address,address2,city,county,state,zip,st_dest,st_email,st_name,st_company,st_atto,countryid,phone,st_addr,st_addr2,st_city,st_county,st_state,st_zip,st_phone,st_country,st_ctryid,sessionid,shiptrakid,discountid,discount,note,ordercode,track_num,shippings,salesrep,email,invstatus,handfee,expressfee,warrantyfe,warrantyte,extdwar1,extdwar2,extdwar3,shiptype,storenotes,pickup_pk,custtype,install,ip,heardfrom,printed,taxid,abcode,panum,dealerid,lpnum,taxauth,taxexpire,referngurl,@timestamp,ebayuserid,ebayordern,handlingc,marketdata,shipgcost,taxcost,invtotalc,supinvpk,retshipcst,costtotal,@lastshipdt,transfees,mktplcordn,st_type,invtotalcs,supstmntid,optin,duty,brokerage,mobile,labor,accountnum,user_pk,apiorderid,vin) 
        set invdate=if(str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@invdate,'%m/%d/%Y %H:%i:%s')),
        completed=if(str_to_date(@completed,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@completed,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@completed,'%m/%d/%Y %H:%i:%s')),
        TIMESTAMP=if(str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timestamp,'%m/%d/%Y %H:%i:%s')),
        `WHEN`=if(str_to_date(@when,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@when,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@when,'%m/%d/%Y %H:%i:%s')),
        lastshipdt= if(@lastshipdt!='/  /     :  :', if(str_to_date(@lastshipdt,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@lastshipdt,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@lastshipdt,'%m/%d/%Y %H:%i:%s')),NULL)";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        print_r($result);
        $dates = ['invdate', 'completed', 'TIMESTAMP', 'WHEN', 'lastshipdt'];

        foreach ($dates as $date) {
            $result = DB::affectingStatement("UPDATE $invoice_table SET `" . $date . "`=NULL,brokerage=1,vin='$date' WHERE `" . $date . "`='0000-00-00'");
            print_r([$date => $result]);
        }
    }

    public function doLineitem($when)
    {
        $lineitems_table = "sophio_fbs.wws_lineitems";
        $type = 'wws_lineitems';
        $template_name = $this->getExportMe($when, $type, 'timein');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateLineItemFile($lineitems_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'timeout');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateLineItemFile($lineitems_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'timestamp');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateLineItemFile($lineitems_table, '/tmp/' . $template_name);
        }


    }

    public function updateLineItemFile($lineitems_table, $template_name)
    {
        $query = "load data local infile  '" . $template_name . "' replace into table  " . $lineitems_table . "  COLUMNS TERMINATED BY '\t'  ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES
(pk,storepk,invpk,custpk,itempk,sup_ord_id,mfr,sku,descript,qty,qty_bo,price,list,core,itemtotal,discount,weight,taxrate,@timein,@timeout,punchedout,color,size,list1,list2,@timestamp,cost,coreprice,make,skuform,sup,whse,linecode,itemform,year,model,engine,cat,subcat,ip,cookieid,jobber,dealer,freightchg,minqty,parttype,skill,lbtime,makeid,modelid,engineid,catid,subcatid,wd,ptype,wdlinecode,coreid,vin,image,stock,freeground,nofreight,oversize,nodiscount,shiprest,calloutid,ustock,uweight,referngurl,usersku,profilepk,noship,`condition`,type,mileage,placement,posonveh,bodycolor,repairhrs,locinyard,stocknum,frgservice,wroption,shipping,itemtax,warranty,sellerid,contactpk,zipcode,currencyrt,currencycd,mrktplcsku,mrktplcid,packqty,skulength,skuwidth,skuheight,skugirth,delivery,supstmntid,track_num,handlingc,invtotalc,supinvpk,basevehid,laborprice,labordesc,laborsrch,special,defectstat,bin,scost,shiplabel,warrlabel,upc,nwtosophio,shiptype,qty_ord,cprice,pickup_pk,blength,bwidth,bheight,bweight,bdimunit,transfees,amazonprc,fstatus,salesrep) 
set timein=if(str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timein,'%m/%d/%Y %H:%i:%s')),
timeout=if(str_to_date(@timeout,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timeout,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timeout,'%m/%d/%Y %H:%i:%s')),
timestamp=if(str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timestamp,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        print_r($result);
        $dates = ['timein', 'timeout', 'timestamp'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $lineitems_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doReturnreason($when)
    {
        $returnreason_table = "sophio_fbs.returnreason";
        $type = 'returnreason';
        $template_name = $this->getExportMe($when, $type);
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
            return false;
        }
        $this->updateReturnreasonFile($returnreason_table, '/tmp/' . $template_name);


    }

    public function updateReturnreasonFile($returnreason_table, $template_name)
    {
        $query = "load data local infile  '" . $template_name . "' replace into table  " . $returnreason_table . "  COLUMNS TERMINATED BY '\t'  ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES
(pk,name,help,charge,type,peritem,forcores,freeshipm,disabled,showorder,calltagok,@timestamp,returntax,custtype,custreason,custretid,noreceive,days,`condition`,okrefund)
 set timestamp=if(str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timestamp,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['timestamp'];

        foreach ($dates as $date) {

            DB::affectingStatement("UPDATE $returnreason_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doReturns($when, $datefield = 'reqdate')
    {

        $returnreason_table = "sophio_fbs.wws_returns";
        $type = 'wws_returns';
        $template_name = $this->getExportMe($when, $type, 'reqdate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnsFile($returnreason_table, '/tmp/' . $template_name);
        }

        $template_name = $this->getExportMe($when, $type, 'refunddate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnsFile($returnreason_table, '/tmp/' . $template_name);
        }

        $template_name = $this->getExportMe($when, $type, 'supcmemodt');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnsFile($returnreason_table, '/tmp/' . $template_name);
        }

    }

    public function updateReturnsFile($returnreason_table, $template_name)
    {
        $query = "load data local infile  '" . $template_name . "' REPLACE into table  " . $returnreason_table . "  COLUMNS TERMINATED BY '\t'  ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES
(pk,invpk,custpk,storepk,shiptype,shipmeth,shipcost,totweight,taxrate,tax,charges,rettotal,status,@reqdate,@apprdate,st_name,st_addr,st_addr2,st_city,st_state,st_zip,st_country,custnotes,ownernotes,st_company,ccresultx,suprmaid,supcmemoid,mktplrmaid,supcredamt,invtotal,@supcmemodt,@invdate,tax1,tax2,tax3,taxrate1,taxrate2,taxrate3,rtracknum,mktplctype,marketdata,@refunddate,returnto,supinvpk,returned,invstatus,custtype,returntax,st_type,supstmntid,buyrmaid,ccresult,ponumber,pickup_pk,user_pk) 
set reqdate=if(str_to_date(@reqdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@reqdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@reqdate,'%m/%d/%Y %H:%i:%s')),
apprdate=if(str_to_date(@apprdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@apprdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@apprdate,'%m/%d/%Y %H:%i:%s')),
supcmemodt=if(str_to_date(@supcmemodt,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@supcmemodt,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@supcmemodt,'%m/%d/%Y %H:%i:%s')),
invdate=if(str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@invdate,'%m/%d/%Y %H:%i:%s')),
refunddate=if(str_to_date(@refunddate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@refunddate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@refunddate,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['reqdate', 'apprdate', 'supcmemodt', 'invdate', 'refunddate'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $returnreason_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }

    }

    public function doReturnlines($when, $datefield = 'timein')
    {
        $type = 'wws_returnlines';
        $returnreason_table = "sophio_fbs.wws_returnlines";
        $template_name = $this->getExportMe($when, $type, "timein");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, "timeout");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";

        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, "received");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";

        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, "returned");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, "supcmemodt");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, "writeoff");
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateReturnlinesFile($returnreason_table, '/tmp/' . $template_name);
        }

    }

    public function updateReturnlinesFile($returnreason_table, $template_name)
    {
        $query = "load data local infile  '" . $template_name . "' replace into table  " . $returnreason_table . "  COLUMNS TERMINATED BY '\t'  ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES
(pk,storepk,invpk,linepk,retpk,sup_ord_id,mfr,sku,descript,qty,ord_qty,price,list,core,itemtotal,discount,weight,@timein,@timeout,punchedout,color,size,list1,cost,coreprice,cookieid,jobber,freightchg,wd,
retcode,retname,retchrge,retchrgtyp,retamount,retfreeshp,status,notes,corereturn,itempk,linecode,whse,suprmaid,profilepk,sup,shipping,itemtax,warranty,sellerid,supinvpk,supstmntid,
rtracknum,supcmemoid,@supcmemodt,supcredamt,wdlinecode,@received,receivedip,`condition`,rprofilepk,@returned,returnedid,sold,`bin`,custtype,imageurl,mktplrmaid,track_num,nwtosophio,
returnto,custpk,invtotalc,skuform,shipper,mtracknum,scost,creditreq,@writeoff,skurecvd,removedcre,rmaid) 
set timein=if(str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timein,'%m/%d/%Y %H:%i:%s')),
timeout=if(str_to_date(@timeout,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timeout,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timeout,'%m/%d/%Y %H:%i:%s')),
supcmemodt=if(str_to_date(@supcmemodt,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@supcmemodt,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@supcmemodt,'%m/%d/%Y %H:%i:%s')),
received=if(str_to_date(@received,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@received,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@received,'%m/%d/%Y %H:%i:%s')),
returned=str_to_date(@returned,'%m/%d/%Y'),
writeoff=if(str_to_date(@writeoff,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@writeoff,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@writeoff,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['returned', 'received', 'writeoff', 'timein', 'timeout', 'supcmemodt'];
        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $returnreason_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }

    }


    public function doCustomer($when)
    {
        $customers_table = "sophio_fbs.wws_customers";
        $type = 'wws_customers';
        $template_name = $this->getExportMe($when, $type);
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
            return false;
        }
        $this->updateCustomerFile($customers_table, '/tmp/' . $template_name);
        DB::affectingStatement("UPDATE sophio_fbs.wws_customers SET storenum=REGEXP_SUBSTR(company,\"[0-9]+\")   WHERE custtype='NAT'");


    }

    public function updateCustomerFile($customers_table, $template_name)
    {
        $query = " load data local infile '" . $template_name . "' replace into table " . $customers_table . "   COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES  (pk,sessionid,storepk,pinpk,company,lastname,firstname,address,address2,city,county,state,zip,zip4,country,countryid,shipping,st_name,st_addr,st_addr2,st_city,st_county,st_state,st_zip,st_phone,st_ctryid,st_ctry,`foreign`,phone,fax,email,url,cc,cvv2,ccexp,cctype,checkno,routingno,accountno,notes,@entered,@updated,@lastorder,heardfrom,referral,userid,password,active,source,downloaded,warehouse,whseemail,warehouse2,whseemail2,salesrep,salesrepem,accountnum,custpass,custtype,shippingcd,xml,usercount,epc,network,@created,@laston,@expires,badentry,hits,comments,type,year,make,model,ip,trans,engine,vin,optin,whse,taxable,taxid,garage,shiptype,st_dest,st_company,pstexempt,pickup_pk,install,paymethod,st_atto,abcode,panum,dealerid,fleetmakes,lpnum,mrparts,taxauth,taxexpire,@timestamp,st_email,oatoken,ebayuserid,twituserid,birthday,currentloc,gender,socialid,id_token,profileurl,utcoffset,pictureurl,likes,displaynam,fbscore,fbnfcnt,fbpfpct,fbsuidlc,contactpk,st_type,taxidstate,settings,mobile,carrier,service,role,uuid,@verified,ordmethod,storenum)
 set  entered=str_to_date(@entered,'%m/%d/%Y %T'),
 updated=if(str_to_date(@updated,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@updated,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@updated,'%m/%d/%Y %H:%i:%s')),
 lastorder=str_to_date(@lastorder,'%m/%d/%Y %T'), 
 created=if(str_to_date(@created,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@created,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@created,'%m/%d/%Y %H:%i:%s')),
 laston=if(str_to_date(@laston,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@laston,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@laston,'%m/%d/%Y %H:%i:%s')),
 expires=if(str_to_date(@expires,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@expires,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@expires,'%m/%d/%Y %H:%i:%s')), 
 timestamp=if(str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timestamp,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timestamp,'%m/%d/%Y %H:%i:%s')),
 verified=if(str_to_date(@verified,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@verified,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@verified,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);

        var_dump($result);
        $dates = ['entered', 'updated', 'lastorder', 'created', 'laston', 'expires', 'timestamp', 'verified'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $customers_table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doStatements($when)
    {
        $table = "sophio_fbs.wws_statements";
        $type = 'wws_statements';
        $template_name = $this->getExportMe($when, $type, 'when');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";

        } else {
            $this->updateStatementsFile($table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'pmtrequest');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateStatementsFile($table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'paid');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateStatementsFile($table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'dapplied');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateStatementsFile($table, '/tmp/' . $template_name);
        }

    }

    public function updateStatementsFile($table, $template_name)
    {
        $query = " load data local infile '" . $template_name . "' replace into table " . $table . "   COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES  
        (pk,profilepk,@stmntdate,amount,stmnttype,supname,storepk,debitamt,debitcnt,creditamt,creditcnt,supstmntid,variance,@when,ip,sophiouser,notes,results,payment,@paid,paymentid,pathsupfil,docnumber,mcreditfnm,deductions,@pmtrequest,returnallo,penalties,@dapplied,unrecogniz,dblcharged,livariance,anticipate,@duedate) 
        set stmntdate=if(str_to_date(@stmntdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@stmntdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@stmntdate,'%m/%d/%Y %H:%i:%s')),
        `when`=if(str_to_date(@when,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@when,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@when,'%m/%d/%Y %H:%i:%s')),
        paid=if(str_to_date(@paid,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@paid,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@paid,'%m/%d/%Y %H:%i:%s')),
        pmtrequest=if(str_to_date(@pmtrequest,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@pmtrequest,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@pmtrequest,'%m/%d/%Y %H:%i:%s')),
        dapplied=if(str_to_date(@dapplied,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@dapplied,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@dapplied,'%m/%d/%Y %H:%i:%s')),
        duedate=str_to_date(@duedate,'%m/%d/%Y %T')";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['stmntdate', 'when', 'paid', 'pmtrequest', 'dapplied', 'duedate'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doStatementsRaw($when)
    {
        $table = "sophio_fbs.wws_statements_raw";
        $type = 'wws_statements_raw';

        $template_name = $this->getExportMe($when, $type, 'supinvdate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateStatementsRawFile($table, '/tmp/' . $template_name);
        }


        $template_name = $this->getExportMe($when, $type, 'bankdate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
        } else {
            $this->updateStatementsRawFile($table, '/tmp/' . $template_name);
        }


    }

    public function updateStatementsRawFile($table, $template_name)
    {
        $query = " load data local infile '" . $template_name . "' replace into table " . $table . "   COLUMNS TERMINATED BY '\t' OPTIONALLY ENCLOSED BY '\"'  LINES TERMINATED BY '\r\n' IGNORE 2 LINES  
        (account,acctname,branch,supinvpk,invtype,stmtdate,ordnum,ponumber,cost,coreprice,invtotalc,discount,invpk2,linecode,lineid,qty,itemtotal,sku,descript,tax,handlingc,supstmntid,supcmemoid,sup_ord_id,@when,totalamt,profilepk,supname,@supinvdate,duedate,@bankdate,pathsupfil,paid,checknum,cinvpk,sup,resultcode,pkstmnt,isinvpk,ismanifest,isprofsku,pk,variance,invpk,notes,custtype,returnallo,penalties,linepk,transfees) 
        set `when`=str_to_date(@when,'%m/%d/%Y %T'),supinvdate=str_to_date(@supinvdate,'%m/%d/%Y %T'),bankdate=str_to_date(@bankdate,'%m/%d/%Y %T')";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['when', 'supinvdate', 'bankdate'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doPenalties($when)
    {
        $table = "sophio_fbs.wws_penalties";
        $type = 'wws_penalties';
        $template_name = $this->getExportMe($when, $type, 'timein');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
            return false;
        }
        $this->updatePenaltiesFile($table, '/tmp/' . $template_name);


    }

    public function updatePenaltiesFile($table, $template_name)
    {
        $query = " load data local infile '" . $template_name . "' replace into table " . $table . "   COLUMNS TERMINATED BY '\t' OPTIONALLY ENCLOSED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES  
        (pk,market,ponumber,invpk,profilepk,pentype,trknum,carrier,@orderdate,expshipdat,actshipdat,dayslate,cancelreas,supstmntid,@deducdate,amount,@timein,oprofilepk) 
        set orderdate=str_to_date(@orderdate,'%m/%d/%Y'),deducdate=str_to_date(@deducdate,'%m/%d/%Y'),
        timein=if(str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@timein,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@timein,'%m/%d/%Y %H:%i:%s'))";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['orderdate', 'deducdate', 'timein'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }
    }

    public function doReturnallowance($when)
    {
        $table = "sophio_fbs.wws_returnallowance";
        $type = 'wws_returnallowance';
        $template_name = $this->getExportMe($when, $type, 'invdate');
        if ($template_name === 'index.wws') {
            echo "FAILED to retrieve the file!\n";
            return false;
        }
        $this->updateReturnallowancFile($table, '/tmp/' . $template_name);


    }

    public function updateReturnallowancFile($table, $template_name)
    {
        $query = " load data local infile '" . $template_name . "' replace into table " . $table . "   COLUMNS TERMINATED BY '\t' ESCAPED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES  
        (pk,@invdate,invpk,custtype,profilepk,supstmntid,@deducdate,invtotal,amount,sku) 
        set invdate=if(str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p') IS NOT NULL, str_to_date(@invdate,'%m/%d/%Y %h:%i:%s %p'),str_to_date(@invdate,'%m/%d/%Y %H:%i:%s')),deducdate=str_to_date(@deducdate,'%m/%d/%Y')";
        echo $query . "\n";
        $result = DB::affectingStatement($query);
        var_dump($result);
        $dates = ['invdate', 'deducdate'];

        foreach ($dates as $date) {
            DB::affectingStatement("UPDATE $table SET `" . $date . "`=NULL WHERE `" . $date . "`='0000-00-00'");
        }

    }

    protected function getExportMe($when, $type, $datefield = 'timestamp')
    {
        $arrContextOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        if (Str::contains($when, '|')) {
            $dates = explode('|', $when);
            $url = 'https://fbs.sophio.com/exportme.wws?type=tab&startdate=' . $dates[0] . '&enddate=' . $dates[1] . '&table=' . $type . '&datefield=' . $datefield . '&istask=.t.';
        } else {
            $url = 'https://fbs.sophio.com/exportme.wws?type=tab&when=' . $when . '&table=' . $type . '&datefield=' . $datefield . '&istask=.t.';
        }
        echo "Execute " . $url . "\n";
        $file = file_get_contents($url, false, stream_context_create($arrContextOptions));
        print_r($file);
        preg_match("<a href=\x22(.+?)\x22>", $file, $matches);
        $filename = explode('/', $matches[1]);
        $template_name = $filename[count($filename) - 1] . '.' . $datefield;
        // exec('rm -rf /tmp/' . $template_name);
        echo "\ncd /tmp;wget -q " . $matches[1] . "-O $template_name\n";
        exec("cd /tmp;wget -q  --no-check-certificate  " . $matches[1] . " -O $template_name");
        return $template_name;
    }

}