<?php

namespace App\Library\Sophio\Actions\Common;

use Laravel\Horizon\Contracts\JobRepository;

class JobsByType
{
    /**
     * @param string $type
     * @return mixed
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function __invoke($type)
    {
        $jobRepository = app()->make(JobRepository::class);
        return $jobRepository
            ->$type(-1)
            ->map(function ($job) {
                $job->payload = json_decode($job->payload);
                return $job;
            })
            ->values();
    }
}