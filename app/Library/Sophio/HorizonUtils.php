<?php

namespace App\Library\Sophio;

use Illuminate\Support\Facades\Http;

class HorizonUtils
{
    public static function getJobByName($name, $url)
    {
        $pending = Http::get('https://' . $url . '/horizon/api/jobs/pending?secret=toldya');
        $jobs = [];
        if ($pending && isset($pending->json()['jobs']))
            foreach ($pending->json()['jobs'] as $b) {

                if ($b['name'] == $name) {

                    $jobs[] = $b;

                }
            }
        $completed = Http::get('https://' . $url . '/horizon/api/jobs/completed?secret=toldya');
        if ($completed && isset($completed->json()['jobs']))
            foreach ($completed->json()['jobs'] as $b) {

                if ($b['name'] == $name) {

                    $jobs[] = $b;

                }
            }
        $failed = Http::get('https://' . $url . '/horizon/api/jobs/failed?secret=toldya');
        if ($failed && isset($failed->json()['jobs']))
            foreach ($failed->json()['jobs'] as $b) {

                if ($b['name'] == $name) {

                    $jobs[] = $b;

                }
            }
        return $jobs;
    }
    public static function getBatchJobById($id, $url)
    {
        $lastbatch = null;
        $batches = Http::get('https://' . $url . '/horizon/api/batches?secret=toldya');
        if ($batches && isset($batches->json()['batches']))
            foreach ($batches->json()['batches'] as $b) {

                if ($b['id'] == $id) {
                    $lastbatch = $b;
                    break;
                }
            }
        return $lastbatch;
    }
    public static function getBatchJobByName($name, $url)
    {
        $lastbatch = null;
        $batches = Http::get('https://' . $url . '/horizon/api/batches?secret=toldya');
        if ($batches && isset($batches->json()['batches']))
            foreach ($batches->json()['batches'] as $b) {

                if ($b['name'] == $name) {
                    $lastbatch = $b;
                    break;
                }
            }
        return $lastbatch;
    }
}