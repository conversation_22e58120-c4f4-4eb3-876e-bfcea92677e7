<?php

namespace App\Library\Sophio\Jobs\Tractor;

use App\Library\Sophio\Products;
use App\Library\Sophio\Exporters\TractorExporter;
use App\Mail\QueueJobGeneral;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;

class BatchExportSkuForm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $settings;
    public $timeout = 0;
    public $tries = 1;

    public function __construct($settings)
    {
        $this->settings = $settings;
    $this->onQueue('longtasks');
    }

    public function handle()
    {

        config(['logging.default'=>'tractor']);
        $nrbatches = 1;
        if ($this->settings['maxlimit'] == 0) {
            $this->settings['maxlimit'] = Products::countForTractor();
        }
        if ($this->settings['perfile'] != 0) {

            $nrbatches = (int) floor($this->settings['maxlimit'] / $this->settings['perfile']);
        }else{
            $this->settings['perfile'] = $this->settings['maxlimit'];
        }
        \Log::error([$this->settings,$nrbatches]);


        $batch = Bus::batch([])
            ->allowFailures()
            ->then(function () {
            })
            ->finally(function () {
                Mail::to(config('sophio.admin.mail_senders.exporters'))->queue( new QueueJobGeneral([
                    'subject' => 'Tractor Batch Export   finished!',
                    'job_name' => $this->tags()[0],
                    'description' => 'result can be seen at '.env('APP_URL').'/admin/tractor/index'
                ]));
             })->name('batchexportskuform')->onQueue('longtasks')->dispatch();

        $i = 0;
        while($i<$nrbatches)
        {
            $export = new ExportSkuForm(['offset'=>$i*$this->settings['perfile'],'limit'=>$this->settings['perfile']]);
            $batch->add($export);
            $i++;
        }
        return $batch->id;
    }

    public function tags()
    {
        return ['batchexportskuform'];
    }
}