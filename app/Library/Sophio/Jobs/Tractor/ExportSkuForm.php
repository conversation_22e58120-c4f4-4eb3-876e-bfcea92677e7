<?php

namespace App\Library\Sophio\Jobs\Tractor;

use App\Library\Sophio\Exporters\TractorExporter;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

class ExportSkuForm implements ShouldQueue,ShouldBeUnique
{
    use   Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $settings;
    public $timeout = 0;
    public $tries = 1;
    public function __construct($settings)
    {

        $this->settings = $settings;
        $this->onQueue('longtasks');
    }

    public function handle()
    {
        config(['logging.default'=>'tractor']);
        $exporter = new TractorExporter();
        $exporter->exportNew($this->settings);
    }
    public function tags()
    {
        return ['tractorexportskuform'];
    }
    public function failed(\Exception $ex)
    {
        \Log::error("tractorexportskuform failed with ".$ex->getMessage());
        $this->delete();
    }
}