<?php

namespace App\Library\Sophio\BarcodeLookup;

use App\Models\Barcode;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class Manager
{
    protected $client;
    public function __construct()
    {
        $this->client= new APIClient();
    }
    public function NeedsContent()
    {
        $ncs = DB::table('nonapps_aces.needs_content')->where('attempted',0)->where('identifier','<>','')->get();

        foreach ($ncs as $nc) {
            echo $nc->identifier."\n";
            $rep= $this->client->products(['barcode'=>$nc->identifier]);
            if(is_array($rep) && isset($rep['error'])){
                DB::table('nonapps_aces.needs_content')->where('id',$nc->id)->update([
                    'attempted'=>1,
                    'barcode_response'=> json_encode($rep,true),
                    'barcode_found'=>0,
                    'updated_at'=>Carbon::now()
                ]);
                sleep(2);
                continue;
            }
            $found =null;
            if(is_array($rep) && isset($rep['products'])){
                foreach($rep['products'] as $product){
                    if((int)$product['barcode_number'] == (int)$nc->identifier){
                        $found = $product;
                    }
                }
            }
            if($found!==null){
                DB::table('nonapps_aces.needs_content')->where('id',$nc->id)->update([
                    'attempted'=>1,
                    'barcode_response'=>$this->client->getRawResponse(),
                    'barcode_found'=>1,
                    'data'=>json_encode($found,true),
                    'updated_at'=>Carbon::now()
                ]);
                $bc = Barcode::firstOrNew(['barcode_number'=>$nc->identifier]);
                $bc->aaiabrandid=$nc->mfg_code;
                $bc->part_number_unformatted=  $nc->part_number_unformatted;
                foreach($found as $key => $value){
                    $bc->$key=$value;
                }
                $bc->save();
                $ic = new \App\Library\Sophio\ImageCrawlResizer();
                $ic->resize = false;
                try {
                    foreach($found['images'] as $image){
                        $imgparts = array_values(array_filter(explode('/', $image)));
                        $newimg = [
                            'image_url' => $image,
                            'aaiabrandid' => $nc->mfg_code,
                            'part_number' => $nc->part_number_unformatted,
                            'part_number_unformatted' => $nc->part_number_unformatted,
                            'upc_int' => $nc->identifier,
                            'filename' => $imgparts[count($imgparts) - 1],
                            'original_path' => "original/" . $nc->mfg_code . "/" . $imgparts[count($imgparts) - 1],
                            'primary' => 1
                        ];
                        $img = $ic->addImage($newimg);
                    }

                } catch (\Throwable $th) {
                    print_r($newimg);
                    echo $th->getMessage()."\n";
                }
            }else{
                DB::table('nonapps_aces.needs_content')->where('id',$nc->id)->update([
                    'attempted'=>1,
                    'barcode_response'=> $this->client->getRawResponse(),
                    'barcode_found'=>0,
                    'updated_at'=>Carbon::now()
                ]);
            }
            sleep(2);
        }
    }
}