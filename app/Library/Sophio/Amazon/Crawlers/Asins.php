<?php

namespace App\Library\Sophio\Amazon\Crawlers;

use App\Exceptions\ExceptionMailAction;
use App\Library\Sophio\Amazon\AsinManager;
use App\Library\Sophio\Amazon\CompetitiveProductPricing;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Actions\SystemLogUpdate;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Models\B2CCentral\AmazonCompetittivePriceHistory;
use Sophio\Common\Models\FBS\PartsharePricing;
use Sophio\Common\Models\PIM\Asin;
use Sophio\Common\Models\SystemLog;

class Asins
{
    protected $marketplaceId;
    protected $configuration;
    protected $sleep;
    protected $should_sleep = true;
    protected $asins = 0;
    protected $systemlog_id;

    public function __construct($marketplaceId = 'ATVPDKIKX0DER', $configuration = null, $sleep = 5)
    {
        $this->marketplaceId = $marketplaceId;
        $this->configuration = $configuration;

        $this->sleep = $sleep;
    }

    public function __invoke($database, $items,$force=false)
    {
        echo count($items) . ' found' . "\n";
        $system_log = SystemLog::create(['task' => 'amazn_asinprices', 'message' => 'Start amzn asin  crawl for ' . $database . ' at ' . Carbon::now(), 'user_id' => 0, 'status' => SystemLogStatus::NEW]);
        $this->systemlog_id = $system_log->id;
        $products = [];
        $i = 0;
        foreach ($items as $item) {
            if ($item->ItemLevelGTIN !== "" && $item->ItemLevelGTIN !== null) {
                $products[] = [
                    'mfg_code' => $item->AAIABrandID,
                    'part_number' => $item->PartNumber,
                    'part_number_unformatted' => unformatString($item->PartNumber),
                    'identifier' => $item->ItemLevelGTIN,

                ];

            }
            if (count($products) == 10) {
                $previous_asins = $this->asins;
                $run = false;
                try {
                    $run = $this->doProducts($products,$force);
                } catch (\Throwable $e) {
                    print_r($e->getMessage());
                    Log::channel('amazon')->error($e->getMessage());
                    (new ExceptionMailAction())($e);
                    throw $e;
                }

                $products = [];
                if ($this->should_sleep)
                    sleep($this->sleep);

            }

            $i++;

        }
        (new SystemLogUpdate())($this->systemlog_id, 'ASINs found : ' . $this->asins . ' out of ' . $i . ' items', SystemLogStatus::INPROGRESS);
        (new SystemLogUpdate())($this->systemlog_id, 'Finished at ' . Carbon::now(), SystemLogStatus::SUCCESS);
    }

    function doProducts($products,$force)
    {

        echo "Asins:DoProducts for " . implode(' , ', array_map(function ($e) {
                return $e['mfg_code'] . ' ' . $e['part_number'];
            }, $products)) . "\n";
        $asinManager = new AsinManager($this->configuration, $this->marketplaceId);
        $products = $asinManager->getAsinsForProducts($products,$force);
        if ($asinManager->executed == false) {
            echo "Nothing to do, we already have the asins\n";
            $this->should_sleep = false;
        } else {
            $this->should_sleep = true;
        }
        $ai = new Image();
        $asins_to_go = [];
        foreach ($products as $product) {
            if (isset($product['asin']) && $product['asin'] !== "") {
                if(isset($product['existing']) && $product['existing'] == true) {
                    continue;
                }
                $asins_to_go[] = $product['asin'];
                echo "get image for " . $product['asin'];
                $aa =Asin::where('asin', $product['asin'])->first();
                $ai->getImages($aa);
            }

        }
        if (count($asins_to_go) == 0) {
            return false;
        } else {
            $this->asins += count($asins_to_go);
        }

        return true;
    }
}
