<?php

namespace App\Library\Sophio\Amazon\Crawlers;

use App\Exceptions\ExceptionMailAction;

class Image
{
    public function getImages($asin)
    {
        $data = json_decode($asin->data, true);
        $mains = [];
        if (isset($data['images']) && is_array($data['images'])) {
            foreach ($data['images'] as $images) {
                foreach ($images['images'] as $image) {
                    if(!isset($mains[$image['variant']])) {
                        $mains[$image['variant']] = $image;
                    }
                    if(   $mains[$image['variant']]['width']<$image['width'] ) {
                        $mains[$image['variant']] = $image;
                    }
                }
            }
        }
        foreach($mains as $image) {
            $ic = new \App\Library\Sophio\ImageCrawlResizer();
            $ic->resize = false;
            $imgparts = array_values(array_filter(explode('/', $image['link'])));
            try {
                $newimg = [
                    'image_url' => $image['link'],
                    'aaiabrandid' => $asin->mfg_code,
                    'part_number' => $asin->part_number_unformatted,
                    'part_number_unformatted' => $asin->part_number_unformatted,
                    'upc_int' => $asin->identifier,
                    'filename' => $imgparts[count($imgparts) - 1],
                    'original_path' => "original/" . $asin->mfg_code . "/" . $imgparts[count($imgparts) - 1],
                    'primary' => ($image['variant']==='MAIN' || count($mains)==1)?1:0,
                    'source'=>'AMZ',
                    'role'=>$image['variant']

                ];
                $img = $ic->addImage($newimg);
            } catch (\Throwable $th) {

                echo $th->getMessage() . "\n";
                (new ExceptionMailAction())($th, ['newimge' => $newimg]);
            }
        }
    }

    public function getImageByAsin($asin)
    {
        echo $asin->part_number . " " . $asin->asin . "\n";
        $data = json_decode($asin->data, true);

        if (isset($data['images']) && is_array($data['images'])) {
            $main = [];
            $imgs = [];
            foreach ($data['images'] as $images) {
                foreach ($images['images'] as $image) {
                    if (!isset($imgs['width'])) {
                        $imgs = $image;
                    } elseif ($imgs['width'] < $image['width']) {
                        $imgs = $image;
                    }
                    if ($image['variant'] === 'MAIN') {
                        if (!isset($main['width'])) {
                            $main = $image;
                        } elseif ($main['width'] < $image['width']) {
                            $main = $image;
                        }
                    }
                }
            }
            if (!isset($main['width'])) {
                $main = $imgs;
            }
            if (isset($main['width'])) {
                $ic = new \App\Library\Sophio\ImageCrawlResizer();
                $ic->resize = false;
                $imgparts = array_values(array_filter(explode('/', $main['link'])));
                try {
                    $newimg = [
                        'image_url' => $main['link'],
                        'aaiabrandid' => $asin->mfg_code,
                        'part_number' => $asin->part_number_unformatted,
                        'part_number_unformatted' => $asin->part_number_unformatted,
                        'upc_int' => $asin->identifier,
                        'filename' => $imgparts[count($imgparts) - 1],
                        'original_path' => "original/" . $asin->mfg_code . "/" . $imgparts[count($imgparts) - 1],
                        'primary' => 1
                    ];
                    $img = $ic->addImage($newimg);
                } catch (\Throwable $th) {
                    print_r($newimg);
                    echo $th->getMessage() . "\n";
                    (new ExceptionMailAction())($th, ['newimge' => $newimg]);
                }

            }
        }
    }
}