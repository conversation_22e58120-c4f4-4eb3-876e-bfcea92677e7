<?php

namespace App\Library\Sophio\Exporters;

use OpenSpout\Writer\Common\Creator\WriterFactory;


class EmptyCSVFastExportTemplate extends FastExportTemplate
{
    protected $writer_class = \OpenSpout\Common\Type::CSV;
    protected $export_dir;
    public function setTabDelimiter()
    {
        $this->writer->setFieldDelimiter("\t");
    }
    public function open()
    {
        $this->writer = WriterFactory::createFromType($this->writer_class);
        $this->writer->setShouldAddBOM(false);
        $this->writer->openToFile($this->newfile);
    }
}