<?php

namespace App\Library\Sophio\Exporters;

use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use OpenSpout\Reader\Common\Creator\ReaderFactory;
use OpenSpout\Writer\Common\Creator\WriterEntityFactory;
use OpenSpout\Writer\Common\Creator\WriterFactory;


class FastExportTemplate extends ExportTemplate
{
    protected $writer_class = \OpenSpout\Common\Type::XLSX;

    public function setWriterCSV()
    {
        $this->writer_class = \OpenSpout\Common\Type::CSV;
    }

    public function setHeaders($array = [])
    {
        $this->writer->addRow(WriterEntityFactory::createRowFromArray($array));
    }

    public function open_read()
    {
        $this->reader = ReaderEntityFactory::createReaderFromFile($this->newfile);
        $this->reader->setShouldFormatDates(true);
        $this->reader->open($this->newfile);
    }

    public function getReader()
    {
        return $this->reader;
    }

    public function open()
    {
        $this->writer = WriterFactory::createFromType($this->writer_class);
        if($this->writer_class===\OpenSpout\Common\Type::CSV)
        {
            $this->writer->setShouldAddBOM(false);
        }

        $this->writer->openToFile($this->newfile);

        if ($this->template != null) {
            $this->reader = ReaderEntityFactory::createReaderFromFile($this->template);
            $this->reader->setShouldFormatDates($this->shouldFormatDates);
            $this->reader->open($this->template);
            foreach ($this->reader->getSheetIterator() as $sheetIndex => $sheet) {
                if ($sheetIndex !== 1) {
                    $this->writer->addNewSheetAndMakeItCurrent();
                }
                foreach ($sheet->getRowIterator() as $row) {
                    if ($this->rowno < $this->start_from) {


                        $this->writer->addRow($row);
                        $this->rowno++;
                        continue;
                    } else {
                        $this->sheet = $sheet;
                        break;
                    }
                }
                break;
            }
        }
    }

    public function getWrite()
    {
        return $this->writer;
    }

    public function addRow($outputArr)
    {
        $this->writer->addRow(WriterEntityFactory::createRowFromArray($outputArr));
        $this->rowno++;
    }

    public function close()
    {
        if ($this->template != null) {
            $this->reader->close();
        }
        $this->writer->close();
    }
}