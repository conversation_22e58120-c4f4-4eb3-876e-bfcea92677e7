<?php

namespace App\Library\Sophio\Exporters;

use App\Library\Sophio\Products;
use App\Library\Sophio\PartGrabberByProduct;
use App\Library\Sophio\PartGrabberByProductFeed;
use Sophio\Common\Models\WHIACES\ProductFeed;
use Sophio\Common\Models\PIM\Product;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class TractorExporter
{
    protected $template;
    public function __construct()
    {
        \Config::set('force_db', 'sophio_fbs');
    }

    public function updateContentDigitalAssetFeed()
    {
        $this->template = new TractorDigitalContentTemplate('tractor_digital_content_skus_readyV5.xlsx');

        $products = new Products();
        foreach ($this->template->open() as $numrow => $existing_row) {
            if ($numrow < 5) {
                continue;
            }
            $array_row = $existing_row;
            if (!isset($array_row[9])) {
                break;
            }
            $tractorid = $array_row[0];

            $brand_mfg_part_number =
                [
                    substr($array_row[9], 0, 4),
                    substr($array_row[9], 5, 3),
                    substr($array_row[9], 9),
                ];
            $aaiabrandid = $brand_mfg_part_number[0];
            $mfg_code = $brand_mfg_part_number[1];
            $part_number = $brand_mfg_part_number[2];
            $part_number_unformatted = unformatString($part_number);
            try {
                $product = Product::where('mfg_code', '=', $mfg_code)->where('part_number_unformatted', '=', $part_number_unformatted)->firstorfail();
            } catch (ModelNotFoundException $e) {
                echo "updating tractorid " . $tractorid . " for " . $mfg_code . " " . $part_number_unformatted . " $part_number \n";
                $pfs = ProductFeed::where('mfg_code', '=', $mfg_code)->where('part_number_unformatted', '=', $part_number_unformatted);
                $pfs->update(['tractorid' => $tractorid]);
                foreach ($pfs as $pf) {
                    $products->refreshProduct($pf);
                }


            }

            if ($product) {
                /*
                $imgparts = explode('/', $product->image_url);
                if($product->image_source!='FBS' && $product->image_url!="") {
                    echo "processing image ".$product->image_url."\n";

                    $resized = $sizer->addImage([
                        'image_url' => $product->image_url,
                        'aaiabrandid' => $aaiabrandid,
                        'part_number' => $part_number_unformatted,
                        'upc_int' =>   ((int)$product->upc!=0)? (int)$product->upc:(int)$product->gtin,
                        'filename' => $imgparts[count($imgparts) - 1],
                        'original_path' => "original/".$aaiabrandid."/".$imgparts[count($imgparts) - 1]
                    ]);
                    if ($resized['resized'] == 1) {

                        $ext =  explode('.',$resized['original_path'] );

                        if(!Storage::disk("imagespool")->exists("scratch/imagetmpbundle/".$tractorid.".".end($ext))) {
                            echo "coyping ".$resized['original_path'] ." to "."scratch/imagetmpbundle/".$tractorid.".".end($ext)."\n" ;
                            Storage::disk("imagespool")->copy($resized['original_path'] ,"scratch/imagetmpbundle/".$tractorid.".".end($ext));
                        }
                        $product->image_url = "https://images-us1.sophio.com/".$resized['original_path'];
                        $product->image_source ="FBS";
                        $product->has_image=1;
                        $product->save();
                    }
                }else{
                    if($product->image_url!="") {
                        $resized = $sizer->addImage([
                            'image_url' => $product->image_url,
                            'aaiabrandid' => $aaiabrandid,
                            'part_number' => $part_number_unformatted,
                            'upc_int' =>   ((int)$product->upc!=0)? (int)$product->upc:(int)$product->gtin,
                            'filename' => $imgparts[count($imgparts) - 1],
                            'original_path' => "original/".$aaiabrandid."/".$imgparts[count($imgparts) - 1]
                        ]);
                        if ($resized['resized'] == 1) {
                            $ext = explode('.', $resized['original_path']);
                            if (!Storage::disk("imagespool")->exists("scratch/imagetmpbundle/" . $tractorid . ".jpeg")) {
                                echo "coyping " . $resized['original_path'] . " to " . "scratch/imagetmpbundle/" . $tractorid . "." . end($ext) . "\n";
                                Storage::disk("imagespool")->copy($resized['original_path'], "scratch/imagetmpbundle/" . $tractorid . ".jpeg");
                            }
                        }
                    }

                }*/
                //$template->addRow($this->updateRow($array_row,$product));
                //Excel starts rows count from 1, not 0 (like PHP), so we need to offset
                $this->updateRowDigitalContent($numrow + 1, $array_row, $product);
            } else {
                //  $template->addRow($array_row);
            }


        }
        $this->template->close();
    }

    public function updateRowDigitalContent($numrow, $row, $product)
    {
        echo " $product->mfg_code  $product->part_number_unformatted \n";
        $description = "";
        $productfeed = $product->productfeed[0];
        if ($productfeed->partshareprice) {
            $psp = \DB::select("select  tractorsupply.my_price_process (?,?,?,?) as o_nsellprice", [203089, $productfeed->partshareprice->linecode, (float)$productfeed->partshareprice->itemaltaffiliate, $productfeed->part_number_unformatted]);
            $this->template->editcell($numrow, 'H', '$' . (float)number_format($psp[0]->o_nsellprice, 2, '.', ''));
        }

        $this->template->editcell($numrow, 'M', $product->long_description);
        //$row[13] ="N/A";
        $this->template->editcell($numrow, 'N', "N/A");
        $this->template->editcell($numrow, 'K', (isset($productfeed->aaiabrand->BrandName) ? $productfeed->aaiabrand->BrandName : $product->mfg_code));
        if ($product->width != null) {
            $this->template->editcell($numrow, 'R', $product->height);
            $this->template->editcell($numrow, 'S', $product->length);
            $this->template->editcell($numrow, 'T', $product->weight);
            $this->template->editcell($numrow, 'U', $product->width);
        }
        $O = "This " . (isset($productfeed->aaiabrand->BrandName) ? $productfeed->aaiabrand->BrandName : $product->mfg_code) . " replacement part is designed to help restore and keep your vehicle running like new for many miles
    and years to come. It is manufactured in a state-of-the-art facility using the highest quality materials and workmanship.";
        $this->template->editcell($numrow, 'O', $O);
        if (isset($productfeed->seofitment->fitment_1) && $productfeed->seofitment->fitment_1 != "") {
            $seo = $productfeed->seofitment->fitment_1;
            if (isset($productfeed->seofitment->fitment_2) && $productfeed->seofitment->fitment_2 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_2;
            }
            if (isset($productfeed->seofitment->fitment_3) && $productfeed->seofitment->fitment_4 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_3;
            }
            if (isset($productfeed->seofitment->fitment_4) && $productfeed->seofitment->fitment_4 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_4;
            }
            if (isset($productfeed->seofitment->fitment_5) && $productfeed->seofitment->fitment_5 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_5;
            }
            if (isset($productfeed->seofitment->fitment_6) && $productfeed->seofitment->fitment_6 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_6;
            }
            if (isset($productfeed->seofitment->fitment_7) && $productfeed->seofitment->fitment_7 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_7;
            }
            if (isset($productfeed->seofitment->fitment_8) && $productfeed->seofitment->fitment_8 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_8;
            }
            if (isset($productfeed->seofitment->fitment_9) && $productfeed->seofitment->fitment_9 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_9;
            }
            if (isset($productfeed->seofitment->fitment_10) && $productfeed->seofitment->fitment_10 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_10;
            }
            if (isset($productfeed->seofitment->fitment_11) && $productfeed->seofitment->fitment_11 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_11;
            }
            if (isset($productfeed->seofitment->fitment_12) && $productfeed->seofitment->fitment_12 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_12;
            }
            if (isset($productfeed->seofitment->fitment_13) && $productfeed->seofitment->fitment_13 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_13;
            }
            if (isset($productfeed->seofitment->fitment_14) && $productfeed->seofitment->fitment_14 != "") {
                $seo .= ", " . $productfeed->seofitment->fitment_14;
            }
            $this->template->editcell($numrow, 'P', $seo);
        }

        $this->template->editcell($numrow, 'Q', ($product->materials != null) ? $product->materials : '');
        // $row[26]='None';
        $this->template->editcell($numrow, 'AA', 'None');
        //$row[27] = 'None';
        //$row[28] = 'No';
        $this->template->editcell($numrow, 'AB', 'None');
        $this->template->editcell($numrow, 'AC', 'No');

        $cal65_found = false;
        if ($product->prop65require == 'Y') {
            $this->template->editcell($numrow, 'AD', 'Yes');
        } else {
            $this->template->editcell($numrow, 'AD', 'No');
        }

        $this->template->editcell($numrow, 'AE', 'No');
        $this->template->editcell($numrow, 'AG', $product->prop65text);
        $product->save();
        return $row;
    }

    public function getPartsGrabber()
    {
        $partsgrabber = new PartGrabberByProductFeed();
        $partsgrabber->requireNoCore();
        $partsgrabber->requireCost();
        $partsgrabber->noTractorId();
        $partsgrabber->setMarket('TRA');
        $partsgrabber->requireQtyAvailWarehouse();
        $partsgrabber->requireWarehouse();
        $partsgrabber->togglePartsharePricing(true);
        return $partsgrabber;
    }

    public function getProducts()
    {
        $products = new PartGrabberByProduct();
        $products->requireNoCore();
        $products->requireCost();
        $products->noTractor();
        $products->requireImageURL();
        $products->requireOneQtyOrder();
        $products->hasDimensions();
        $products->hasUpcOrGtin();
        $products->setMarket('TRA');
        $products->requireQtyAvailWarehouse();
        $products->hasCountryOfOrigin();
        $products->requireWarehouse();
        $products->togglePartsharePricing(true);
        return $products;
    }

    public function exportNew($settings)
    {

        $products = $this->getProducts();
        if ($settings['limit'] > 0) {
            $products->setLimit($settings['limit']);
        }
        if ($settings['offset'] > 0) {
            $products->setOffset($settings['offset']);
        }

        $npitemplate = new TractorSkuFormTemplate('new_sku_sophio_' . date("Y-m-d") . '_' . $settings['offset'] . '_' . $settings['limit'] . '.xlsx');
        $npitemplate->open();
        $products->execute_callback(function ($p) use ($npitemplate,$settings) {
            $npitemplate->addRow($this->createSkuFormRow($p,$settings));
        }
        );
        $npitemplate->close();
        return $npitemplate;
    }

    public function createSkuFormRow($product,$settings)
    {
        $productfeed = $product->productfeed[0];
        $psp = \DB::select("select  tractorsupply.my_price_process (?,?,?,?) as o_nsellprice", [203089, $productfeed->partshareprice->linecode, (float)$productfeed->partshareprice->itemaltaffiliate, $productfeed->part_number_unformatted]);
        $cost = (float)number_format($psp[0]->o_nsellprice, 2, '.', '');

        if (!isset($this->cache['aaiabrandid'][$productfeed->aaiabrandid])) {
            if (isset($productfeed->aaiabrand)) {
                $this->cache['aaiabrandid'][$productfeed->aaiabrandid] = [
                    'BrandName' => $productfeed->aaiabrand->BrandName,
                    'BrandOwner' => $productfeed->aaiabrand->BrandOwner,
                    'BrandID' => $productfeed->aaiabrand->BrandID,
                    'ParentCompany' => $productfeed->aaiabrand->ParentCompany
                ];

            }
        }

        return [
            '802080', //A - Vendor Number,
            $product->part_name, //B product name
            $this->cache['aaiabrandid'][$productfeed->aaiabrandid]['BrandName'] ?? $productfeed->mfg_code, // C Manufacturer Brand
            $this->cache['aaiabrandid'][$productfeed->aaiabrandid]['ParentCompany'] ?? $productfeed->mfg_code, // D Manufacturer
            $product->aaiabrandid . '-' . $product->mfg_code . '-' . $productfeed->part_number, //E Vendor Part
            $productfeed->part_number, // F Model
            ($product->upc != null ? substr($product->upc, 0, 12) : ($product->gtin != null ? $product->gtin : '')),  // G Selling Unit
            '', //H
            '', //I
            'None', //J
            '', //K
            '', //L
            '', // M Product Type
            (strlen($product->description) > 40 ? $product->product_name : $product->description), //N description
            $cost, //O cost
            2 * $cost, //O Vendor suggested retail
            (float)$product->partshareprice->map > 0 ? (float)number_format($product->partshareprice->map, 2, '.', '') : '',
            (float)$product->partshareprice->imap > 0 ? (float)number_format($product->partshareprice->imap, 2, '.', '') : '',
            (float)$product->partshareprice->imap > 0 ? 'STRICT_MAP' : '',
            $product->countryoforigin, //T
            '', //U shelf life
            'N', //V fragile
            $product->qty_uom,
            $product->weight,
            $product->length,
            $product->width,
            $product->height,
            $product->length, //AB length
            $product->width,
            $product->height,
            '',
            '',
            '',
            '',
            '',
            '', //AJ
            '',
            '',
            '',
            '',
            '',
            '', // AP
            '',
            '',
            '',
            '',
            '', //AU
            $product->prop65require == 'Y' ? 'Y' : 'N', //AV
            'N',
            'N',
            'N',
            'N/A', // AZ sale restrictions
            'N',
            'AK,HI,PR,VI,GU', //BB
            'N',
            '',
            '',
            '',
            'N', //BG test item
            'N',
            'N', //BI core charge
            'N',
            'N',
            'N',

        ];
    }
}