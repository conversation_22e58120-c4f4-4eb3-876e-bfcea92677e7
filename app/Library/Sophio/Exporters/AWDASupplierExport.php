<?php

namespace App\Library\Sophio\Exporters;

use App\Models\Website;
use App\Models\Wwsitem;
use chillerlan\QRCodeTest\Data\NumberTest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Sophio\Common\Models\AAIA\Brand;
use Sophio\Common\Models\AAIA\BrandMix;
use Sophio\Common\Models\B2CCentral\AmazonCompetittivePriceHistory;
use Sophio\Common\Models\Partshare\FlattenedData;

class AWDASupplierExport
{
    protected $settings = [];
    protected $export;
    protected $time;

    public function __construct($settings = [])
    {

        $this->settings = $settings;
        $this->time = time();
    }

    public function export()
    {
        $website = Website::find($this->settings['website_id']);
        $this->export = new AWDASupplierTemplate($website->metadata['catalog_filename'] . '-' . Carbon::today()->format('Y-m-d') . '.xlsx', 'fbs');
        $this->export->open();
        $this->export->setCellValueExplicitByColumnAndRow(4,1,ucwords(str_replace('-',' ', $website->metadata['catalog_filename'])).' Catalog');
        $this->populate();
        $this->export->close();
    }
    public function exportByBrand($aaiabrandid)
    {
        $website = Website::find($this->settings['website_id']);
        $this->export = new AWDASupplierTemplate($website->metadata['catalog_filename'] . '-'.$aaiabrandid.'-' . Carbon::today()->format('Y-m-d') . '-partshare.xlsx', 'fbs');
        $this->export->open();
        $brand = BrandMix::where('brand_id',$aaiabrandid)->first();
        $this->export->setCellValueExplicitByColumnAndRow(4,1,$brand->BrandName .' Catalog');
        $this->populateByBrand($aaiabrandid);
        $this->export->close();
    }

    public function getExport()
    {
        return $this->export;
    }
    public function populateByBrand($aaiabrandid)
    {
        $d = FlattenedData::where('AAIABrandID',$aaiabrandid)->get();



        foreach ($d as $key => $partshare) {
            $images = $partshare->product->images;

            $array = [
                $partshare->LineCode,  //Network Line Code
                Carbon::parse($partshare->ItemAffiliateEffectiveDate)->format('m/d/y'),  //Cost Effective Date
                Carbon::parse($partshare->ItemAffiliateEffectiveDate)->format('m/d/y'), //Sell Prices Effective
                Carbon::parse($partshare->DateLastModified)->format('m/d/y'), //Date Modified

                $partshare->ItemGTIN,  //Item Level GTIN
                $partshare->ItemBarCode, //Item UPC or EAN
                $partshare->PartNumber, //Part Number
                $partshare->ShortDescription  ,  //Short Description
                $partshare->ReplacementPart, //Replacement part number
                $partshare->SupplierPopularityCode, //Mfc Pop Code
                $partshare->ItemPkgType != "" ? $partshare->ItemPkgType : 'BX', //Item Package Type
                $partshare->UnitsPerItem, //Item Package Qty
                $partshare->ItemUOM, //Item Quantity UOM
                $partshare->MinOrderQty, //Minimum Order Quantity
                number_format($partshare->ItemList, 2, '.', ''),  //Suggested List
                number_format($partshare->ItemRetail, 2, '.', ''),  //Suggested Retail
                number_format($partshare->ItemCOMMER, 2, '.', ''),  //Online Retail
                number_format($partshare->ItemWHSALE, 2, '.', ''),  //Suggested Wholesale
                number_format($partshare->ItemAffiliate, 2, '.', ''),  //Network Affiliate
                number_format($partshare->ItemAltAffiliate, 2, '.', ''),  //Online Wholesale
                number_format($partshare->AffilateCore, 2, '.', ''),  //Affiliate Core
                number_format($partshare->LineItemGoldMemberInvoice, 2, '.', ''),  //Line Item Invoice Price
                number_format($partshare->LineItemGoldMemberInvoice, 2, '.', ''), //Net  Invoice Cost
                number_format($partshare->GoldMemberInvoiceCore, 2, '.', ''),//Line Item Invoice Core Price
                number_format($partshare->GoldMemberInvoiceCore, 2, '.', ''),  //Net Invoice Core Cost
                $partshare->EachPackageGTIN, // Each Package Level GTIN
                $partshare->EachPackageBarCode, //Each Package Bar Code Characters
                $partshare->EachPackageInnerQuantity, //Each Package Inner Quantity
                $partshare->EachPackageInnerQuantityUOM, //Each Package Inner Quantity UOM
                $partshare->ItemPkgType, //Each Package Type
                number_format($partshare->ItemHeight, 2, '.', ''), //Each Height
                number_format($partshare->ItemWidth, 2, '.', ''), //Each Width
                number_format($partshare->ItemDepth, 2, '.', ''),  //Each Length
                number_format($partshare->ItemWeight, 2, '.', ''), //Each Weight
                $partshare->InnerPackageUPC, //Inner Pack Level GTIN
                $partshare->InnerPackageBarCode, //Inner Package Bar Code Characters
                $partshare->EachPackageInnerQuantityUOM, //Inner Pack Type
                $partshare->ItemsPerInnerPack, //Inner Pack Quantity of Eaches In Package

                number_format($partshare->InnerPackHeight, 2, '.', ''), //Inner Pack Height
                number_format($partshare->InnerPackWidth, 2, '.', ''), //Inner Pack Width
                number_format($partshare->InnerPackDepth, 2, '.', ''), //Inner Pack Length
                number_format($partshare->InnerPackWeight, 2, '.', ''), //Inner Pack Weight
                $partshare->CaseUPC, //Case Package Level GTIN
                $partshare->CaseBarCode, //Case Package Bar Code Characters
                $partshare->ItemsPerCase, //Quantity of Eaches in Case
                number_format($partshare->CaseHeight, 2, '.', ''),  //Case Height
                number_format($partshare->CaseWidth, 2, '.', ''), //Case Width
                number_format($partshare->CaseDepth, 2, '.', ''), //Case Length
                number_format($partshare->CaseWeight, 2, '.', ''), //Case Weight
                $partshare->PalletUPC,  //Pallet Package Level GTIN
                $partshare->ItemsPerPallet,  //Pallet Quantity of Eaches in Package
                $partshare->WeightUOM,  //UOM for Weight
                $partshare->DimensionUOM,  //Dimension UOM
                $partshare->NetworkItemDemand, //Network Item Demand
                $partshare->PerCartonQty, //Per Car Qty
                $partshare->USVioCount, //U.S. VIO Count
                Carbon::parse( $partshare->VIOReportDate)->format('m/d/y') , //VIO Report Date
                $partshare->LifeCycleStatus, //life cycle status
                $partshare->MSDSRequired === 'Y' ?? 'N', //MSDS FLAG
                $partshare->MSDSSheetNumber, //MSDS SHEET ORDER NUMBER
                $partshare->MSDSLink,  //MSDS link

                $partshare->HazMatClassCode,  //Hazardous Materials Class Code
                $partshare->HazMatDescription,  //Hazardous Material Description
                $partshare->CountryOfOrigin, //Country of Origin (Primary)
                $partshare->LongDescription,  //Part Description – Long
                $partshare->PartType, //Part Type (AAIA)
                $partshare->MartketingDescription,  //Marketing Description (Long)
                $partshare->Applications,  //Applications
                $partshare->ProductCategoryCode, //ProductCategoryCode
                $partshare->ProductGroupCode, //ProductGroupCode
                $partshare->ProductSubGroupCode, //ProductSubGroupCode
                $partshare->CalifProp65Required,  //California Proposition 65 Required
                ($partshare->CalifProp65Required === 'Y' ? $partshare->CalifProp65 : ''),  //California Proposition 65 Text
                $partshare->CalifProp65Image, //California Proposition 65 Image
                $partshare->AAIABrandID, //AAIA Brand ID
            ];

            $this->export->addRow($array);
        }
    }
    public function populate()
    {
        $d = Wwsitem::where('contactpk', 201366)->get();


        foreach ($d as $key => $wwsitem) {
            if(!$wwsitem->partshare) {
                continue;
            }

            if($wwsitem->partshare->LineCode=="") {

                continue;
            }
            $images = $wwsitem->product->images??'';
            $onlineRetail = 0.0;
            if($wwsitem->product) {
                if($wwsitem->product->asin && $wwsitem->product->asin->asin!=="") {
                    $az = AmazonCompetittivePriceHistory::where('asin',$wwsitem->product->asin->asin)->orderBy('updated_at','desc')->limit(1)->first();
                    if($az){
                        $onlineRetail = $az->LandedPrice;
                    }else{
                        $onlineRetail = 0;
                    }

                }
            }
            $array = [
                $wwsitem->partshare->LineCode,  //Network Line Code
                Carbon::parse($wwsitem->partshare->ItemAffiliateEffectiveDate)->format('m/d/y'),  //Cost Effective Date
                Carbon::parse($wwsitem->partshare->ItemAffiliateEffectiveDate)->format('m/d/y'), //Sell Prices Effective
                Carbon::parse($wwsitem->partshare->DateLastModified)->format('m/d/y'), //Date Modified

                $wwsitem->partshare->ItemGTIN,  //Item Level GTIN
                $wwsitem->partshare->ItemBarCode, //Item UPC or EAN
                $wwsitem->partshare->PartNumber, //Part Number
                $wwsitem->partshare->ShortDescription  ,  //Short Description
                $wwsitem->partshare->ReplacementPart, //Replacement part number
                $wwsitem->partshare->SupplierPopularityCode, //Mfc Pop Code
                $wwsitem->partshare->ItemPkgType !== "" ? $wwsitem->partshare->ItemPkgType : 'BX', //Item Package Type
                $wwsitem->partshare->UnitsPerItem, //Item Package Qty
                $wwsitem->partshare->ItemUOM, //Item Quantity UOM
                $wwsitem->partshare->MinOrderQty, //Minimum Order Quantity
                number_format($wwsitem->partshare->ItemList, 2, '.', ''),  //Suggested List
                number_format($wwsitem->partshare->ItemRetail, 2, '.', ''),  //Suggested Retail
                number_format($wwsitem->list>0?$wwsitem->list:$wwsitem->partshare->ItemCOMMER, 2, '.', ''),  //Online Retail
                number_format($wwsitem->partshare->ItemWHSALE, 2, '.', ''),  //Suggested Wholesale
                number_format($wwsitem->partshare->ItemAffiliate, 2, '.', ''),  //Network Affiliate
                number_format($wwsitem->partshare->ItemAltAffiliate, 2, '.', ''),  //Online Wholesale
                number_format($wwsitem->partshare->AffilateCore, 2, '.', ''),  //Affiliate Core
                number_format( $wwsitem->cost>0?$wwsitem->cost:$wwsitem->partshare->LineItemGoldMemberInvoice, 2, '.', ''),  //Line Item Invoice Price
                number_format($wwsitem->cost>0?$wwsitem->cost:$wwsitem->partshare->LineItemGoldMemberInvoice, 2, '.', ''), //Net  Invoice Cost
                number_format($wwsitem->partshare->GoldMemberInvoiceCore, 2, '.', ''),//Line Item Invoice Core Price
                number_format($wwsitem->partshare->GoldMemberInvoiceCore, 2, '.', ''),  //Net Invoice Core Cost
                $wwsitem->partshare->EachPackageGTIN, // Each Package Level GTIN
                $wwsitem->partshare->EachPackageBarCode, //Each Package Bar Code Characters
                $wwsitem->partshare->EachPackageInnerQuantity, //Each Package Inner Quantity
                $wwsitem->partshare->EachPackageInnerQuantityUOM, //Each Package Inner Quantity UOM
                $wwsitem->partshare->ItemPkgType, //Each Package Type
                number_format($wwsitem->partshare->ItemHeight, 2, '.', ''), //Each Height
                number_format($wwsitem->partshare->ItemWidth, 2, '.', ''), //Each Width
                number_format($wwsitem->partshare->ItemDepth, 2, '.', ''),  //Each Length
                number_format($wwsitem->partshare->ItemWeight, 2, '.', ''), //Each Weight
                $wwsitem->partshare->InnerPackageUPC, //Inner Pack Level GTIN
                $wwsitem->partshare->InnerPackageBarCode, //Inner Package Bar Code Characters
                $wwsitem->partshare->EachPackageInnerQuantityUOM, //Inner Pack Type
                $wwsitem->partshare->ItemsPerInnerPack, //Inner Pack Quantity of Eaches In Package

                number_format($wwsitem->partshare->InnerPackHeight, 2, '.', ''), //Inner Pack Height
                number_format($wwsitem->partshare->InnerPackWidth, 2, '.', ''), //Inner Pack Width
                number_format($wwsitem->partshare->InnerPackDepth, 2, '.', ''), //Inner Pack Length
                number_format($wwsitem->partshare->InnerPackWeight, 2, '.', ''), //Inner Pack Weight
                $wwsitem->partshare->CaseUPC, //Case Package Level GTIN
                $wwsitem->partshare->CaseBarCode, //Case Package Bar Code Characters
                $wwsitem->partshare->ItemsPerCase, //Quantity of Eaches in Case
                number_format($wwsitem->partshare->CaseHeight, 2, '.', ''),  //Case Height
                number_format($wwsitem->partshare->CaseWidth, 2, '.', ''), //Case Width
                number_format($wwsitem->partshare->CaseDepth, 2, '.', ''), //Case Length
                number_format($wwsitem->partshare->CaseWeight, 2, '.', ''), //Case Weight
                $wwsitem->partshare->PalletUPC,  //Pallet Package Level GTIN
                $wwsitem->partshare->ItemsPerPallet,  //Pallet Quantity of Eaches in Package
                $wwsitem->partshare->WeightUOM,  //UOM for Weight
                $wwsitem->partshare->DimensionUOM,  //Dimension UOM
                $wwsitem->partshare->NetworkItemDemand, //Network Item Demand
                $wwsitem->partshare->PerCartonQty, //Per Car Qty
                $wwsitem->partshare->USVioCount, //U.S. VIO Count
                Carbon::parse( $wwsitem->partshare->VIOReportDate)->format('m/d/y') , //VIO Report Date
                $wwsitem->partshare->LifeCycleStatus, //life cycle status
                $wwsitem->partshare->MSDSRequired === 'Y' ?? 'N', //MSDS FLAG
                $wwsitem->partshare->MSDSSheetNumber, //MSDS SHEET ORDER NUMBER
                $wwsitem->partshare->MSDSLink,  //MSDS link

                $wwsitem->partshare->HazMatClassCode,  //Hazardous Materials Class Code
                $wwsitem->partshare->HazMatDescription,  //Hazardous Material Description
                $wwsitem->partshare->CountryOfOrigin, //Country of Origin (Primary)
                $wwsitem->partshare->LongDescription,  //Part Description – Long
                $wwsitem->partshare->PartType, //Part Type (AAIA)
                $wwsitem->partshare->MartketingDescription,  //Marketing Description (Long)
                $wwsitem->partshare->Applications,  //Applications
                $wwsitem->partshare->ProductCategoryCode, //ProductCategoryCode
                $wwsitem->partshare->ProductGroupCode, //ProductGroupCode
                $wwsitem->partshare->ProductSubGroupCode, //ProductSubGroupCode
                $wwsitem->partshare->CalifProp65Required,  //California Proposition 65 Required
                ($wwsitem->partshare->CalifProp65Required === 'Y' ? $wwsitem->partshare->CalifProp65 : ''),  //California Proposition 65 Text
                $wwsitem->partshare->CalifProp65Image, //California Proposition 65 Image
                $wwsitem->partshare->AAIABrandID, //AAIA Brand ID
                number_format(   $onlineRetail, 2, '.', '')
            ];

            $this->export->addRow($array);
        }
    }
}