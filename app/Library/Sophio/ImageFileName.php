<?php

namespace App\Library\Sophio;
use Backpack\CRUD\app\Library\Uploaders\Support\FileNameGenerator;
use Illuminate\Http\UploadedFile;
use Symfony\Component\HttpFoundation\File\File;

class ImageFileName extends FileNameGenerator
{
    public function getName(string|UploadedFile|File $file): string
    {

        if (is_object($file) && get_class($file) === File::class) {
            return $file->getClientOriginalName();
        }
        if(is_string($file)) {

            return $file;
        }
        return $file->getClientOriginalName();
    }
    private function getFileName(string|UploadedFile $file): string
    {
        dd($file);
    }
}