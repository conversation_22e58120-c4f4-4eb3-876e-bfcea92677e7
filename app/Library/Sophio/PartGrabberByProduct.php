<?php

namespace App\Library\Sophio;

use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\PIM\Product;
use Sophio\Common\Models\PIM\Part;
use Illuminate\Database\Eloquent\Builder;
use Sophio\Common\Services\IsoCodesService;

class PartGrabberByProduct  extends PartGrabber
{
    protected $product;
    protected $conditions = [];
    public function __construct()
    {
        $this->product = new Product();
    }
    public function getProductModel()
    {
        return $this->product;
    }
    public function getConditions()
    {
        return $this->conditions;
    }
    public function setMfgCode($mfgcode)
    {
        $this->mfgcode = $mfgcode;
        $this->conditions['mfgcode'] = $mfgcode;
        $this->product = $this->product->where('mfg_code', $mfgcode);
        return $this;
    }
    public function setPartNumberUnformatted($part_number_unformatted)
    {
        $this->product = $this->product->where('part_number_unformatted','=',$part_number_unformatted);
    }
    public function requireNoCore()
    {
        $this->conditions['nocore'] = true;
    }
    public function requireQtyAvailWarehouse()
    {
        $this->conditions['qtyavailwarehouse'] = true;
    }
    public function denyQtyAvailWarehouse()
    {
        $this->conditions['noqtyavailwarehouse'] = true;
        return $this;
    }
    public function requireQtyFeed()
    {
        $this->conditions['qtyfeed'] = true;
    }
    public function requireCost()
    {
        $this->conditions['cost']  = true;
    }
    public function requireImageURL()
    {
        $this->conditions['image_url'] = true;
        $this->product =  $this->product->whereNotNull('image_url')->where('image_url','!=','');
        return $this;
    }
    public function denyImageURL()
    {
        $this->product =  $this->product->where(function($query){$query->whereNull('image_url')->orWhere('image_url','=','');});
        return $this;
    }
    public function requireHostedImageUrl()
    {
        $this->conditions['image_url'] = true;
        $this->conditions['image_hosted'] = true;
        $this->product =  $this->product->whereNotNull('image_url')->where('image_url','!=','')->where('image_source','=','FBS');
        return $this;

    }
    public function denyHostedImageURL()
    {
        $this->product =  $this->product->whereNotNull('image_url')->where('image_url','!=','')->where('image_source','<>','FBS');
        return $this;
    }
    public function noZoroId()
    {
        $this->product = $this->product->whereHas('productfeed',function (Builder $query){
            $query->whereNull('zoroid');

        });
    }
    public function noWalmartId()
    {
        $this->product = $this->product->whereDoesntHave('marketplaceitem');


    }
    public function noWalmartFeedItem()
    {
        $this->product = $this->product->whereDoesntHave('walmartfeeditem');
    }
    public function requireAaiabrandid()
    {
        $this->conditions['aaiabrandid'] = true;
        $this->product = $this->product->whereNotNull('aaiabrandid');

    }
    public function requireEA()
    {
        $this->conditions['uom_ea'] = true;
        $this->product = $this->product->where('qty_uom','=','EA');
    }
    public function denyEA()
    {
        $this->conditions['uom_ea'] = true;
        $this->product = $this->product->where('qty_uom','!=','EA');
        return $this;
    }
    public function requireEABXPC()
    {
        $this->conditions['uom_eabxpc'] = true;
        $this->product = $this->product->whereIn('qty_uom',['EA','PC','BX']);
    }
    public function denyEABXPC()
    {
        $this->conditions['uom_eabxpc'] = true;
        $this->product = $this->product->whereNotIn('qty_uom',['EA','PC','BX']);
        return $this;
    }

    public function requireOneQtyOrder()
    {
        $this->conditions['min_order_qty'] = 1;
        $this->product = $this->product->where('min_order_qty','=',1);
    }
    public function requireLongDescription($length=1)
    {
        $this->product = $this->product->where('long_description','!=',"");
    }
    public function hasDimensions()
    {
        $this->conditions['has_dimensions'] = 1;
        $this->product = $this->product->where('has_dimensions','=',1);
    }
    public function nothasDimensions()
    {
        $this->conditions['has_dimensions'] = 0;
        $this->product = $this->product->where('has_dimensions','=',0);
        return $this;
    }

    public function hasSeoFitment()
    {
        $this->conditions['has_seo_fitment'] = 1;
        $this->product = $this->product->where('has_seo_fitment','=',1);
    }
    public function hasGtin()
    {
        $this->conditions['has_upc_or_gtin'] = 1;
        $this->product = $this->product->where('gtin','<>','');

        return $this;
    }
    public function hasUpcOrGtin()
    {
        $this->conditions['has_upc_or_gtin'] = 1;
        $this->product = $this->product->where(function($query){
            $query->whereNotNull('upc')->orWhereNotNull('gtin');

        });

        $this->product = $this->product->where(function($query){
            $query->where('upc','<>','')->orWhere('gtin','<>','');

        });

        return $this;
    }
    public function hasPackage()
    {
        $this->conditions['has_package'] = 1;
        $this->product = $this->product->whereHas('package');
        return $this;
    }
    public function nothasUpcOrGtin()
    {
        $this->conditions['has_upc_or_gtin'] = 1;
        $this->product = $this->product->whereNull('upc')->whereNull('gtin');
        return $this;
    }
    public function hasCountryOfOrigin()
    {
        $this->conditions['has_countryoforigin'] = 1;
        $this->product  = $this->product->whereNotNull('countryoforigin');
    }
    public function hasCountryOfOriginAllowed()
    {
        $this->conditions['has_countryoforiginallowed'] = 1;
        $this->product  = $this->product->whereNotIn('countryoforigin',['RU','PK']);
    }
    public function setContactpk($contactpk)
    {
        $this->conditions['contactpk'] = $contactpk;
    }
    public function setLimit($limit)
    {
        $this->limit =$limit;
        $this->product =  $this->product->limit($this->limit);
    }
    public function setOffset($offset)
    {
        $this->offset =$offset;
        $this->product =  $this->product->offset($this->offset);
    }
    public function setMarket($market)
    {
        $this->conditions['market'] = $market;
    }
    public function requirePtNum() {
        $this->conditions['pt_num'] = true;
    }
    public function setPartTypes($pt_nums) {
        $this->conditions['pt_nums'] =  $pt_nums;
    }
    public function requireProductFeed()
    {
        $this->product = $this->product->whereHas('productfeed',function(Builder $query) {
            $query->whereNull('part_status');
            if(isset($this->conditions['pt_num'])) {
                $query->whereNotNull('pt_num')->where('pt_num','!=',0);
            }
            if(isset($this->conditions['pt_nums'])) {
                $query->whereIn('pt_num', $this->conditions['pt_nums']);
            }
        });
    }
    public function requireWarehouse()
    {
        $conditions = $this->conditions;
        $this->product = $this->product->whereHas('productwarehouse', function (Builder $query) {

            $query->from = \Config::get('tenant_db').'.'.$query->from;
            if(isset($this->conditions['contactpk'])) {
                $query->where('contactpk',$this->conditions['contactpk']);
            }
            if(isset($this->conditions['market'])) {
                $query->whereRaw("contactpk in (SELECT contactpk from ".config('tenant_db').".marketplaces_suppliers WHERE market='" . $this->conditions['market'] . "' )");
            }
            if(isset($this->conditions['nocore']) && $this->conditions['nocore']===true) {
                $query->where('coreprice', 0);
            }
            if(isset($this->conditions['cost']) && $this->conditions['cost']===true) {
                $query->where('cost','>', 0);
            }
            if(isset($this->conditions['qtyavailwarehouse']) && $this->conditions['qtyavailwarehouse']===true) {
                $query->where('qty_avail','>', 0);
            }
            if(isset($this->conditions['noqtyavailwarehouse']) && $this->conditions['noqtyavailwarehouse']===true) {
                $query->where('qty_avail','=', 0);
            }
            if(isset($this->conditions['qtyfeed']) && $this->conditions['qtyfeed']===true) {
                $query->where('qty_feed','>', 0);
                $query->where('qty_avail','>', 5);
            }
        });
        return $this;
    }
    public function requireNoPartStatus()
    {
        $this->product =$this->product->whereNull('part_status');
    }
    public function noTractor()
    {
        $this->product = $this->product->whereHas('productfeed', function (Builder $query) {
            $query->whereNull('tractorid');
        });
    }
    public function requireBrandName()
    {
        $this->product =$this->product->whereHas('aaiabrand');
    }
    public function count()
    {
        return $this->product->count();
    }
    public function togglePartsharePricing($toogle)
    {
        if ($toogle === true) {
            $this->conditions['partshareprice'] = 'Has';
            $this->product = $this->product->whereHas('partshareprice');
        } else {
            $this->conditions['partshareprice'] = 'DoesntHave';
            $this->product = $this->product->whereDoesntHave('partshareprice');
        }
    }
    public function setCondition($f)
    {
        $this->product = $f($this->product);
    }
    public function execute_callback($callback)
    {

        foreach ($this->product->reorder()->orderByRaw('NULL')->cursor() as $p) {

            $callback($p);
        }
    }
    public function executeParts($callback) {

        foreach ($this->product->reorder()->orderByRaw('NULL')->cursor() as $p) {
            $callback($this->createPart($p));
        }
    }
    public function createPart(Product $p) {
        $part = new Part();
        $part->mfgcode = $p->mfg_code;
        $part->part_number_unformatted = $p->part_number_unformatted;
        try {

            list($part->gtin,$part->upc) = IsoCodesService::extractUpcGtin([$p->gtin,$p->upc]);
        }catch (\Exception $e) {
            Log::channel('walmart')->error(print_r($p,true));
            throw $e;
        }

        $part->aaiabrandid = $p->aaiabrandid;
        return $part;
    }
    public function execute()
    {

        foreach ($this->product->cursor() as $p) {

            $this->parts[$this->mfgcode . '-' . $p->part_number_unformatted] = $this->createPart($p);

        }
    }
}