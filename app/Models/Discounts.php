<?php

namespace App\Models;

use Sophio\Common\Models\MultiDbTenant;

/**
 * App\Models\Discounts
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Discounts newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Discounts newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Discounts query()
 * @method static \Illuminate\Database\Eloquent\Builder|MultiDbTenant filterDate($value)
 * @mixin \Eloquent
 */
class Discounts  extends MultiDbTenant
{
    use \Awobaz\Compoships\Compoships;
    protected $table = 'discounts';
    public $timestamps = false;
    protected $fillable=['linecode','linename','discount','effectiveDate','lastupdate','market'];
}