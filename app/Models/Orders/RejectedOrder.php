<?php

namespace App\Models\Orders;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Orders\OrderSupplierLog as SupplierLog;
use Sophio\Common\Models\MultiDbTenant;

/**
 * App\Models\Orders\RejectedOrder
 *
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Orders\LogOrder[] $logorder
 * @property-read int|null $logorder_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Orders\LogOrderLink[] $logorderlink
 * @property-read int|null $logorderlink_count
 * @property-read \App\Models\Orders\OrderSupplierLog|null $supplierlog
 * @method static Builder|RejectedOrder multiline()
 * @method static Builder|RejectedOrder newModelQuery()
 * @method static Builder|RejectedOrder newQuery()
 * @method static Builder|RejectedOrder query()
 * @property int $orderId
 * @property int|null $invoiceNumber
 * @property string|null $po_number
 * @property string $storepk
 * @property int $userId
 * @property int $clientId
 * @property string $orderBody
 * @property string $reason
 * @property string|null $message
 * @property int $supplierlog_id
 * @property string $dateModified
 * @property string $dateCreated
 * @property string|null $dateImported
 * @method static \Illuminate\Database\Eloquent\Builder|MultiDbTenant filterDate($value)
 * @mixin \Eloquent
 */
class RejectedOrder extends MultiDbTenant
{
    protected $table = 'rejected_orders';
    protected $primaryKey = 'orderId';
    public $timestamps = false;
    public function supplierlog()
    {
        return $this->belongsTo(SupplierLog::class,'supplierlog_id');
    }
    public function logorder()
    {
        return $this->hasManyThrough(LogOrder::class,SupplierLog::class, 'id','supplierlog_id','supplierlog_id');
    }
    public function logorderlink()
    {
        return $this->hasManyThrough(LogOrderLink::class,SupplierLog::class, 'id','supplierlog_id','supplierlog_id');
    }

    public function stockcheck()
    {
        return LogStock::where('po_number',$this->po_number)->orderby('created_at','desc')->get();
    }

    public function scopeMultiline($query)
    {
        return $query->select('rejected_orders.*')->join('wws_supplierlogs','rejected_orders.supplierlog_id','=','wws_supplierlogs.id')
            ->join('log_createorder','log_createorder.supplierlog_id','=','wws_supplierlogs.id')->groupby('wws_supplierlogs.id')->groupby('rejected_orders.orderId')->havingRaw('count(rejected_orders.orderId) >1');


    }
}