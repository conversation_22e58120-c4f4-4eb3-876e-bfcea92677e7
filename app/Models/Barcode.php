<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Barcode
 *
 * @property int $id
 * @property string $aaiabrandid
 * @property string $part_number_unformatted
 * @property int $barcode_number
 * @property string $barcode_formats
 * @property string $mpn
 * @property string $model
 * @property string $asin
 * @property string $title
 * @property string $category
 * @property string $manufacturer
 * @property string $brand
 * @property array $contributors
 * @property string $age_group
 * @property string $ingredients
 * @property string $nutrition_facts
 * @property string $energy_efficiency_class
 * @property string $color
 * @property string $gender
 * @property string $material
 * @property string $pattern
 * @property string $format
 * @property string $multipack
 * @property string $size
 * @property string $length
 * @property string $width
 * @property string $height
 * @property string $weight
 * @property string $release_date
 * @property string $description
 * @property array $features
 * @property array $images
 * @property string $last_update
 * @property array $stores
 * @property array $reviews
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Barcode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Barcode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Barcode query()
 * @mixin \Eloquent
 */
class Barcode extends Model
{
    use \Awobaz\Compoships\Compoships;
    protected $table = 'pnci_pim.barcode';
    protected $fillable = ['aaiabrandid','part_number_unformatted','barcode_number','barcode_formats','mpn','model','asin','title','category','manufacturer','brand','contributors','age_group','ingredients','nutrition_facts','energy_efficiency_class','color','gender','material','pattern','format','multipack','size','length','width','height','weight','release_date','description','features','images','last_update','stores','reviews'];
    protected $casts = ['stores' => 'array','reviews' => 'array','features'=>'array','images'=>'array','contributors'=>'array'];

}