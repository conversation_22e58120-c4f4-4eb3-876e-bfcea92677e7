<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ImagePrimary
 *
 * @property int $partmaster_id
 * @property string $image_url
 * @property string $alternatives
 * @property string $catalogVersion
 * @property string $modified_at
 * @method static \Illuminate\Database\Eloquent\Builder|ImagePrimary newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImagePrimary newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImagePrimary query()
 * @mixin \Eloquent
 */
class ImagePrimary extends Model
{
    use \Awobaz\Compoships\Compoships;
    protected $table = 'whi_aces.image_feed_normalized_primary';
    protected $primaryKey = 'id';
    public $timestamps = false;

}