<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Zipcode
 *
 * @property int $id
 * @property string|null $zip
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $city
 * @property string|null $county
 * @property string|null $state
 * @property string|null $country
 * @method static \Illuminate\Database\Eloquent\Builder|Zipcode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Zipcode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Zipcode query()
 * @mixin \Eloquent
 */
class Zipcode extends Model
{
    protected $table = 'faceted_meta_helper.zipcodes';
    protected $primaryKey = 'id';
    public $timestamps = false;
}