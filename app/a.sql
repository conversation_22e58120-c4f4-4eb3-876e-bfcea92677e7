BEGIN
    SELECT
        coalesce(vd.pslinecode, fd.LineCode,"") as `Network Line Code`,
        DATE_FORMAT(CURDATE(), '%m/%d/%Y') as `Cost Effective Date`,
        DATE_FORMAT(CURDATE(), '%m/%d/%Y') as `Sell Prices Effective`,
        DATE_FORMAT(CURDATE(), '%m/%d/%Y') as `Date Modified`,
        coalesce(fd.ItemGTIN,"") as `Item GTIN`,
        coalesce( fd.InnerPackageUPC,"") as `Item UPC or EAN`,
        i.sku as `Catalog Part Number`,
        COALESCE(fd.ShortDescription,"") as `Short Description`,
        '' as `Replacement Part Number`,
        coalesce(fd.SupplierPopularityCode,"") as `Mfc Pop Code`,
        coalesce(fd.PackageTypeName,"") as `Item Package Type`,
        COALESCE((fd.MinOrderQty,"") as `Item Package Qty`,
                 coalesce(fd.QtyUOMName,"") as `Item Quantity UOM`,
                 coalesce(fd.MinOrderQty,"") as `Minimum Order Qty`,
                 coalesce(fd.SupplierItemList,"") as `Suggested List`,
                 i.`list` as `Suggested Retail`,
                 COALESCE(fd.ItemRetail,"") as `Online Retail`,
                 coalesce(fd.ItemWHSALE,"") as `Suggested Wholesale`,
                 coalesce(fd.ItemAffiliate,"") as `Network Affiliate`,
                 coalesce(fd.NetworkToSophioPrice,"") as `Online Wholesale`,
                 coalesce(fd.AffilateCore,"") as `Affiliate Core`,
                 coalesce(fd.LineItemGoldMemberInvoice,"") as `Line Item Invoice Price`,
                 coalesce(fd.LineItemGoldMemberInvoice,"") as `Net Invoice Cost`,
                 coalesce(fd.GoldMemberInvoiceCore,"") as `Line Item Invoice Core Price`,
                 coalesce(fd.GoldMemberInvoiceCore,"") as `Net Invoice Core Cost`,
                 coalesce(fd.EachPackageGTIN,"") as `Each Package GTIN14`,
                 coalesce(fd.InnerPackageUPC,"") as `Each Package UPC or EAN`,
                 i.minqty as `Each Package Inner Quantity`,
                 i.uom as `Each Package Inner Qty UOM`,
                 'Ea.' as `Each Package Type`,
                 i.weight as `Each Weight`,
                 i.height as `Each Height`,
                 i.width as `Each Width`,
                 0.00 as `Each Depth`,
                 i.gtin as `Inner Pack GTIN(14)`,
                 i.upc as `Inner Pack UPC or EAN`,
                 '' as `Inner Pack Type`,
                 '' as `Items per Inner Pack`,
                 i.weight as `Inner Pack Weight`,
                 i.height as `Inner Pack Height`,
                 i.width as `Inner Pack Width`,
                 0.00 as `Inner Pack Depth`,
                 '' as `Case GTIN(14)`,
                 '' as `Case UPC or EAN`,
                 0 as `Items per Case`,
                 0 as `Case Weight`,
                 500 as `Case Height`,
                 0 as `Case Width`,
                 0 as `Case Depth`,
                 '' as `Pallet Package Level GTIN`,
                 0 as `Items per Pallet`,
                 i.weight_uom as `Weight UOM`,
                 i.uom as `Dimension UOM`,
                 0.00 as `Network Item Demand`,
                 1 as `Per Car Qty`,
                 0 as `U.S. VIO Count`,
                 '' as `VIO Report Date`,
                 '' as `Life Cycle Status`,
                 '' as `MSDS FLAG`,
                 '' as `MSDS SHEET ORDER NUMBER`,
                 '' as `MSDS link`,
                 '' as `Hazerdous Class Code`,
                 '' as `Hazmat Description`,
                 '' as `Country of Origin`,
                 i.`description` as `Part Description – Long`,
                 IF(p.aaia_parttype_id <> 0, p.aaia_parttype_id, fd.PartTypeID) AS `Part Type (AAIA)`,
                 '' as `Marketing Description (Long)`,
                 '' as `Summary of Applications`,
                 '' as `Product Category Code`,
                 '' as `Product Group Code`,
                 '' as `Product Sub-Group Code`,
                 '' as `California Proposition 65 Required`,
                 '' as `California Proposition 65 Text`,
                 '' as `California Proposition 65 Image`,
                 i.BrandID as `AAIA Brand ID`
                 FROM toolsequipments_aces.wws_items i
                 left JOIN tool_and_equipment.vision_dcf vd ON i.BrandID = vd.aaiabrandid
                 LEFT JOIN partshare.FlattenedData fd ON i.BrandID = fd.AAIABrandID AND i.part_number_unformatted = fd.CompressedPartNumber
                 LEFT JOIN pnci_pim.products p ON p.aaiabrandid = fd.AAIABrandID AND p.part_number_unformatted = fd.CompressedPartNumber;
END