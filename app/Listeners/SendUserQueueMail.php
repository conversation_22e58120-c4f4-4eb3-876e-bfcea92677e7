<?php

namespace App\Listeners;


use App\Events\UserQueueMailEvent;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendUserQueueMail
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param UserQueueMailEvent $event
     * @return void
     */
    public function handle(UserQueueMailEvent $event)
    {
        $class = 'App\Mail\\' . $event->mailClass;


        if (!is_array($event->emails) || count($event->emails) == 0) {
            $emails =config('sophio.admin.mail_senders.developers');
        }else{
            $emails =$event->emails;
        }
        /*
                if (isset($event->messages['mail_config'])) {
                    if ($event->messages['mail_config'] === 'store' && isset($event->messages['store'])) {
                        $config = Mail::createSymfonyTransport([
                            'transport' => 'smtp',
                            'host' => $event->messages['store']->MAILSERVER,
                            'port' => $event->messages['store']->ADVSET['SENDMAILPORT'],
                            'encryption' => isset($event->messages['store']->ADVSET['SENDMAILPORT']) && $event->messages['store']->ADVSET['SENDMAILPORT'] != "" ? 'tls' : '',
                            'username' => $event->messages['store']->ADVSET['SENDMAILUSERNAME'],
                            'password' => $event->messages['store']->ADVSET['SENDMAILPASSWORD'],
                        ]);
                        Mail::setSymfonyTransport($config);
                        $mail = Mail::to($emails);
                    }
                } else {
                    $mail = Mail::to($emails);
                }
        */
        $mail = Mail::to($emails);

        Log::channel('mail')->info(['to'=>$emails,'messages'=>$event->messages,'emails'=>$event->emails,'mailClass'=>$event->mailClass]);
        $mail->queue(new $class(
            array_merge($event->messages, ['tenant_db' => Config::get('tenant_db')])
        ));


    }
}
