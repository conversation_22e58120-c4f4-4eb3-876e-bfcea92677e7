<?php

namespace App\Providers;

use Sophio\Common\SophioUser;
use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Support\Facades\Log;

class SophioUserProvider extends EloquentUser<PERSON><PERSON>ider implements UserProvider
{


    public function updateRememberToken(Authenticatable|\Illuminate\Contracts\Auth\Authenticatable $user, $token)
    {
        // TODO: Implement updateRememberToken() method.
    }
    public function retrieveByCredentials(array $credentials)
    {

        // TODO: Implement retrieveByCredentials() method.
        if (! array_key_exists('user_name', $credentials)) {
            return null;
        }
        return parent::retrieveByCredentials($credentials);
    }
    public function validateCredentials(Authenticatable|\Illuminate\Contracts\Auth\Authenticatable $user, array $credentials)
    {

        // TODO: Implement validateCredentials() method.
        if (! array_key_exists('password', $credentials)) {
            return false;
        }
        Log::error('validateCredentials');
        return  $user->getAuthPassword()===md5($credentials['password']);
    }
}