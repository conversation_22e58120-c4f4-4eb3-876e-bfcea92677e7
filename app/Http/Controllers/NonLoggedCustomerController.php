<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

use Sophio\Common\Models\FBS\Customer;

class NonLoggedCustomerController extends Controller

{
    public function index()
    {
        if (request()->get('accountnum', '') != '' && request()->get('from') == 'WHI') {
            setLayoutBoxed();
            session(['nonlogged_accountum' => request()->get('accountnum')]);
            $cust = Customer::where('accountnum', request()->get('accountnum'))->first();
            $token = cust_token_enc(request()->get('accountnum'), $cust);
            return view('nonlogged.index', ['accountnum' => request()->get('accountnum'), 'x' => true, 'token' => $token]);
        }

        if (request()->get('token')) {
            $cust = cust_token_dec(request()->get('token'));
            session(['nonlogged_accountum' => $cust->accountnum]);
            return view('nonlogged.index', ['accountnum' => $cust->accountnum, 'x' => true, 'token' => request()->get('token')]);
        }
        return redirect("/");
    }
    public function b2bregisterverify(){

    }
    public function b2bregister()
    {
        $validator = Validator::make(request()->all(), [
            'email' => ['required', 'string', 'email', 'max:255', 'unique:wws_customers'],

        ]);
        if (request()->post()) {
            $validator = Validator::make(request()->all(), [
                'email' => ['required', 'string', 'email', 'max:255', 'unique:wws_customers'],
            ]);
            $validator->validate();
            $customer = Customer::create(
                [
                    'email' => request()->get('email'),
                    'company' => request()->get('company'),
                    'firstname' => request()->get('firstname'),
                    'lastname' => request()->get('lastname'),
                    'address' => request()->get('address'),
                    'address2' => request()->get('address2') ?? '',
                    'city' => request()->get('city'),
                    'state' => request()->get('state'),
                    'country' => request()->get('countryid'),
                    'countryid' => request()->get('countryid'),
                    'zip' => request()->get('zip'),
                    'phone' => request()->get('phone'),
                    'mobile' => request()->get('phone'),
                    'custtype' => request()->get('custtype'),
                    'paymethod' => request()->get('paymethod'),
                    'xml' => ['ANNUALPVOL' => request()->get('annualpvol')],
                    'notes' => request()->get('notes'),
                    'heardfrom' => request()->get('referral'),
                    'url' => request()->get('url'),
                    'accountnum' => request()->get('accountnum') ?? '',
                    'st_addr' => request()->get('address'),
                    'st_addr2' => request()->get('address2') ?? '',
                    'st_city' => request()->get('city'),
                    'st_state' => request()->get('state'),
                    'st_phone' => request()->get('phone'),
                    'st_zip' => request()->get('zip'),
                    'st_ctryid' => request()->get('countryid'),
                    'st_ctry' => request()->get('countryid'),
                    'active'=>'F',
                ]
            );
            $remote_filename = request()->file('file')->getClientOriginalName();
            \Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.fbs.root') . '/customers/' . $customer->pk, 0755, true);
            Storage::disk('fbs')->putFileAs('/customers/' . $customer->pk, request()->file('file'), $remote_filename);
            $customer->xml['AGREEMENTFILE'] = '/customers/' . $customer->pk . '/' . $remote_filename;
            $customer->xml['AGREEMENTSIGNATURE'] = request()->get('agreement-signature');
            $customer->xml['AGREEMENTCOMPANYNAME'] = request()->get('agreement-company-name');
            $customer->xml['AGREEMENTDATE'] = request()->get('agreement-date');
            $customer->xml['AGREEMENTTITLE'] = request()->get('agreement-purchaser-title');
            $customer->xml['AGREEMENTDDESCRIBEDITEMS'] = request()->get('agreement-described-items');
            $customer->xml['AGREEMENTDDESCRIBEDEXEMPTION'] = request()->get('agreement-described-exemption');
            $customer->xml['NEXPARTUSERNAME']= request()->get('b2busername');
            $customer->xml['NEXPARTPASSWORD']= request()->get('b2bpassword');
            $customer->save();

        }
        return view('nonlogged.b2bregister');
    }
}