<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Website;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\View;

class Frontend extends Controller
{
    public function __construct()
    {

        $this->middleware(function (Request $request, $next) {
            
            if ($request->input('lang') != '') {
                $lang = $request->input('lang');
                if (in_array($lang, ['en', 'es', 'fr'])) {
                    \App::setLocale($lang);
                }
            }
            $domain = $request->getHttpHost();

            $website = Cache::rememberForever('domain-' . $domain, function () use ($domain) {

                $website = Website::where('domain', $domain)->first()->toArray();

                $website = array_merge($website, $website['metadata']);
                return $website;
            });

            if ($website) {
                if ($request->input('theme')) {
                    $theme = $request->input('theme');
                } else {
                    $theme = str_replace('-', '_', $website['design']);
                }
                $website['layout'] = 'catalog-2.design.' . $theme;
                $website['pagination'] = 1;
                View::addNamespace('theme', [
                    resource_path('views/themes/' . $theme),
                    resource_path('views/themes/default'),
                    resource_path('views/catalog-2'),
                ]);


                config(
                    ['website' => $website]
                );

                $request->api_base_url = $website['api_base'] . '/' . $website['uuid'] . '/catalog-2/';
                $request->website_parameters = (array)$website;

                $request->api_parameters = [
                    'catalogtype' => $website['catalogtype'],
                    'clientId' => $website['clientId'],
                    'contactpk' => $website['contactpk'],
                    'regionId' => $website['regionId'],
                    'nomfgcode' => $website['nomfgcode']
                ];
                if (isset($website['counterman_questions']) && $website['counterman_questions'] == "1") {
                    $request->api_parameters['questions'] = 1;
                    $request->website_parameters['pagination'] = 0;
                }
                if ($request->input('nocache', 0) == 1) {
                    $request->api_parameters['nocache'] = microtime(true);
                }

                $make = $request->input('make', \Route::current()->parameter('make', ''));
                $model = $request->input('model', \Route::current()->parameter('model', ''));
                $year = $request->input('year', \Route::current()->parameter('year', ''));
                $engine = $request->get('engine', \Route::current()->parameter('engine', ''));
                $submodel = $request->get('submodel', \Route::current()->parameter('submodel', ''));

                $vehicle_key = $year . '-' . $make . '-' . $model;
                $vehicle = [
                    'ids' => [
                        'year' => $year,
                        'make' => $make,
                        'model' => $model,
                        'engine' => $engine,
                        'submodel' => $submodel,
                    ],
                    'name' => $year . ' ' . Str::title(str_replace('-', ' ', $make)) . ' ' . Str::title(str_replace('-', ' ', $model)),
                    'api_url' => $request->api_base_url .
                        'vehicle/' . $year . '/' . $make . '/' . $model
                ];
                $cookie_vehicle = json_decode(base64_decode($request->cookie('current_vehicle')),true);
                if(is_array($cookie_vehicle)) {


                    $vehicle['extended_ids'] = array_diff_key ($cookie_vehicle,$vehicle['ids']);
                    unset( $cookie_vehicle['values']);
                    $vehicle['ids'] = array_merge($vehicle['ids'], $cookie_vehicle);
                }else{
                    $vehicle['extended_ids'] =  [];
                }


                if ($make != "" && $model !== "" && $year !== "") {
                    $request->website_parameters['has_vehicle'] = true;
                } else {
                    $request->website_parameters['has_vehicle'] = false;
                }

                $request->website_parameters['vehicle'] = $vehicle;

                if ($vehicle_key != "--") {

                    session(['vehicle' => $vehicle]);
                } else {
                    session(['vehicle' => null]);
                }

                $request->global_taxonomies = cache()->remember('parttypes-all-' . $request->api_parameters['clientId'], 18600, function () use ($request) {
                    $url = $request->api_base_url . 'parttypes';
                    $response = Http::asForm()->post($url, $request->api_parameters)->json();

                    $tax = [];
                    foreach ($response['parttypes'] as $pt) {

                        foreach ($pt['categories'] as $category) {

                            if (!isset($tax[$category['category_id']])) {
                                $tax[$category['category_id']] = [
                                    'name' => $category['category_name'],
                                    'parttypes' => []
                                ];
                            }
                            $tax[$category['category_id']]['parttypes'][$pt['id']] = $pt['name'];
                        }

                    }
                    return $tax;
                });
                $request->global_manufacturers = cache()->rememberForever('manufacturers-allw-' . $request->api_parameters['clientId'], function () use ($request) {
                    $url = $request->api_base_url . 'manufacturer';
                    $response = Http::asForm()->post($url, $request->api_parameters)->json();
                    foreach ($response['manufacturers'] as $k => $m) {
                        $ch = curl_init('https://images.sophio.com/catalog/aces/brands/600/' . $m['id'] . '.jpg');
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                        curl_exec($ch);
                        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        if (($code == 301) || ($code == 302)) {
                            $response['manufacturers'][$k]['hasImage'] = false;
                        } else {
                            $response['manufacturers'][$k]['hasImage'] = true;
                        }
                    }
                    return $response['manufacturers'];
                });
                if (\Illuminate\Support\Facades\Route::current()->named('catalog.vehicle.*')) {
                    $request->breadcrumbs = 'vehicle';
                } else {
                    $request->breadcrumbs = false;
                }
            }
            $request->api_parameters_translate = [
                'subcategories' => 'subcategory',
                'manufacturers' => 'manufacturer',
                'engines' => 'engine',
                'parttypes' => 'parttype',
                'categories' => 'category',
                'years' => 'year',
                'makes' => 'make',
                'positions' => 'position',
                'models' => 'model',
                /*
                'AC' => 'AC',
                'ASPIRATION' => 'ASPIRATION',
                'BED_LENGTH' => 'BED_LENGTH',
                'BED_TYPE' => 'BED_TYPE',
                'BODY_NUM_DOORS' => 'BODY_NUM_DOORS',
                'BODY_TYPE' => 'BODY_TYPE',
                'BRAKE_ABS' => 'BRAKE_ABS',
                'BRAKE_SYSTEM' => 'BRAKE_SYSTEM',
                'CYLINDER_HEAD_TYPE' => 'CYLINDER_HEAD_TYPE',
                'DRIVE_TYPE' => 'DRIVE_TYPE',
                'ENGINE_BASE' => 'ENGINE_BASE',
                'ENGINE_DESIGNATION' => 'ENGINE_DESIGNATION',
                'ENGINE_VERSION' => 'ENGINE_VERSION',
                'ENGINE_VIN' => 'ENGINE_VIN',
                'ENGINE_MFR' => 'ENGINE_MFR',
     */

            ];
            return $next($request);
        });
        \App::setLocale('en');
    }

    public function home(Request $request)
    {
        return view('theme::pages.home', ['parameters' => $request->api_parameters, 'can_have_vehicle' => false]);
    }

    public function start(Request $request)
    {
        return view('theme::pages.start', ['parameters' => $request->api_parameters, 'can_have_vehicle' => false]);
    }

    public function searchVin(Request $request)
    {
        $url = $request->api_base_url . 'vin/' . \Route::current()->parameter('searchfor', '');

        $response = Http::asForm()->post($url, $request->api_parameters)->json();
        if (isset($response['make_slug']) && $response['make_slug'] != "") {
            return redirect()->route('vehicle.mym', [
                'make' => $response['make_slug'],
                'model' => $response['model_slug'],
                'year' => $response['year'],
                'engine' => $response['engine_slug']
            ]);
        }
    }

    public function search(Request $request)
    {
        
        if ($request->input('searchtype', '') == "vin") {
            return $this->searchVin($request);
        }
        $request->per_page = 12;
        $url = $request->api_base_url . 'parts';
        $request->api_parameters = array_merge($request->api_parameters,
            [
                'year' => $request->input('year', \Route::current()->parameter('year', '')),
                'make' => $request->input('make', \Route::current()->parameter('make', '')),
                'model' => $request->input('model', \Route::current()->parameter('model', '')),
                'engine' => $request->input('engine', $request->input('engine', '')),
                'submodel' => $request->input('submodel', $request->input('submodel', '')),
                'category' => $request->input('category', \Route::current()->parameter('category', '')),
                'subcategory' => $request->input('subcategory', ''),
                'parttype' => \Route::current()->parameter('parttype', $request->input('parttype', '')),
                'manufacturer' => \Route::current()->parameter('manufacturer', $request->input('manufacturer', '')),
                'searchType' => $request->input('searchType', ''),
                'searchfor' => $request->input('searchfor', \Route::current()->parameter('searchfor', '')),
                'position' => $request->input('position', ''),
                'nomfgcode' => $request->input('nomfgcode', ''),
                'getlabor' => $request->input('getlabor', ''),
                'offset' => ($request->input('currentpage', 1) - 1) * 12,
                'orderby' => $request->input('sortby', ''),
                'long' => $request->input('long', ''),
                'lat' => $request->input('lat', ''),

            ]
        );

        $request->api_parameters = array_merge($request->except('_url'), $request->api_parameters);

        $blacklisted_facets =
            [
                'pricesegments',
                'makes',
                'models',
                'years'
            ];

        $breadcrumbs = [];
        if ($request->api_parameters['engine'] != "" && strtolower($request->api_parameters['engine']) != "all") {
            $breadcrumbs['engine'] = $request->api_parameters['engine'];
        }
        if ($request->api_parameters['manufacturer'] != "" && strtolower($request->api_parameters['manufacturer']) != "all") {
            $breadcrumbs['manufacturer'] = $request->api_parameters['manufacturer'];
        }
        if ($request->api_parameters['position'] != "" && strtolower($request->api_parameters['position']) != "all") {
            $breadcrumbs['position'] = $request->api_parameters['position'];
        }
        if ($request->api_parameters['parttype'] != "") {
            $blacklisted_facets[] = 'subcategories';
            $blacklisted_facets[] = 'categories';
        }


        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        $facets = $response['facets'] ?? [];
        foreach ($blacklisted_facets as $bfacet) {
            if (isset($facets[$bfacet])) {
                unset($facets[$bfacet]);
            }
        }

        $questions = $response['questions'] ?? [];
        $question_facets = [];
        /*
        if(isset($facets['positions']) && count($facets['positions'])>1)  {
            $question_facets[] = 'positions';
        }
        */
        if (empty($questions)) {

            foreach ($facets as $name => $facet) {
                if (!isset($request->api_parameters_translate[$name]) && count($facet) > 1) {
                    $question_facets[] = $name;
                }
            }
        }

        $parts = $response['results']['parts'] ?? [];
        foreach ($parts as $i => $part) {
            if (isset($parts[$i]['application_note']))
                $parts[$i]['application_note'] = implode('<br>', array_slice(explode(';', $part['application_note']), 0, 3));
        }
        $view = ($request->ajax()) ? 'theme::components.search_results' : 'theme::pages.search';
        $can_have_vehicle = $request->input('searchfor', \Route::current()->parameter('searchfor', '')) == '' ? true : false;
        if ($request->server('HTTP_USER_AGENT') == "google") {
            $can_have_vehicle = false;
        }
        
        return view($view, [
            'can_have_vehicle' => $can_have_vehicle,
            'parameters_translate' => $request->api_parameters_translate,
            'parameters' => $request->api_parameters,
            'facets' => $facets,
            'question_facets' => $question_facets,
            'questions' => $questions,
            'per_page' => $request->per_page,
            'current_page' => $request->input('currentpage', 1),
            'parts' => $parts,
            'breadcrumbs' => $breadcrumbs,
            'count' => $response['meta']['total_found'] ?? 0,
            'options' => ['showas' => $request->input('showas', 'grid')],
            'vehicle_name' => ucwords($request->api_parameters['year'] . ' ' . $request->api_parameters['make'] . ' ' . $request->api_parameters['model']),
            'vehicle' => [
                'year' => $request->api_parameters['year'],
                'make' => $request->api_parameters['make'],
                'model' => $request->api_parameters['model']
            ],
            'item_detail_url' => '/catalog-2/itemdetail/%s/%s',
            'url' => $request->api_base_url .
                'vehicle/' . $request->api_parameters['year'] . '/' . $request->api_parameters['make'] . '/' .
                $request->api_parameters['model']
        ]);
    }

    public function itemdetail(Request $request)
    {

        $url = $request->api_base_url . 'itemdetail/' . \Route::current()->parameter('mfg', '') . '/' . \Route::current()->parameter('sku', '');
        $request->api_parameters = array_merge($request->api_parameters,
            [
                'mfg' => \Route::current()->parameter('mfg', ''),
                'sku' => \Route::current()->parameter('sku', ''),
                'year' => \Route::current()->parameter('year', ''),
                'model' => \Route::current()->parameter('model', ''),
                'make' => \Route::current()->parameter('make', ''),

            ]
        );

        $request->api_parameters = array_merge( $request->api_parameters, $request->website_parameters['vehicle']['ids'] );

        $response = Http::asForm()->post($url, $request->api_parameters)->json();
        $facets = [];
        $question_facets = [];
        $part = $response['results']['parts'][0] ?? [];
        if (empty($part)) {
            $part = ["product_sku" => microtime(true),
                "part_number" => \Route::current()->parameter('sku', ''),
                "part_label" => "",
                "part_desc" => "",
                "wddescription" => "",
                "part_whse_desc" => "",
                "mfg_code" => "",
                "mfg_name" => str_replace("-", ' ', \Route::current()->parameter('mfg', '')),
                "linecode" => "NAT",
                "qty_per_application" => 0,
                "qty_uom" => "EA",
                "gtin" => "",
                "min_order_qty" => 1,
                "qty_avail" => 0,
                "position" => "",
                "part_status" => "",
                "supplier_code" => "WHD",
                "application_note" => "",
                "popularity" => "",
                "oem" => "N/A",
                "pricing" => ['sellprice' => 0, 'listprice' => 0, 'coreprice' => 0,
                    'cost' => 0, 'map' => 0, 'currency' => 'USD'],
                "packaging" => ["height" => 0,
                    "width" => 0,
                    "length" => 0,
                    "weight" => 0,
                    "dimension_uom" => "inches",
                    "weight_uom" => "pounds",
                    "pack_gtin" => ""],
                "fitment" => [],
                "image_url" => [],
                "vehicle" => [],
                "taxonomy" => ['category' => ['id' => '', 'name' => ''],
                    'subcategory' => ['id' => '', 'name' => ''],
                    'parttype' => ['id' => '', 'name' => '']],
                "options" => "",
                "part_number_slug" => Str::slug(\Route::current()->parameter('sku', '')),
                "partterminologyid" => "",
                "zipcode" => "",
                "contactpk" => "",
                "omsid" => "",
                "zoroid" => "",
                "tractorid" => "",
                "mfg_name_slug" => \Route::current()->parameter('mfg', ''),
                "relevancy" => "",
                "distance" => "",
                "inv" => "",
                "inventories," => "",
                "type" => "apps",
                "warehouses" => "",
                "attributes" => []];
        }
        $fits = 0;

        $current_vehicle = $request->website_parameters['vehicle'];Log::error($current_vehicle);
        if ($request->website_parameters['has_vehicle']==true) {
            $fits = 2;

            if (isset($part['fitment']))
                foreach ($part['fitment'] as $fitment) {
                    if (Str::slug($fitment['year']) == $current_vehicle['ids']['year'] &&
                        Str::slug($fitment['model']) == $current_vehicle['ids']['model'] &&
                        Str::slug($fitment['make']) == $current_vehicle['ids']['make']
                    ) {
                        $fits = 1;
                        if ($current_vehicle['ids']['engine'] != ""  && $current_vehicle['ids']['engine'] != "all") {
                            if (Str::slug($fitment['engine']) == $current_vehicle['ids']['engine']) {
                                $fits = 1;
                            } else {
                                $fits = 0;
                            }
                        }

                    }
                }
            if (isset($request->website_parameters['counterman_questions']) && $request->website_parameters['counterman_questions'] == "1" && isset($part['questions'])) {

                if(isset($response['facets'] ) && count($response['facets'])>0) {
                    $facets = $response['facets'];
                    foreach ($facets as $name => $facet) {
                        if (!isset($request->api_parameters_translate[$name]) && count($facet) > 1) {
                            $question_facets[] = $name;
                        }
                    }

                }
                foreach($part['questions'] as $q) {
                    if($q['Name']==null) {
                        $fits = 4;
                    }
                    elseif(!in_array($q['Name'],array_keys($request->website_parameters['vehicle']['ids'])) && in_array($q['Name'],$question_facets)) {

                        $fits = 3;
                    }elseif(in_array($q['Name'],array_keys($request->website_parameters['vehicle']['ids'])) && $request->website_parameters['vehicle']['ids'][$q['Name']]!=$q['Id']){
                        $fits = 3;
                    }
                }


            }
        }
        Log::error($fits);
        $part_attributes = [];
        if (isset($part['fitment']))
            foreach ($part['attributes'] as $attribute) {

                if (isset($part_attributes[array_key_first($attribute)])) {
                    $part_attributes[array_key_first($attribute)][] = array_shift($attribute);
                } else {
                    $part_attributes[array_key_first($attribute)] = [array_shift($attribute)];
                }
            }
        $part['attributes'] = $part_attributes;

        return view('theme::pages.itemdetail', ['can_have_vehicle' => $current_vehicle == null ? true : false, 'part' => $part, 'fits_current_vehicle' => $fits,
            'question_facets' => $question_facets,
            'facets' => $facets,
            'questions' => $part['questions']??[],]);
    }

    public function parttypes(Request $request)
    {
        $url = $request->api_base_url .
            'parttypes/' . \Route::current()->parameter('year', '') . '/' . \Route::current()->parameter('make', '') . '/' .
            \Route::current()->parameter('model', '');
        $request->api_parameters = array_merge($request->api_parameters,
            [
                'year' => \Route::current()->parameter('year', ''),
                'make' => \Route::current()->parameter('make', ''),
                'model' => \Route::current()->parameter('model', ''),
                'engine' => $request->input('engine', '')
            ]
        );
        $have_vehicle = (
            \Route::current()->parameter('year', '') == "" ||
            \Route::current()->parameter('make', '') == "" ||
            \Route::current()->parameter('model', '') == ""
        ) ? false : true;
        $response = Http::asForm()->post($url, $request->api_parameters)->json();
        $cats_parttypes = [];
        $parttypes = $response['parttypes'];
        foreach ($parttypes as $pt) {
            foreach ($pt['categories'] as $c) {
                if (!isset($cats_parttypes[$c['category_id']])) {
                    $cats_parttypes[$c['category_id']] =
                        [
                            'name' => $c['category_name'],
                            'parttypes' => [
                                [
                                    'id' => $pt['id'],
                                    'name' => $pt['name']
                                ]
                            ]
                        ];
                } else {
                    $cats_parttypes[$c['category_id']]['parttypes'] [] = [
                        'id' => $pt['id'],
                        'name' => $pt['name']
                    ];
                }
            }
        }

        return view('theme::pages.parttypeslisting', [
            'can_have_vehicle' => false,
            'props' => $cats_parttypes,

            'taxonomy' => [
                'title' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make'] . ' ' . $request->api_parameters['model']),
                'current' => 'Part Type',
                'parent' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make'] . ' ' . $request->api_parameters['model']),
                'url' => ($have_vehicle
                    ? strtolower('/catalog-2/vehicle/' . $request->api_parameters['make'] . '/' . $request->api_parameters['year'] . '/' . $request->api_parameters['model'] . '/%s?engine=' . $request->api_parameters['engine'])
                    : strtolower('/catalog-2/parttype/%s')
                ),
                'url_category' => ($have_vehicle
                    ? strtolower('/catalog-2/category/%s/' . $request->api_parameters['make'] . '/' . $request->api_parameters['year'] . '/' . $request->api_parameters['model'])
                    : strtolower('/catalog-2/category/%s/')
                ),
                'naming' => ' %s'
            ]

        ]);
    }

    public function models(Request $request)
    {

        $url = $request->api_base_url . 'vehicle';
        $request->api_parameters = array_merge($request->api_parameters,
            [
                'make' => \Route::current()->parameter('make', ''),
                'year' => \Route::current()->parameter('year', '')
            ]
        );

        $response = Http::asForm()->post($url, $request->api_parameters)->json();
        return view('theme::pages.taxnomythumbs', [
            'can_have_vehicle' => false,
            'props' => $response['models'],
            'taxonomy' => [
                'title' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make']),
                'current' => 'Model',
                'parent' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make']),
                'url' => '/catalog-2/vehicle/' . $request->api_parameters['make'] . '/' . $request->api_parameters['year'] . '/%s',
                'image_url' => 'https://images-us1.sophio.com/vehicle/' . strtolower($request->api_parameters['make']) . '/' . strtolower($request->api_parameters['make']) . '_%s.png',
                'naming' => strtoupper($request->api_parameters['make']) . ' %s',
                'noimage' => 'https://images.sophio.com/vehicle/vehicle-coming-soon.jpg',
                'class_prefix' => 'categorycolumn'
            ]

        ]);
        return view('theme::pages.taxonomylist', [
            'can_have_vehicle' => false,
            'props' => $response['models'],
            'taxonomy' => [
                'title' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make']),
                'current' => 'Model',
                'parent' => strtoupper($request->api_parameters['year'] . ' ' . $request->api_parameters['make']),
                'url' => '/catalog-2/vehicle/' . $request->api_parameters['make'] . '/' . $request->api_parameters['year'] . '/%s',
                'naming' => strtoupper($request->api_parameters['make']) . ' %s'
            ]

        ]);
    }

    public function years(Request $request)
    {
        $url = $request->api_base_url . 'vehicle';
        $request->api_parameters = array_merge($request->api_parameters, ['make' => \Route::current()->parameter('make', '')]);

        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        return view('catalog-2.pages.taxonomylist', [
            'can_have_vehicle' => false,
            'props' => $response['years'],
            'taxonomy' => [
                'title' => strtoupper($request->api_parameters['make']),
                'current' => 'Year',
                'parent' => strtoupper($request->api_parameters['make']),
                'url' => '/catalog-2/vehicle/' . $request->api_parameters['make'] . '/%s',
                'naming' => '%s'
            ]

        ]);
    }

    public function makes(Request $request)
    {
        $url = $request->api_base_url . 'vehicle';


        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        if ($request->website_parameters['onlymakes'] != '') {
            $allowed_makes = explode('@@', $request->website_parameters['onlymakes']);
            foreach ($response['makes'] as $k => $make) {
                if (!in_array($make['id'], $allowed_makes)) {
                    unset($response['makes'][$k]);
                }
            }
        }
        return view('theme::pages.taxnomythumbs', [
            'can_have_vehicle' => false,
            'props' => $response['makes'],
            'taxonomy' => [
                'title' => '',
                'current' => 'Make',
                'parent' => '',
                'url' => '/catalog-2/vehicle' . '/%s',
                'image_url' => 'https://images-us1.sophio.com/vehicle/makes/make_%s_over.png',
                'naming' => '%s',
                'noimage' => 'https://images.sophio.com/vehicle/vehicle-coming-soon.jpg',
                'class_prefix' => 'makecolumn'
            ]

        ]);

    }

    public function categories(Request $request)
    {
        $url = $request->api_base_url . 'category';


        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        return view('theme::pages.taxnomythumbs', [
            'can_have_vehicle' => false,
            'props' => $response['categories'],
            'taxonomy' => [
                'title' => '',
                'current' => 'Category',
                'parent' => '',
                'name' => 'category',

                'url' => '/catalog-2/category' . '/%s',
                'image_url' => 'https://images.sophio.com/catalog/aces/category/%s.png',
                'naming' => '%s',
                'noimage' => 'https://images.sophio.com/vehicle/vehicle-coming-soon.jpg',
                'class_prefix' => 'categorycolumn'
            ]

        ]);
    }

    public function subcategories(Request $request)
    {
        $url = $request->api_base_url . 'category';


        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        return view('theme::pages.taxnomythumbs', [
            'can_have_vehicle' => false,
            'props' => $response['subcategories'],
            'taxonomy' => [
                'title' => '',
                'current' => 'Subcategory',
                'parent' => '',
                'url' => '/catalog-2/category' . '/%s',
                'image_url' => 'https://images.sophio.com/catalog/aces/subcategory/%s.png',
                'naming' => '%s',
                'noimage' => 'https://images.sophio.com/vehicle/coming-soon.jpg',
                'class_prefix' => 'categorycolumn'
            ]

        ]);
    }

    public function manufacturers(Request $request)
    {
        $url = $request->api_base_url . 'manufacturer';


        $response = Http::asForm()->post($url, $request->api_parameters)->json();

        return view('theme::pages.taxnomythumbs', [
            'can_have_vehicle' => false,
            'props' => $response['manufacturers'],
            'taxonomy' => [
                'title' => '',
                'current' => 'Manufacturer',
                'parent' => '',
                'url' => '/catalog-2/manufacturer' . '/%s',
                'image_url' => 'https://images.sophio.com/catalog/aces/brands/600/%s.jpg',
                'naming' => '%s',
                'noimage' => 'https://images.sophio.com/vehicle/coming-soon.jpg',
                'class_prefix' => 'categorycolumn'
            ]

        ]);
    }

    public function checkfit(Request $request)
    {
        $url = $request->api_base_url . 'checkFitment/' . \Route::current()->parameter('productSku', '') . "/" .
            \Route::current()->parameter('year', '') . '/' . \Route::current()->parameter('make', '') . '/' .
            \Route::current()->parameter('model', '');
        if (\Route::current()->parameter('engine', '') != '') {
            $url .= "/" . \Route::current()->parameter('engine', '');
        }
        $request->api_parameters = array_merge($request->api_parameters, ['productSku' => \Route::current()->parameter('productSku', '')]);
        $response = Http::asForm()->post($url, $request->api_parameters)->json();
        if ($response['fits'] == "OK") {

            $make = \Route::current()->parameter('make', '');
            $model = \Route::current()->parameter('model', '');
            $year = \Route::current()->parameter('year', '');
            $engine = $request->get('engine');
            $submodel = $request->get('submodel');

            $vehicle = [
                'ids' => [
                    'year' => $year,
                    'make' => $make,
                    'model' => $model,
                    'engine' => $engine,
                    'submodel' => $submodel,
                ],
                'name' => $year . ' ' . Str::title(str_replace('-', ' ', $make)) . ' ' . Str::title(str_replace('-', ' ', $model)),
                'api_url' => $request->api_base_url .
                    'vehicle/' . \Route::current()->parameter('year', '') . '/' . \Route::current()->parameter('make', '') . '/' .
                    \Route::current()->parameter('model', '')
            ];
            session(['vehicle' => $vehicle]);
        }
        return response()->json($response);
    }

}
