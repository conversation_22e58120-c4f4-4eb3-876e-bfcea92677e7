<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\PartsharePricing;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\Partshare\FlattenedData;
use Sophio\Common\Models\PIM\Product;
use Sophio\Common\Models\WHIACES\Manufacturer;
use Sophio\Common\Services\ProductInformation;

class PartshareController extends Controller
{
    public function findByPid()
    {

        Config::set('tenant_db', config('sophio.admin.default_database'));
        $partshare = FlattenedData::where('PID', request()->get('pid'))->first();

        if ($partshare) {

            $product = Product::where('aaiabrandid',$partshare->AAIABrandID)->where('part_number_unformatted',$partshare->CompressedPartNumber)->first();
            if(!$product) {
                return response()->json([
                    'success' => false
                ]);
            }
            $return = ['success' => true];
            $imgs=[];

            foreach($product->images as $image){
                $imgs[] = getImagePath($image['resized']);
            }
            $return['images'] = $imgs;
            $m = Manufacturer::where('mfg_code', $product->mfg_code)->first();
            if($m) {
                $return['url'] = '/catalog-2/itemdetail/' . $m->mfg_name_slug . '/' . Str::slug($partshare->part_number) . '?mastertemplate=design-partshare';
            }else{
                $return['url']='';
            }
            return response()->json($return);
        }
        return response()->json([
            'success' => false
        ]);

    }

    public function renderButton()
    {
        Config::set('tenant_db', config('sophio.admin.default_database'));

        $website = 'https://www.lastchanceautoparts.com';
        $partshare = PartsharePricing::where('PID', request()->get('pid'))->first();
        if (!$partshare) {
            return '';
        }
        $pi = new ProductInformation($partshare->WHIManufacturerCode, unformatString($partshare->part_number));
        $pi->discover();
        $product_feed = $pi->getProductFeed();
        if ($product_feed->product_sku) {
            return view('partshare.buybutton', ['partshare' => $partshare, 'website' => $website, 'product_feed' => $product_feed]);
        }
        return '';
    }
}