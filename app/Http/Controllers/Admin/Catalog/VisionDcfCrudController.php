<?php

namespace App\Http\Controllers\Admin\Catalog;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

use Sophio\Common\Models\AAIA\BrandMix;
use Sophio\Common\Models\Catalog\LocalBrands;
use Sophio\Common\Models\Catalog\VisionDCF;
use Sophio\Common\Models\FBS\Lookups;
use Sophio\Common\Models\Partshare\Linecode;

class VisionDcfCrudController extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\Pro\Http\Controllers\Operations\FetchOperation;

    public function setup()
    {


        CRUD::setModel(VisionDCF::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/visiondcf');
        CRUD::setEntityNameStrings('Vision line', 'Vision DCF');
        VisionDCF::saved(function (VisionDCF $entry) {
            if(!LocalBrands::where('BrandID',$entry->aaiabrandid)->first()) {
                LocalBrands::create(['mfg_code'=>$entry->aaiabrandid,'BrandID'=>$entry->aaiabrandid,'active'=>1,'mfg_name'=>$entry->brandmix()->BrandName]);
            }

        });
    }

    protected function setupCreateOperation()
    {
        CRUD::field([
            'name' => 'aaiabrandid',
            'label' => "Brand",
            'default' => request()->get('aaiabrandid'),
            'entity' => false,
            'type' => 'ourselect2_from_ajax',
            'foreignkey' => 'brand_id',
            'attributes' => [
                'required'=>'required'
            ],
            'model' => BrandMix::class,

            'attribute' => "path",
            'method' => 'POST',
            'data_source' => backpack_url('visiondcf/fetch/localbrand'),

        ]);
        CRUD::field([
            'name' => 'linecode',
            'label' => "Vision linecode",
            'default' => request()->get('linecode'),
            'entity' => false,
            'type' => 'ourselect2_from_ajax',
            'foreignkey' => 'cdata',
            'attributes' => [
                'required'=>'required'
            ],
            'model' => Lookups::class,
            'attribute' => "cdatas",
            'method' => 'POST',
            'data_source' => backpack_url('visiondcf/fetch/visionlinecode'),

        ]);
        /*
        CRUD::field([
            'name' => 'pslinecode',
            'label' => "Partshare linecode",
            'default' => request()->get('pslinecode'),
            'type' => 'ourselect2_from_ajax',
            'foreignkey' => 'LineCode',
             'model' => Linecode::class,
            'attribute' => "description",
            'method' => 'POST',
            'data_source' => backpack_url('visiondcf/fetch/partsharelinecode'),

        ]);
        */

        CRUD::field([
            'name' => 'pslinecode',
            'label' => "Partshare linecode",
            'default' => request()->get('pslinecode'),
            'type' => 'text',

        ]);
    }

    public function fetchvisionlinecode()
    {
        return
            Lookups::where('type', 'VISIONLINECODE')
                ->where(function ($where) {
                    $where->where('cdata', 'LIKE', '%' . request()->get('q') . '%')
                        ->orWhere('cdata1', 'LIKE', '%' . request()->get('q') . '%');
                })
                ->selectRaw('*,concat(cdata," - ",cdata1) as cdatas')
                ->get();
    }

    public function fetchPartsharelinecode()
    {
        return Linecode::where('LineCode', 'LIKE', '%' . request()->get('q') . '%')
            ->orWhere('CompanyName', 'LIKE', '%' . request()->get('q') . '%')
            ->orWhere('AAIABrandLabel', 'LIKE', '%' . request()->get('q') . '%')
            ->selectRaw('*,concat(LineCode," (",LineName,") from  ",CompanyName," - brand: ",AAIABrandLabel) as description')
            ->get();
    }

    public function fetchLocalbrand()
    {
        return BrandMix::where('BrandName', 'LIKE', '%' . request()->get('q') . '%')->selectRaw('brand_id,path')->get();
    }

    protected function setupListOperation()
    {
        CRUD::column([
            'name' => 'aaiabrandid',
            'label' => "Brand ID",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'brandmix.BrandName',
            'label' => "Brand Name",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'aaiabrandid',
            'label' => "Brand ID",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'linecode',
            'label' => "Linecode",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'pslinecode',
            'label' => "Partshare Linecode",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::filter('aaiabrandid')
            ->type('text')
            ->label('Brand ID')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'aaiabrandid', '=', $value);
            });
        CRUD::filter('brandname')
            ->type('text')
            ->label('Brand Name')
            ->whenActive(function ($value) {
                CRUD::addClause('whereHas', 'brandmix',
                    function ($query) use ($value) {
                        $query->where('BrandName', '=', $value);
                    }
                );
            });
        CRUD::filter('linecode')
            ->type('text')
            ->label('Linecode')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'linecode', '=', $value);
            });
        CRUD::filter('pslinecode')
            ->type('text')
            ->label('Partshare Linecode')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'pslinecode', '=', $value);
            });
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

}