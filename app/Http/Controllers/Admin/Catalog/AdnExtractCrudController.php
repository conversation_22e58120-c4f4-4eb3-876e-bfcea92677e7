<?php

namespace App\Http\Controllers\Admin\Catalog;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Models\Catalog\AdnExtract;
use Sophio\Common\Models\Catalog\AdnExtractDcf;

class AdnExtractCrudController     extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\Pro\Http\Controllers\Operations\FetchOperation;
    public function setup()
    {


        CRUD::setModel(AdnExtract::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/adnextract');;

        CRUD::setEntityNameStrings('Adn Extract', 'AdnExtract');
    }
    protected function setupListOperation()
    {
        CRUD::column([
            'name' => 'mfr',
            'label' => "Mfr",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'sku',
            'label' => "SKU",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'aaiabrandid',
            'label' => "Brand ID",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'level1',
            'label' => "level1",
            'limit' => 120,
            'type' => 'text',


        ]);
        CRUD::column([
            'name' => 'level2',
            'label' => "level2",
            'limit' => 120,
            'type' => 'text',


        ]);
        CRUD::column([
            'name' => 'level3',
            'label' => "level3",
            'limit' => 120,
            'type' => 'text',


        ]);
        CRUD::column([
            'name' => 'image1',
            'label' => "image1",
            'limit' => 120,
            'type' => 'image',


        ]);
        CRUD::filter('mfr')
            ->type('text')
            ->label('Mfr')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'mfr', '=', $value);
            });
        CRUD::filter('sku')
            ->type('text')
            ->label('SKU')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'SKU', 'like', "%$value%");
            });
    }
}