<?php

namespace App\Http\Controllers\Admin\Catalog;

use App\Library\Sophio\Catalog\SalesManager;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\Catalog\Sales;
use Sophio\Common\Models\Catalog\VisionDCF;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;

class SalesController extends Controller
{
    public function upload()
    {
        $i=0;
        if (request()->post()) {
            $remote_filename = request()->file('file')->getClientOriginalName();
            Storage::disk('fbs')->putFileAs('te/', request()->file('file'), $remote_filename);
            $salesManager = new SalesManager();
            $i=$salesManager->addSalesInvoices('te/' . $remote_filename);
        }
        return view('admin.catalog.sales_upload', ['rows' => $i]);
    }
}