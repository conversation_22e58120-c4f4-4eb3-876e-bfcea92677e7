<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Library\Sophio\Exporters\CommerceHubBasic;
use App\Library\Sophio\Jobs\CommerceHub\BasicExport;
use App\Library\Sophio\Jobs\CommerceHub\BatchExport;

class HomeDepotController extends Controller
{
    public function index()
    {
        $template = new CommerceHubBasic('');
        $files = $template->getAllFiles();
        return view('admin.commercehub.index', [
            'files' => $files
        ]);
    }
    public function startonboard()
    {
        if (request()->isMethod("post")) {

            BasicExport::dispatch(['time'=>time()]);

            request()->flash('message_flash','Job Started!');
        }
        return redirect('/admin/homedepot/index');
    }
}