<?php

namespace App\Http\Controllers\Admin\FBS;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Store;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;

trait ShipstationSupplierTrait
{
    protected function setupShipstationRoutes($segment, $routeName, $controller)
    {

        Route::get($segment . '/{id}/shipstation/warehouses', [
            'as' => $routeName . '.ssWarehouses',
            'uses' => $controller . '@ssWarehouses',
            'operation' => 'ssWarehouses',
        ]);
        Route::get($segment . '/{id}/shipstation/warehouses/{warehouseId}/view', [
            'as' => $routeName . '.ssWarehouses.view',
            'uses' => $controller . '@ssWarehousesView',
            'operation' => 'ssWarehouses.view',
        ]);
        Route::any($segment . '/{id}/shipstation/warehouses/{warehouseId}/edit', [
            'as' => $routeName . '.ssWarehouses.edit',
            'uses' => $controller . '@ssWarehousesEdit',
            'operation' => 'ssWarehouses.edit',
        ]);
        Route::get($segment . '/{id}/shipstation/warehouses{warehouseId}/delete', [
            'as' => $routeName . '.ssWarehouses.delete',
            'uses' => $controller . '@ssWarehousesDelete',
            'operation' => 'ssWarehouses.delete',
        ]);
        Route::get($segment . '/{id}/shipstation/packages', [
            'as' => $routeName . '.ssPackages',
            'uses' => $controller . '@ssPackages',
            'operation' => 'ssPackages',
        ]);
        Route::get($segment . '/{id}/shipstation/carriers', [
            'as' => $routeName . '.shipstationCarriers',
            'uses' => $controller . '@shipstationCarriers',
            'operation' => 'shipstationCarriers',
        ]);

        Route::get($segment . '/{id}/shipstation/carriers/assign', [
            'as' => $routeName . '.assigntootheraccount',
            'uses' => $controller . '@assigntootheraccount',
            'operation' => 'assigntootheraccount',
        ]);

        Route::get($segment . '/{id}/shipstation/carriers/remove', [
            'as' => $routeName . '.removefromotheraccount',
            'uses' => $controller . '@removefromotheraccount',
            'operation' => 'removefromotheraccount',
        ]);
        Route::get($segment . '/{id}/shipstation/services', [
            'as' => $routeName . '.ssServices',
            'uses' => $controller . '@ssServices',
            'operation' => 'ssServices',
        ]);
        Route::get($segment . '/{id}/shipstation/stores', [
            'as' => $routeName . '.ssStores',
            'uses' => $controller . '@ssStores',
            'operation' => 'ssStores',
        ]);
        Route::get($segment . '/{id}/shipstation/webhooks', [
            'as' => $routeName . '.ssWebhooks',
            'uses' => $controller . '@ssWebhooks',
            'operation' => 'ssWebhooks',
        ]);
        Route::get($segment . '/{id}/shipstation/addwebhook', [
            'as' => $routeName . '.ssAddwebhook',
            'uses' => $controller . '@ssAddwebhook',
            'operation' => 'ssAddwebhook',
        ]);
        Route::get($segment . '/{id}/shipstation/ssusers', [
            'as' => $routeName . '.ssusers',
            'uses' => $controller . '@ssusers',
            'operation' => 'ssusers',
        ]);
    }

    public function ssusers()
    {
        $settings = new Settings([]);

        $store = Store::where('STOREPK', config('sophio.admin.default_store'))->first();
        $settings->setSettingsByStore($store);
        $shipstationManager = new ShipStationManager($settings);
        $supplier = $this->crud->getCurrentEntry();
        if (request()->get('type') === 'SOPHIOFBS') {


            $shipstationManager->setSupplierCreds(Supplier::where('pk', 753)->first());
        } else {

            $shipstationManager->setSupplierCreds($supplier);
        }


        $shipstationManager->createClient();
        try {
            $users = $shipstationManager->listUsers();
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
            $users = [];
        }
        $SSUsers[] = ['id' => '', 'text' => 'None'];
        foreach ($users as $user) {

            $u = [
                'id' => $user->userId,
                'text' => $user->name . ' ( ' . $user->userId . ' )'
            ];
            if (isset($supplier->SETTINGS['SHIPSTATIONUSERKEY'])) {
                if ($u['id'] == $supplier->SETTINGS['SHIPSTATIONUSERKEY']) {
                    $u['selected'] = true;
                }
            }
            $SSUsers[] = $u;
        }
        return \Response::json(['results' => $SSUsers]);
    }

    public function ssWarehouses()
    {
        $this->topbarSupplier($this->crud->getCurrentEntry());
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        try {
            $shipstationManager->createClient();
            $table = $shipstationManager->listWarehouses();
            $columns = [];
            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
            $table = [];
        }


        $this->crud->setSubheading('Shipstation warehouses');
        return view('admin.datatable', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns]);
    }

    public function ssWebhooks()
    {
        $this->topbarSupplier($this->crud->getCurrentEntry());
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        try {
            $shipstationManager->createClient();
            $table = $shipstationManager->listWebhooks()->webhooks;
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
            $table = [];
        }


        $columns = [];
        if (count($table) > 0) {
            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }

        } else {
            $columns = [];
            $table = [];
        }

        $this->crud->setSubheading('Shipstation webhooks');
        return view('admin.datatable', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns]);
    }

    public function ssAddwebhook()
    {
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        try {
            $shipstationManager->createClient();
            $shipstationManager->createHook($this->crud->getCurrentEntry()->SETTINGS['SHIPSTATIONSTOREID'] ?? null);
            \Alert::success(trans('backpack::crud.update_success'))->flash();
        } catch (\Exception $e) {

            \Alert::failed(trans('backpack::crud.update_success'))->flash();
        }
        return to_route('fbs/supplier.ssWebhooks', ['database' => \Route::current()->parameter('database'), 'id' => \Route::current()->parameter('id')]);
    }

    public function ssWarehousesEdit()
    {
        $this->crud->entry = Supplier::where('PK', \Route::current()->parameter('id'))->first();
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        $shipstationManager->createClient();
        $warehouse = $shipstationManager->getWarehouse(\Route::current()->parameter('warehouseId'));
        return view('admin.fbs.supplier.shipstationWarehousesEdit', ['crud' => $this->crud, 'warehouse' => $warehouse]);
    }

    public function shipstationCarriers()
    {
        $this->topbarSupplier($this->crud->getCurrentEntry());
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        $table = [];
        $columns = [];
        try {
            $shipstationManager->createClient();
            $table = $shipstationManager->listCarriers();

            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
        }


        $this->crud->setSubheading('Shipstation carriers');
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });

        $customers = Customer::whereIn('pk', $custs)->get();
        return view('admin.fbs.supplier.shipstationCarriers', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns, 'supplier' => $this->crud->entry, 'customers' => $customers]);
    }

    public function assigntootheraccount()
    {
        if (request()->get('shippingProviderId') !== null && request()->get('carrier', '') !== '') {
            $s = Supplier::where('PK', $this->crud->getCurrentEntry()->PK)->first();
            if (request()->get('custpk', '') !== '') {
                $s->SETTINGS[ssCarrierCodeToShort(request()->get('carrier')) . 'OTHERACCOUNT' . request()->get('custpk')] = request()->get('shippingProviderId');
            } else {
                $s->SETTINGS[ssCarrierCodeToShort(request()->get('carrier')) . 'OTHERACCOUNT'] = request()->get('shippingProviderId');
            }

            $s->update();
            \Alert::success(trans('backpack::crud.update_success'))->flash();
        } else {
            \Alert::failed(trans('backpack::crud.update_success'))->flash();
        }
        return back();
    }

    public function removefromotheraccount()
    {
        if (request()->get('carrier', '') !== '') {
            $s = Supplier::where('PK', $this->crud->getCurrentEntry()->PK)->first();
            if (request()->get('custpk', '') !== '') {
                $s->SETTINGS[ssCarrierCodeToShort(request()->get('carrier')) . 'OTHERACCOUNT' . request()->get('custpk')] = "";
            } else {
                $s->SETTINGS[ssCarrierCodeToShort(request()->get('carrier')) . 'OTHERACCOUNT'] = "";
            }
            $s->update();
            \Alert::success(trans('backpack::crud.update_success'))->flash();
        } else {
            \Alert::failed(trans('backpack::crud.update_success'))->flash();
        }
        return back();
    }

    public function ssPackages()
    {

        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        $table = [];
        $columns = [];
        $this->topbarSupplier($this->crud->getCurrentEntry());
        try {
            $shipstationManager->createClient();
            $tableCarriers = $shipstationManager->listCarriers();


            if (request()->get('carrier', '') !== '') {
                $table = $shipstationManager->listPackages(request()->get('carrier'));
            } else {
                foreach ($tableCarriers as $row => $carrier) {
                    $table = array_merge(array_values($table), array_values($shipstationManager->listPackages($carrier->code)));
                }
            }

            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
        }


        $this->crud->setSubheading('Shipstation packages');
        return view('admin.datatable', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns]);
    }
    public function ssStores()
    {
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        $table = [];
        $columns = [];
        $this->topbarSupplier($this->crud->getCurrentEntry());
        try {
            $shipstationManager->createClient();
            $table = $shipstationManager->listStores();




            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }
        } catch (\Exception $e) {
            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }
        }


        $this->crud->setSubheading('Shipstation stores');
        return view('admin.datatable', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns]);
    }
    public function ssServices()
    {
        $this->topbarSupplier($this->crud->getCurrentEntry());
        $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $shipstationManager->setSupplierCreds($this->crud->getCurrentEntry());
        $table = [];
        $columns = [];
        try {
            $shipstationManager->createClient();
            $tableCarriers = $shipstationManager->listCarriers();

            if (request()->get('carrier', '') !== '') {
                $table = $shipstationManager->listServices(request()->get('carrier'));
            } else {
                foreach ($tableCarriers as $row => $carrier) {

                    $table = array_merge(array_values($table), array_values($shipstationManager->listServices($carrier->code)));

                }
            }
            foreach ($table[0] as $column => $value) {
                $columns[] = $column;
            }
        } catch (\Exception $e) {
            report($e);

            if ($e->getCode() == 401) {
                return view('admin.error', ['message' => 'Shipstation returned a 401 error code. This is usually caused by wrong API credentials.']);
            } else {
                return view('admin.error', ['message' => $e->getMessage()]);
            }

        }

        $this->crud->setSubheading('Shipstation services');
        return view('admin.datatable', ['crud' => $this->crud, 'table' => $table, 'columns' => $columns]);
    }

}