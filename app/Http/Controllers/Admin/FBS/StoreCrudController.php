<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Library\Sophio\Traits\AuditOperation;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Store;

class StoreCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use AuditOperation;
    protected $advset=[
        'FORCENOSTOCKIFCOSTEXCEEDSOW',
        'FULFILLMENTNOTESHOLD',
        'WALMARTERROREMAILS',
        'MANAGERUSERID',
        'DISABLEFULFILLMENTHOURS',
        'LISTCANCELLEDORDERSSHIPSTATION',
        'HIDENOSTOCK',
        'LOCALWWSWAREHOUSES',
        'WHICQ',
        'SENDTRACKINGEMAILS',
        'TEXTPHONE',
        'TWILIOSRC',
        'TWILIOAUTHTOKEN',
        'TWILIOAUTHID',
        'S2FA',
        'DISABLEAUTOSENDB2B',
        'DISABLEAUTOSENDAPS',
        'DISABLEAUTOSENDEB',
        'DISABLEAUTOSENDAMZ',
        'DISABLEAUTOSENDMIT',
        'DISABLEAUTOSENDNAT',
        'DISABLEAUTOSENDAPI',
        'INSUREFIRSTCLASS',
        'FIRSTCLASSFILTER',
        'SHIPSTODAYMESSAGE',
        'SPARKLINKWARRANTDURATION',
        'B2BPRICEFIELD',
        'NEXPARTB2BCOMMENTETA2',
        'NEXPARTB2BCOMMENTETA1',
        'NEXPARTB2BMANUALFULFILLMENT',
        'NEXPARTB2BDEMO',
        'EBAYDISABLEPRODUCTANDVEHICLEIMAGES',
        'EBAYSENDVEHICLEIMAGES',
        'EBAYURGIDS',
        'CORCENTRICPREFIX',
        'MAXDEFECTDAYS',
        'AMAZONSTOREID',
        'AMAZONPAY',
        'CORCOMMUNITYCODE',
        'CORCENTRICENDPOINT',
        'STRIPEVERSION',
        'PAYBYSTRIPE',
        'PAYPALLOGOURL',
        'SOPHIOPLATFORMPAYPALCLIENTID',
        'CLIENTSELFAPPROVEB2B',
        'AMAZONURGSELLPRICE',
        'ENDOFDAYEMAIL',
        'POWERLINK2APIKEY',
        'POWERLINK2PURGID',
        'POWERLINK2P',
        'POWERLINK2U',
        'POWERLINKDEMO',
        'POWERLINK2',
        'EBAYURGSELLPRICE',
        'REQUIRELOGINFORCONTACTUS',
        'EBAYSUBTITLE',
        'BELOWCOSTTHRESHHOLD',
        'ZORORETURNALLOWANCE',
        'ZOROFORMULAQTY',
        'ZOROSERVERHOSTKEY',
        'ZOROSELLERNAME',
        'ZOROGSDID',
        'ZOROLOG',
        'TRACTORRETURNALLOWANCE',
        'TRACTORCUSTPK',
        'TRACTORFORMULAQTY',
        'TRACTORSERVERHOSTKEY',
        'TRACTORFTPPASSWORD',
        'TRACTORFTPUSER',
        'TRACTORFTPLINK',
        'TRACTORFTPSEND',
        'TRACTORDBFFOLDER',
        'TRACTORRECEIVERID',
        'TRACTORVENDORNUMBER',
        'TRACTORSELLERNAME',
        'TRACTORSELLERID',
        'TRACTORGSDID',
        'TRACTORLOG',
        'TRACTORDEMO',
        'CHANNELSPYDERAPI',
        'WHILICENSEPLATESEARCH',
        'LIMITRESULTS',
        'DONTSENDEMAIL',
        'AREMAIL',
        'DONOTUPDATEDCFFROMWHI',
        'TRACTORSUPPLYRETURNALLOWANCE',
        'TRACTORSUPPLYMANUALFULFILLMENT',
        'TRACTORSUPPLYCUSTPK',
        'TRACTORSUPPLYFORMULAQTY',
        'TRACTORSUPPLYFTPSEND',
        'TRACTORSUPPLYSHIPNODE',
        'TRACTORSUPPLYCHANNELTYPE',
        'TRACTORSUPPLYPRIVATEKEY',
        'TRACTORSUPPLYCONSUMERID',
        'TRACTORSUPPLYLOG',
        'TRACTORSUPPLYDEMO',
        'WALMARTRETURNALLOWANCE',
        'HOMEDEPOTRETURNALLOWANCE',
        'EBAYURGGRADES',
        'WALMARTPRICETESTFEED',
        'ACCOUNTINGEMAIL',
        'WALMARTPRICEALLITEMS',
        'CARTBASEDSHIPTYPE',
        'MYSQLTABLESTOSYNCHRONIZE',
        'WALMARTSENDALLITEMS',
        'OPENORDERSBYCUSTTYPE',
        'AUTOSENDBUSINESSHOURSONLY',
        'NOSTOCKLABELRTSC',
        'MAINTENANCE',
        'RETURNWHSEEMAIL',
        'WALMARTONLYITEMSINSTOCK',
        'WALMARTMANUALFULFILLMENT',
        'WALMARTCUSTPK',
        'WALMARTFORMULAQTY',
        'WALMARTFTPSEND',
        'WALMARTSHIPNODE',
        'WALMARTCHANNELTYPE',
        'WALMARTPRIVATEKEY',
        'WALMARTCONSUMERID',
        'WALMARTLOG',
        'WALMARTDEMO',
        'MITCHELLMANUALFULFILLMENT',
        'MITCHELLDEMO',
        'CHUBSETDISCONTINUED',
        'CHUBMINIMUMSTOCK',
        'EBAYSELLREMANINVENTORY',
        'DEFAULTCUSTTYPE',
        'NOEMAILTOOWNER',
        'DHLPASSWORD',
        'DHLUSERNAME',
        'DHL',
        'NEXPARTDEMO',
        'URGHIDELOCATION',
        'FITMENTENGINE',
        'SUBCATALOGTYPE',
        'STATEMENTVARIANCEEMAIL',
        'MAILCHIMPEMAIL',
        'EMAILANSWER',
        'EMAILQUESTION',
        'MYSQLPRICINGDB',
        'HIDERETURNMODULE',
        'RETURNSEMAIL',
        'SOPHIOORDERSAPI',
        'NEWACCOUNTEMAIL',
        'USEMINASMAXINSHIPPING',
        'SHIPENGINEAPIKEYSANDBOX',
        'SHIPENGINEAPIKEY',
        'SHIPENGINETEST',
        'SHIPENGINE',
        'UPSAPIPASSWORD',
        'UPSBILLINGZIPCODE',
        'FEDEXENABLE',
        'ROCKETSHIP',
        'FEDEXACCOUNTNUMBER',
        'FEDEXAPIKEY',
        'FEDEXAPIPASSWORD',
        'FEDEXMETERNUMBER',
        'FEDEXVERSION',
        'ROCKETSHIPAPIKEY',
        'FEDEXBILLINGZIPCODE',
        'FEDEXSMARTPOSTHUBID',
        'UPSACCTNUMBER',
        'STAMPSPASSWORD',
        'STAMPSUSERNAME',
        'USPSAPIUSERNAME',
        'USPSAPIPASSWORD',
        'ORDERTEXTMESSAGE',
        'AUTOREFRESHADMIN',
        'CORCENTRICCODE',
        'CORVENDORCODE',
        'CORCENTRICPASSWORD',
        'CORCOMMMUNITYCODE',
        'CORCENTRICUSERNAME',
        'PAYPALAPIPLATFORMPAYEE',
        'PAYPALAPISECRET',
        'PAYPALAPIACCOUNT',
        'PAYPALAPICLIENTID',
        'PAYPALMERCHANTSECRET',
        'UPDATEDCFFROMWHI',
        'ORDERNOSTOCKITEMS',
        'CSRUSERIDS',
        'OVERRIDEUSERIDS',
        'HIDEBATTERIESWITHNOSTOCK',
        'HIDEPRODUCTLOCATION',
        'STOCKCHECKMESSAGE',
        'NOSTOCKADDTOCARTMESSAGE',
        'AAAADDITEMSTOCKCHECK',
        'SQLADDITEMSTOCKCHECK',
        'ACCEPTBACKORDERS',
        'SPECORDLINKAFTERSUBMIT',
        'DCFSUBGROUPY',
        'MINSTOCKFORBUYBUTTON',
        'DONTNOTIFYAFFILIATEORDERS',
        'OPENORDERSTATUSES',
        'HOLDLINECODES',
        'CHECKRETURNSFORSTOCK',
        'SETPACKITEMSINVENTORYTOZERO',
        'SHIPSTATIONSUREPOST',
        'HOMEDEPOTCUSTPK',
        'SETCOREINVENTORYTOZERO',
        'URGSORTBY',
        'FAILOVER',
        'CATALOGTYPE',
        'CATEGORYIMAGEPATH',
        'VERIFICATIONMESSAGE',
        'TAXSTATES',
        'EXCLUDESHIPPINGFROMCUSTTYPES',
        'EXCLUDETAXESFROMCUSTTYPES',
        'SHIPSTATIONRATING',
        'UPDATEPRODUCTWAREHOUSERTSC',
        'FAILEDDATAFEEDEMAILS',
        'SKIPEMAILATCHECKOUT',
        'HIDEPARTNUMBERLIST',
        'REQUIREMOBILE',
        'CHECKOUTPCI',
        'SHOWALLDELIVERYMETHODSINCHECKOUT',
        'COUNTRIESSERVICED',
        'REPLYTOEMAIL',
        'SELLITEMSWITHMAPPRICING',
        'AMAZONSELLERSKUTEMPLATE',
        'AVERAGESHIPPING',
        'SHIPSTATIONPROFILEPK',
        'WHADDDISCOUNTIDTOCOMMENT',
        'ZOROCUSTPK',
        'ZOROFTPCSVMODE',
        'ZOROFTPSEND',
        'ZOROONBOARDING',
        'ZORODEMO',
        'ZOROVENDORNUMBER',
        'ZOROSELLERID',
        'ZORORECEIVERID',
        'ZOROFTPLINK',
        'ZOROFTPUSER',
        'ZOROFTPPASSWORD',
        'ZORODBFFOLDER',
        'SENDGRIDAPIKEY',
        'PHONEASPONUMBER',
        'DISABLEUNPAIDWARNINGEMAILS',
        'MOBILE',
        'TFR',
        'PARTNERID',
        'WHMCSSUPPORTPAGE',
        'GA_TAGMANAGERID',
        'GA_UNIVERSAL',
        'GA_ECOMMERCE',
        'GA_SITETAG',
        'GA_HIDE',
        'CHUBNONEWORDERS',
        'HIDEDISCOUNTS',
        'EMAILCLIENTSERVICES',
        'CHUBOPERATIONTYPE',
        'HTTPGETNOTDOTNET',
        'CHECKOUTMIAMI',
        'LOGSHIPPING',
        'AAAFACETEDCATALOG',
        'AAAFACETEDPRICING',
        'AAAWDID',
        'AAAWHSE',
        'ABANDONAIDURL',
        'LINEITEMSPLITTHRESHHOLD',
        'ACESFACETEDCATALOG',
        'ADDITEMDEFSUPPLIER',
        'ADDOFFLINEINVTOBROKER',
        'ADDSHIPPINGTOPO',
        'ADDTHISUSERID',
        'ADMINPASS',
        'ADMINPASSCHANGED',
        'ADMINPASSCHANGEDBYIP',
        'ADMINUSER',
        'AFFILIATEFLATSHIPPINGRATE',
        'AFFILIATEFLATSHIPPINGRATESTORES',
        'AFFILIATEID',
        'AIRBORNEACCTNUMBER',
        'ALERTINVSTATUSCODES',
        'ALLOWEDSUPPLIERS',
        'ALLOWOVERSELL',
        'ALLOWPRODSUBSTITUTION',
        'ALTERNATECURRENCY',
        'ALTERNATECURRENCYNAME',
        'AMAZONAWSACCESSKEYID',
        'AMAZONFULFILLMENTLATENCY',
        'AMAZONLOG',
        'AMAZONMARKETPLACEID',
        'AMAZONMWSAUTHTOKEN',
        'AMAZONPURGEANDREPLACE',
        'AMAZONSECRETKEY',
        'AMAZONSELLERID',
        'AMAZONSTOCKTHRESHOLDSOP',
        'AMAZONSTOCKTHRESHOLDURG',
        'AMERICAN',
        'ANETDELIMIT',
        'APIACESCATALOGCLIENTID',
        'APIACESINTERNALSERVICEADDRESS',
        'APIACESSERVICEADDRESS',
        'APIFACETEDCATALOGUUID',
        'APIKEY',
        'APOFPO',
        'AUTHORIZENETSERVERURL',
        'AUTHORIZETYPE',
        'AUTOCREATESKU',
        'AUTOMATEDPICKUP',
        'AUTOSENDCUSTTYPES',
        'AUTOSENDMINUTESDELAY',
        'AUTOSENDORDER',
        'AUTOSENDRULE',
        'AUTOSENDTOCLOSESTBRANCHFORSUP',
        'B2BTAX',
        'B2CCENTRALCLIENTRESPONSE',
        'B2CTAX',
        'BABACCOUNT',
        'BABBUYERID',
        'BABDEMO',
        'BABLOG',
        'BABSELLERID',
        'BABURL',
        'BACKGROUNDIMAGE',
        'BONGOUSURL',
        'BROKERSTORE',
        'BUILDOECMFR ',
        'BUILDOECMFR',
        'BUYERBROWSESTOCKINFO',
        'CERTIFICATIONAGREEMENT',
        'CERTIFICATIONEMAIL',
        'CERTIFICATIONNAME',
        'CHECKSTOCK',
        'CKEDITOR',
        'CLIENTSELFAPPROVEB2C',
        'CMSREADY',
        'CONFIGBACKEDUP',
        'CONFIGCHANGENAME',
        'CONFIGPK',
        'CONFIGREPLACEDBY',
        'CONFIGREPLACEDBYIP',
        'CONFIGRESTORED',
        'CONFIGRESTOREDBY',
        'CONFIGRESTOREDBYIP',
        'CONTACTUSEMAILADDRESS',
        'CUSTOMPAYMENT',
        'CUSTOMPAYMENTDOMAIN',
        'CUSTOMPAYMENTNAME',
        'CUSTOMPAYMENTPAGE',
        'CUSTOMPAYMENTRULES',
        'CUSTOMS_AMOUNT',
        'DATABASETYPE1',
        'DATABASETYPE3',
        'DATABASETYPE4',
        'DCIACCOUNT',
        'DCIJSURL',
        'DCIVERSION',
        'DDG1',
        'DDG2',
        'DDG3',
        'DDG4',
        'DDG5',
        'DEFAULTSHIPDESC',
        'DEFAULTWARRANTY',
        'DEFINEDLANGUAGES',
        'DEMO',
        'DESIGNMASTERTEMPLATEPERSIST',
        'DISABLEAUTOMATEDEBAYRETURNS',
        'DISABLECUSTNOTESTOWD',
        'DISABLEDAUTOSENDLINECARD',
        'DISABLEDEVELOPEREMAIL',
        'DISABLEEMAILREGISTRATIONNOTIFICATIONS',
        'DISABLEIPNPOSTBACK',
        'DISABLEMINQTY',
        'DISABLEMULTIFREIGHT',
        'DISABLESECURELOGIN',
        'DISCOVER',
        'DISPLAYALWAYSTAXSHIP',
        'DOMAINEXTENSION',
        'DOMAINNAME',
        'DONOTUPDATESTOCK',
        'DONTINCLUDEB2CCENTRALINAMAZONFEED',
        'DONTINCLUDEB2CCENTRALINEBAYFEED',
        'DROPSHIP',
        'EBAYAUTHTOKEN',
        'EBAYCATALOGINVENTORY',
        'EBAYCHANGEPRICETO4999HIDE',
        'EBAYDEMO',
        'EBAYEXCLUDEDLINECODES',
        'EBAYFEEDDONTSENDABOUTBRAND',
        'EBAYFEEDDONTSENDABOUTPART',
        'EBAYFEEDDONTSENDSUBTITLE',
        'EBAYFEEDDONTSENDTITLE',
        'EBAYFEEDSUPPLIERS',
        'EBAYFEEDTYPETITLE',
        'EBAYFEEDWITHOUTWEIGHTS',
        'EBAYFORMULAQTY',
        'EBAYFORMULASELLPRICE',
        'EBAYFTPFEEDLOC',
        'EBAYFTPFEEDPW',
        'EBAYFTPFEEDUSER',
        'EBAYFTPSEND',
        'EBAYINCLUDEDLINECODES',
        'EBAYLOG',
        'EBAYMAXDIMWEIGHT',
        'EBAYNUMBEROFDAYS',
        'EBAYPRICETHRESHHOLD',
        'EBAYSELLERUSERID',
        'EBAYSELLPRICEMIN',
        'EBAYSENDORDERFULFILLMENTCONFIRMATIONS',
        'EBAYSENDZEROSTOCK',
        'EBAYSESSIONID',
        'EBAYSTOCKMAX',
        'EBAYSTOCKTHRESHOLD',
        'EBAYSTOCKTHRESHOLDAAA',
        'EBAYSTOCKTHRESHOLDFMP',
        'EBAYSTOCKTHRESHOLDGWC',
        'EBAYSTOCKTHRESHOLDIMC',
        'EBAYSTOCKTHRESHOLDMET',
        'EBAYSTOCKTHRESHOLDPAR',
        'EBAYSTOCKTHRESHOLDSOP',
        'EBAYSTOCKTHRESHOLDURG',
        'EBAYUSEPREFERREDB2CCENTRAL',
        'EBAYWHIINVENTORYFEEDFORMAT',
        'EMAILFOOTER',
        'EMAILHEADER',
        'EPICORFTPLOCATION',
        'EPICORFTPPASSWORD',
        'EPICORFTPUSERID',
        'EPICORLOCATIONNUMBER',
        'EPICORWDCODE',
        'EXACTMATCHRESULTSONLY',
        'EXPMINFEE',
        'EXPORTORDERTOXMLURL',
        'EXPPERC',
        'EXPRESSFEE',
        'FBS',
        'FEATUREDBRAND',
        'FEATUREDCATEGORY',
        'FEATUREDLABEL',
        'FEATUREDMAKE',
        'FIRSTNAME',
        'FIRSTTIMEBUYERDISCOUNTID',
        'FORCEPRICETO99',
        'FORCEUPPER',
        'FREEFREIGHTTHRESHHOLD',
        'FREEGROUNDSHIPPING',
        'FREESHIPPINGTEXT',
        'FTPCREATED',
        'FTPIP',
        'FTPLOCATION',
        'FTPPASS',
        'FTPUSER',
        'FTPWHITELISTIP',
        'FULFILLMENTHALTSTATUS',

        'GMTOFFSET',

        'GWUSERID',
        'HANDLINGFEE',
        'HASWDPRODFILE',
        'HIDEALTERNATES',
        'HIDEALTTAGS',
        'HIDEANETSEAL',
        'HIDECARRIER',
        'HIDECCONINTLORDERS',
        'HIDEIICON',
        'HIDEINSTOREPICKUPMESSAGING',
        'HIDEINVSTATUSOPENORD',
        'HIDELINKFORALTERNATES',
        'HIDENEARMATCH',
        'HIDEORDEROVERRIDE',
        'HIDESHIPTOHOME',
        'HIDESTOCKINFO',
        'HIDEVINSEARCH',
        'HIDEWEIGHT',
        'HORIZONTALNAVIGATION',
        'HVTACCOUNT',
        'HVTITEMSPERPAGE',

        'IGNOREITEMFORM',
        'IMCACCTNO',
        'IMCACESFILTERCATEGORIES',
        'IMCACESOPTIONSCONTAINER',
        'IMCACESOPTIONSLAYOUT',
        'IMCATTRIBUTEFILTERING',
        'IMCCLIENTTYPE',
        'IMCDISABLEKITS',
        'IMCIPOVERSION',
        'IMCLOCALCATALOG',
        'IMCLOG',
        'IMCPRICEACCOUNT',
        'IMCPWD',
        'IMCUID',
        'IMCVERSION2',
        'IMCVERSION',
        'INCLUDELOCALITEMS',
        'INCREMENTALSEARCH',
        'INSTOCKLABEL',
        'INSTOREPICKUP',
        'INVENTORYBUILDERPK',
        'INVENTORYBUILDERTASK',
        'IPOACCOUNT',
        'IPOBUYERID',
        'IPODEMO',
        'IPOLOG',
        'IPOSELLERID',
        'IPOURL',
        'IPOVERSION',
        'KEYACCOUNT',
        'KEYDISABLESELECTALL',
        'KEYGETCOSTFORPARTS',
        'KEYINCLUDEEXOTICMAKES',
        'KEYINCLUDEUNCOMMONMODELS',
        'KEYINCLUDEUNIVERSALPARTS',
        'KEYMAXROWSOFPARTS',
        'KEYUSERID',
        'LASTDOWNLOAD',
        'LASTNAME',
        'LINKEDINURL',
        'LOCALDCF',
        'LOGFREIGHT',
        'LOGPRICE',
        'LOGSQL',
        'MAILCC',
        'MAILFEMAIL',
        'MAILFROM',
        'MAILINGLISTONCART',
        'MARKUPLISTWHENNEEDED',
        'MASTERCARD',
        'MATCHPRICES',
        'MAXPOLENGTH',
        'MEMBERSHIPPRICE',
        'METACCOUNT',
        'METDEMO',
        'METLOG',
        'METORDERPASSWORD',
        'METVERSION',
        'METWDID',
        'METWHSE',
        'MINIMUMSTOCKFORB2CCENTRALAFFILIATES',
        'MONEYORDER',
        'MONEYORDERNOTE',
        'MULTICURRENCY',
        'MYSQLDATACENTER',
        'MYSQLDBF',
        'NOCHECKOUT',
        'NOFREESHIPPINGTEXT',
        'NOIMAGEURL',
        'NOLOGINONCHECKOUT',
        'NOPRICETEXT',
        'NOSHIPTO',
        'NOSTOCKLABEL',
        'NOSTOCKLABELBRANCH',
        'OEAACCOUNT',
        'OEACLIENTTYPE',
        'OEAFACETEDCATALOG',
        'OEAFACETEDPRICING',
        'OEAITEMSPERPAGE',
        'OECAUTHKEY',
        'OECBRANCH',
        'OECBRANCHNAME',
        'OECCLIENTTYPE',
        'OECDELMETHOD',
        'OECFACETEDCATALOG',
        'OECFACETEDPRICING',
        'OECMULTICATALOG',
        'OECPASSWORD',
        'OECPROVIDER',
        'OECSHIPTYPE',
        'OECUSEMFGCODEONSTOCKCHECK',
        'OECUSERNAME',
        'OECWDID',
        'OECWDPASSWORD',
        'OECWDUSERNAME',
        'OECWHSE',
        'OEPARTSDB',
        'OESPHINXINDEX',
        'OFFLINEINV',
        'ONEALL',
        'ONEALLUSERID',
        'ONEPAGECHECKOUT',
        'OPENACCOUNT',
        'OPENORDERSORT',
        'OPTINFALSE',
        'ORDERALERTEMAIL',
        'ORDERLIMIT',
        'ORDERLIMITMESSAGE',
        'ORDERNUMBER',
        'ORDERPRINTFIELDLIST',
        'OVERSELLLABEL',
        'PARACCOUNT',
        'PARLOG',
        'PARORDERPASSWORD',
        'PARORDERUSERNAME',
        'PARTDISPLAYPAGE',
        'PARTNUMBERDISPLAYFIELD',
        'PARTPAGELAYOUT',
        'PARTSAUTHORITYDEMO',
        'PARTSLISTINGWIDTH',
        'PARWDID',
        'PARWHSE',
        'PAYINSTORE',
        'PAYPAL',
        'PAYPALACCOUNT',
        'PAYPALCURRENCYCODE',
        'PAYPALMAXMONEYSEND',
        'PAYPALMODE',
        'PAYPALPWD',
        'PAYPALRESERVEONIMPORT',
        'PAYPALSIG',
        'PAYPALUID',
        'PID',
        'PORTPOSTALCODE',
        'PPIPNLOGGING',
        'PRICEBYZIP',
        'PRICEOWNITEMS',
        'PRODUCT_WAREHOUSE',
        'QUOTEONLY',
        'RATEXCHANGE',
        'REMOVELEADINGZEROS',
        'REQUIREEMAIL',
        'REQUIREPHONE',
        'REQUIRESHIPPHONE',
        'RESELLERRATINGSID',
        'RETURNADDRESS1',
        'RETURNADDRESS2',
        'RETURNADDRESS3',
        'RETURNCHARGE',
        'RETURNCITY',
        'RETURNCOMPANYNAME',
        'RETURNCONTACTNAME',
        'RETURNEMAIL',
        'RETURNOWNERNOTES',
        'RETURNPHONE',
        'RETURNPOSTAL',
        'RETURNSAUTOAPPROVE',
        'RETURNSREQUIRELOGIN',
        'RETURNSRMAOVERRIDE',
        'RETURNSTATEPROVINCE',
        'SALESADMINPASS',
        'SALESADMINUSER',
        'SCREENCONFIRMATIONHIDECREDENTIALS',
        'SEARCHITEMNEARMATCH',
        'SEARCHITEMNOWD',
        'SELLBELOWCOST',
        'SENDB2BORDERS',
        'SENDDISCOUNTTOWD',
        'SENDFREIGHTTOWD',
        'SENDITEMPACKAGEQUANTITYMORETHAN1TOAMAZON',
        'SENDITEMPACKAGEQUANTITYMORETHAN1TOEBAY',
        'SENDMAILPASSWORD',
        'SENDMAILPORT',
        'SENDMAILSSL',
        'SENDMAILUSERNAME',
        'SENDMONEYVIAPAYPAL',
        'SENDORDERTOCLIENT',
        'SENDPAIDORDERSONLY',
        'SENDRMAREQUESTSTOSUPPLIER',
        'SENDTAXTOWD',
        'SENDTAXTOWDASRATE',
        'SENDTRACKINGNUMBERTOMARKETPLACELOG',
        'SESSIONCLASS',
        'SFTP',
        'SHIPPERSTATEOVERRIDE',
        'SHIPPERZIPOVERRIDE',
        'SHIPPINGAVERAGECOST',
        'SHIPRANGEVALUE',
        'SHIPSTATIONALLCCRESULT',
        'SHIPSTATIONNOTFULFILLED',
        'SHIPSTATIONUSER',
        'SHIPSTATIONPASSWORD',
        'SHIPSTATIONSOURCE',
        'SHIPSUSAINTL',
        'SHOPPINGFEEDTASK',
        'SHOWAPOFPOHELPLINK',
        'SHOWBRANCHINVENTORY',
        'SHOWCASE_H1',
        'SHOWDISCOUNTCODE',
        'SHOWMETHEPARTSDEV',
        'SHOWOUTOFSTOCKITEMS',
        'SHOWQTYOH',
        'SHOWSAVECART',
        'SITEMAP',
        'SITENAME',
        'SKIPALTBRANCHQTY',
        'SMPACCOUNTID',
        'SOCIALDISCOUNTID',
        'SONUMBER',
        'SOPHIOCENTRALURL',
        'SOPHIOENTRALURLERROR',
        'STATETAX1',
        'STORAGE',
        'STORECCINFO',
        'STOREHOURS',
        'STOREPHONE',
        'SUPPLIER',
        'SUPPLIEREMAIL',

        'TAXBYCOUNTY',
        'TAXEXEMPT_TRESHHOLD',
        'TESTSKU',
        'TFO',
        'TODOLIST',
        'TRUSTPILOTORDERS',
        'UNIQUEITEMRULE',
        'UNVACCOUNT',
        'UNVITEMSPERPAGE',
        'UPDATEEBAYNEWORDERS',
        'URGCATALOGLOOKUPPROXY',
        'URGFACETEDCATALOG',
        'URGFACETEDPRICING',
        'URGID',
        'URGIMAGEURLOVERRIDE',
        'URGPARTSDB',
        'URGPARTSURL',
        'URGSPHINXCLIENTID',
        'URGSPHINXINDEX',
        'USEAJAXONPARTLISTING',
        'USECSQTY',
        'USEINTERCHANGE',
        'USEMARKUPCEILING',
        'USEMARKUPCEILINGV2',
        'USEPRODUCTXML',
        'USERATEXCHANGEONSHIPPING',
        'USERATEXCHANGEONSIHPPING',
        'USEWHDPRICEWHENNOSTOCK',
        'USPSACCTNUMBER',
        'VISA',
        'WDDISCOUNTLINECODE',
        'WDDISCOUNTSKU',
        'WDFREIGHTBRANCH',
        'WDFREIGHTLINECODE',
        'WDFREIGHTSKU',
        'WDPASSWORD',
        'WDTAXBRANCH',
        'WDTAXLINECODE',
        'WDTAXSKU',
        'WDUSERNAME',
        'WELCOME',
        'WHBRANCH',
        'WHBRANCHNAME',
        'WHCACHETYPE',
        'WHCLIENTTYPE',
        'WHDADDITEMSTOCKCHECK',
        'WHDADDWONUMBER',
        'WHDBOTBLOCKER',
        'WHDCFAPPEND',
        'WHDCFBRANCHNO',
        'WHDCFTYPE',
        'WHDDCFCREDENTIALS',
        'WHDELMETHOD',
        'WHDFACETEDCATALOG',
        'WHDFACETEDPRICING',
        'WHDFACETEDSEARCH',
        'WHDISABLEWAIT',
        'WHDISTRIBUTORNAME',
        'WHDISTRIBUTORNO',
        'WHDMATCHMFGCODESKUONLY',
        'WHDMATCHORDLINEBYPOSITION',
        'WHDNOCACHE',
        'WHDORDERVERSION',
        'WHDPGPSNOUPDATE',
        'WHDSENDFREIGHTTOTALINHEADER',
        'WHDSENDLOG',
        'WHDUNFORMATSKU',
        'WHDUSELINECODEONSTOCKCHECK',
        'WHDUSEVIEWONLYRESULTS',
        'WHDWDID',
        'WHDWDSENDSHIPTO',
        'WHDWHSE',
        'WHFILLTYPE',
        'WHHIDEPARTSEARCH',
        'WHIACCT',
        'WHIEBAYFEEDFILENAME',
        'WHILOCALCATALOG',
        'WHITEMSPERPAGE',
        'WHIUSELINECODEASMFGCODE',
        'WHMCSEMAIL',
        'WHMCSLASTORDER',
        'WHMCSPASS',
        'WHOFFLINEPRICETABLE',
        'WHOFFLINEPRICEURL',
        'WHORDERDEMO',
        'WHORDERPASSWORD',
        'WHORDERSOAPURL',
        'WHORDERUSERNAME',
        'WHORDERVERSION',
        'WHPARTSSORTORDER',
        'WHPARTSSORTTYPE',
        'WHPASSWORD',
        'WHPRICEPASSWORD',
        'WHPRICEUSERNAME',
        'WHPRICINGPASSWORD',
        'WHPRICINGUSERNAME',
        'WHPROVIDER',
        'WHREMAPLINECODE',
        'WHREMOVENOFITEMS',
        'WHSHIPMETHASGENERAL',
        'WHSHIPTAXCOMMENT',
        'WHSHIPTAXCOMMENTAUTOEASE',
        'WHSHIPTYPE',
        'WHSOAPURL',
        'WHUSERNAME',

        'WWSITEMSUPLOADASYN',
        'SENDEMAILSYNC',
        'DISABLEPENDINGORDEREMAILS',
        'AMAZONUSEPREFERREDB2CCENTRAL',
        'AMAZONSENDORDERFULFILLMENTCONFIRMATIONS',
        'AMAZONFORMULASELLPRICE',
        'AMAZONFORMULAQTY',
        'AMAZONPRICETHRESHHOLD',
        'AMAZONFTPSEND',
        'AMAZONMAXDIMWEIGHT',
        'AMAZONINCLUDEDLINECODES',
        'AMAZONEXCLUDEDLINECODES',
        'AMAZONSELLPRICEMIN',
        'AMAZONSTOCKMAX',
        'AMAZONSTOCKTHRESHOLD',
        'AMAZONFEEDSUPPLIERS',
        'AMAZONSTOCKTHRESHOLDAAA',
        'AMAZONSTOCKTHRESHOLDIMC',
        'AMAZONSTOCKTHRESHOLDPAR',
        'AMAZONSTOCKTHRESHOLDGWC',
        'AMAZONSTOCKTHRESHOLDMET',
        'AMAZONSTOCKTHRESHOLDFMP',
        'AMAZONTRANSFEE',
        'AMAZONSENDZEROSTOCK',
        'EBAYTRANSFEE',
        'AFMKTITEMDETAILBUYPANELSTYLE',
        'URGPARTLISTLAYOUT',
        'OWNFACETEDCATALOG',
        'BLOCKINTLVISITORS',
        'SHOWLOCALDELIVERY',
        'FREIGHTCHGSINGLE',
        'PAYPALCHECKOUTMESSAGE',
        'PAYPALTRANSFEE',
        'STRIPETRANSFEE',
        'B2BTRANSFEE',
        'TRATRANSFEE',
        'NATTFR',
        'TFR',
        'AMAZONTRANSFEE',
        'EBAYTRANSFEE',

        'PAYCOD',
        'DISABLECUSTOMERREGISTRATION',
        'HP',
        'HYPERPAYDEMO',
        'HYPERPAYCURRENCY',
        'HYPERPAYUSERID',
        'HYPERPAYPASSWORD',
        'HYPERPAYENTITYID',
        'IGLOBAL',
        'IGLOBALSTOREID',
        'IGLOBALLOGGING',
        'IGLOBALSERVICESPASSWORD',
        'IGLOBALSUBDOMAIN',
        'IGLOBALBOXMARKUP',
        'IGLOBALDEFAULTBOXSIZE',
        'STRIPETESTSECRETKEY',
        'STRIPETESTPUBLISHABLEKEY',
        'STRIPELIVESECRETKEY',
        'STRIPELIVEPUBLISHABLEKEY',
        'STRIPECURRENCY',
        'STRIPEDEMO',
        'AUTOSENDPAYINSTORE',
        'OFFLINEINVDEBUG',
        'SOPHIOAPI',
        'FACETEDCATALOGHIDEPARTTYPEFROMWIDGET',
        'DEBUGFACETEDCATALOG',
        'CATALOGUPDATEINPROGRESS',
        'AFMKTCATEGORYFILTER',
        'AFMKTSUBCATEGORYFILTER',
        'AFMKTPARTTYPEFILTER',
        'AFMKTMFGNAMEFILTER',
        'OECATALOGLOOKUPPROXY',
        'OEACATALOGLOOKUPPROXY',
        'APIAFMKTSERVICEADDRESS',
        'APIOWNSERVICEADDRESS',
        'APIURGSERVICEADDRESS',
        'GETLATLONGLOG',
        'GETLATLONGAPI',
        'SHIPWORKSUSER',
        'SHIPWORKSPASSWORD',
        'SHIPWORKSALLCCRESULT',
        'SHIPWORKSNOTFULFILLED',
        'SHIPWORKSSOURCE',
        'ALTERNATEFACETEDCATALOG',
        'URGIMAGEURL',
        'WHIACES',
        'THIRDPARTYCART',
        'PAYPALMERCHANTACCOUNTID',
        'CENTINEL',
        'CENTINELTEST',
        'CENTINELMERCHANTID',
        'CENTINELPROCESSORID',
        'CENTINELTRANSACTIONPWD',
        'EPROCESSINGNETWORK',
        'EPROCESSINGNETWORKTESTMODE',
        'SENDTRACKINGNUMBERTOAFFILIATESTORE',
        'SHOWDELIVERYTYPES',
        'SHIPMETHODDEFAULT',
        'UPSLIVE',
        'UPSAPIKEY',
        'UPSUSERID',
        'UPSPASSWORD',
        'UPSDEBUG',
        'USPSDEBUG',
        'CANADAPOSTENABLE',
        'CANADAPOSTDEMO',
        'CANADAPOSTAPIKEY',
        'CANADAPOSTCUSTOMERNUMBER',
        'CANADAPOSTCONTRACTID',
        'CANADAPOSTUSERNAME',
        'CANADAPOSTPASSWORD',
        'CANADAPOSTRATETYPE',
        'USEDIMENSIONALWEIGHT',
        'CANPARACCT',
        'VISACHECKOUT',
        'VISACHECKOUTDEMO',
        'VISACHECKOUTAPIKEY',
        'VISACHECKOUTSHAREDSECRET',
        'VISACHECKOUTCURRENCYCODE',
        'CREDITRETURNSMANUALLY',
        'ROLECREDITRETURNS',
        'FACETEDCATALOGORDERBY',
        'FACETEDCATALOGYMM',
        'ACESPARTSDBINVENTORY',
        'ACESPARTSDB',
        'OWNFACETEDPRICING',
        'OWNIMAGEURLOVERRIDE',
        'RECYCLEDFACETEDPRICING',
        'DEBUGTAXONOMICCATALOG',
        'STORETYPE',
        'NEEDSLOGIN',
        'LEGALNAME',
        'POFIRSTNAME',
        'POLASTNAME',
        'POCOMPANY',
        'POADDRESS',
        'POCITY',
        'POSTATE',
        'POZIP',
        'POPHONE',
        'POEMAIL',
        'SYSTEMMESSAGES',
        'USEWWHTTPCLASSTOSENDTRACKINGNUMBERS',
        'MINIMUMCOSTTOIMPORT',
        'DROPSHIPAGREEMENTSIGNED',
        'DROPSHIPAGREEMENTSIGNEDBY',
        'DROPSHIPAGREEMENTSIGNEDIP',
        'APIOWNFACETEDCATALOGUUID',
        'ACESNOMFGCODE',
        'URGCLIENTTYPE',
        'FACETEDCATALOGCONNECTOR',
        'FACETEDCATALOGLISTSTYLE',
        'FACETEDCATALOGGRIDSTYLE',
        'OWNPRICINGSTYLE',
        'URGPARTTYPELAYOUT',
        'CHECKOUTSIGNATUREREQUIRED',
        'ADDIMMNUMBERS',
        'APIFACETEDCATALOGSEARCHUIMODE',
        'WYSIWYG',
        'NEWCUSTOMERHITS',
        'USEDESKTOPFORTABLETS',
        'FACETEDALTERNATEWAREHOUSES',
        'URGVEHICLESELECTOR',
        'NONAPPCATALOG',
        'OPENSELECT2AUTOMATICALLY',
        'HIDESIMILARANDRELATEDITEMS',
        'SELLBATTERY',
        'HIDEPRICE',
        'HIDEBUYBUTTON',
        'CATALOGHIDEZIPCODE',
        'CATALOGHIDEWARRANTY',
        'HIDEFITMENTS',
        'HIDEDIMENSIONS',
        'HIDEPOPULARITY',
        'DELIVERYDAYSEXCLUDED',
        'HIDESHIPPINGMETHODSWHENLOCALDELIVERYAVAILABLE',
        'SHIPPO',
        'SHIPPOTEST',
        'SHIPPOLIVETOKEN',
        'SHIPPOTESTTOKEN',
        'INSTOREPICKUPAUTOCHOOSE',
        'INSTOREPICKUPSINGLELOCATION',
        'STOREGROUP',
        'WHDSENDBILLTOASSHIPTO',
        'GETZIPBYIP',
        'GETLATLONGLOCAL',
        'GETLATLONGBYZIP',
        'ISPUMAP',
        'INSTOREPICKUPSHOWCONAME',
        'INSTOREPICKUPDISTANCE',
        'USEBUYERPOSTALFORDELIVERY',
        'PAYMENTREQUESTEMAIL',
        'PAYMENTREQUESTEMAIL1',
        'PLIVOAUTHID',
        'PLIVOAUTHTOKEN',
        'PLIVOSRC',
        'EXCLUDEPARTTERMINOLOGYIDS',
        'PORTALPAYMENTMETHODS',
        'IPQUALITYSCORE',
        'FRAUDBLOCKNONUSA',
        'FRAUDBLOCKVPN',
        'MONERISCHECKOUT',
        'MONERISSTOREID',
        'MONERISKEY',
        'MONERISTEST',
        'MONERISCURRENCYCODE',
        'PAYLATER',
        'MASTERTEMPLATE',
        'SOPHIOPRIVATEKEY',
        'ACESREGIONID',
        'FACETEDCATALOGINVENTORYMIXING',
        'REALTIMEINVENTORY',
        'WPACSCREENSCRAPE',
        'WHIACESCATLINKUSERID',
        'WHIACESCATLINKPASSWORD',
        'WHIACESCATLINKPROVIDER',
        'WHIACESCATLINKSERVERURL',
        'AMAZONMANUALFULFILLMENT',
        'AMAZONCLIENTID',
        'AMAZONADDCORESHIPPINGTOCOSTBASIS',
        'AMAZONOPERATIONTYPE',
        'EBAYIGNORECHECKOUTMESSAGES',
        'EBAYPRODUCTSTOLIST',
        'EBAYSENDNONCATALOGITEMS',
        'EBAYSENDINVENTORYPOSTALCODE',
        'EBAYHIDEITEMSWITHOUTPRICE',
        'EBAYCONTACTPKS',
        'EBAYVACATION',
        'EBAYREDIRECT',
        'EBAYFEEDDONTSENDIMAGES',
        'EBAYRETURNTYPES',
        'EBAYADDCORESHIPPINGTOCOSTBASIS',
        'CHUBDEMO',
        'CHUBSELLERID',
        'CHUBSELLERNAME',
        'CHUBSELLERPASSWORD',
        'CHUBMANUALFULFILLMENT',
        'CHUBFTPSEND',
        'CHUBSENDORDERFULFILLMENTCONFIRMATIONS',
        'CHUBFORMULASELLPRICE',
        'CHUBFORMULAQTY',
        'CHUBMAXDIMWEIGHT',
        'CHUBLOG',
        'CHUBADDCORESHIPPINGTOCOSTBASIS',
        'CHUBSTOCKTHRESHOLD',
        'CHUBPRICETHRESHHOLD',
        'CHUBSELLPRICEMIN',
        'CHUBSTOCKMAX',
        'CHUBCONTACTPKS',
        'CHUBFTPFEEDLOC',
        'CHUSERVERHOSTKEY',
        'SOPHIOIPNDELAYMINUTES',
        'SENDTAXTOWDVIAAPI',
        'CHUBONBOARDING',
        'SUPPLIERLOGSTYPE',
        'CHUBPARTNERPERSONPLACEID',
        'CHUBSENDZEROSTOCKASONE',
        'VAT',
        'TAXLOGGING',
        'TAXCANADIANS',
        'TAXJARAPIKEY',
        'TAXJARDEMO',
        'TAXJAR',
        'HIDEIMAGESIFLOGOS',
        'SHIPSTATIONHIDEPRICE',
        'SHIPSTATIONAPIKEY',
        'SHIPSTATIONSECRETKEY',
        'SHIPSTATIONSTOREID',
        'FULFILLMENTSUPPLIERS',
        'AMAZONCONDITIONFORURG',
        'BRANDSLIDER',
        'PRODUCTSLIDERSPECIAL',
        'PRODUCTSLIDERPOPULAR',
        'MEGAMENU',
        'INDEXABOUT',
        'CATEGORYSLIDER',
        'PRODUCTSLIDERNEW',
        'FREIGHTFORWARDING',
        'FF_NAME',
        'FF_ADDR',
        'FF_ADDR2',
        'FF_CITY',
        'FF_STATE',
        'FF_ZIP',
        'FF_PHONE',
        'FF_EMAIL',
        'FF_CTRYID',
        'FF_CTRY',
        'FF_USEZIPFORINTLSHIPCALC',
        'FF_PROFILEPK',
        'SOPACCOUNTNUM',
        'SOPPASSWORD',
        'SOPURL',
        'SOPCONTACTPK',
        'SHIPPOTHIRDPARTYBILLING',
        'CROSSDOCK',
        'DATABASETYPE2',
        'SENDITEMPACKAGEQUANTITYMORETHAN1TOCHUB',
        'CHECKFORDEFECTS',
        'CHECKFOR',
        'STOREADDRESS',
        'STORECITY',
        'STORESTATE',
        'EXCLUDEDOW',
        'POADDRESSNUMBER',
        'POCNTRYID',
        'GEOPLACENAME',
        'GEOREGION',
        'GEOPOSITION',
        'GEOICBM',
        'SPARKLINKDEFAULTDB',
        'PAYPALPARTNERCLIENTID',
        'PAYPALMERCHANTCLIENTID',

        'SURCHARGELIMIT',
        'IMPERSONATE_TO_FRONTEND',
        'EXTRASHIPPINGRATE',
        'AUTOFULFILL',
        'AUTOFULFILLMAXORDERSIZE',
        'CATALOGCLIENTID',
        'PREFERREDPARTTYPECATS','ALLOWEDCARRIERS','MAINTENANCEEMAIL'
        ];
    public function setup()
    {
        parent::setup();
        $this->crud->setModel(Store::class);
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/store');
        CRUD::setEntityNameStrings('store', 'Stores');
        Store::saved(function ($store) {
            Cache::forget('store.'.$store->STOREPK);
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
        });
    }

    protected function setupListOperation()
    {
        $this->crud->orderBy('PK','asc');
        $this->crud->addColumns([
            [
                'name' => 'PK',
                'label' => 'ID',
                'type' => 'text',
                'tab' => 'General',

            ], [
                'name' => 'STOREPK',
                'label' => 'Store PK',
                'type' => 'text',
                'tab' => 'General',

            ], [
                'name' => 'STORENAME',
                'label' => 'Name',
                'type' => 'text',
                'tab' => 'General',

            ], [
                'name' => 'STORETYPE',
                'label' => 'Type',
                'type' => 'text',
                'tab' => 'General',

            ],[
                'name' => 'VIRTUAL',
                'label' => 'URL',
                'type' => 'text',
                'tab' => 'General',

            ],
        ]);
    }

    protected function setupShowOperation()
    {
        $this->crud->addColumns($this->getFields());
    }
    protected function setupCreateOperation()
    {
        $this->crud->addFields($this->getFields());

    }

    protected function setupUpdateOperation()
    {

        $this->setupCreateOperation();
    }
    public function getFields()
    {
        $fields = [


            [
                'name' => 'STOREPK',
                'label' => 'Store PK',
                'type' => 'text',
                'tab' => 'General',

            ], [
                'name' => 'STORENAME',
                'label' => 'Name',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'STORETYPE',
                'label' => 'Type',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'VIRTUAL',
                'label' => 'URL',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'BASEURL',
                'label' => 'Base Url',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'MAILFEMAIL',
                'label' => 'MailF EMail',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'MAILFROM',
                'label' => 'Mail FROM',
                'type' => 'text',
                'tab' => 'General',

            ],
            [
                'name' => 'MAILCC',
                'label' => 'Mail CC',
                'type' => 'text',
                'tab' => 'General',

            ],
        ];
        foreach($this->advset as $advset) {
            $fields[] = [
                'name' => $advset,
                'label' =>  $advset,
                'fake' => true,
                'store_in' => 'ADVSET',
                'type' => 'text',
                'tab' => 'ADVSET',
            ];
        }
        return $fields;
    }
}