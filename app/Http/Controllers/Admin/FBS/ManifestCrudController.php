<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Lookups;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\FBSReturns\Library\Actions\ManifestExportTemplate;
use Sophio\FBSReturns\Library\Actions\Reports\ReturnRate;
use Sophio\FBSReturns\Library\ManifestService;
use Sophio\FBSReturns\src\Models\ReturnLine;

class ManifestCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;

    public function __construct()
    {
        ini_set('memory_limit', '-1');
        parent::__construct();
        $this->middleware(function ($request, $next) {
            if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
                $request->merge(['profilepk' => backpack_user()->user_key]);
                $request->merge(['supplierpk' => backpack_user()->user_key]);
            }
            return $next($request);
        });
    }

    public function setup()
    {

        parent::setup();
        CRUD::setModel(ReturnLine::class);

        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/manifest');
        CRUD::setEntityNameStrings('Manifest', 'Manifest');
        if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
            $this->crud->query->where('profilepk', backpack_user()->user_key);
        }
        $this->crud->query->with(['return' => function ($query) {
            return $query->orderBy('reqdate');
        }]);


    }

    public function setupManifestRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/generate', [
            'as' => $routeName . '.generate',
            'uses' => $controller . '@generate',
            'operation' => 'generate',
        ]);
        Route::any($segment . '/manifestlist', [
            'as' => $routeName . '.manifestlist',
            'uses' => $controller . '@manifestlist',
            'operation' => 'manifestlist',
        ]);
        Route::any($segment . '/perform', [
            'as' => $routeName . '.perform',
            'uses' => $controller . '@perform',
            'operation' => 'perform',
        ]);
        Route::any($segment . '/dashboard', [
            'as' => $routeName . '.dashboard',
            'uses' => $controller . '@dashboard',
            'operation' => 'dashboard',
        ]);
        Route::any($segment . '/sendemail', [
            'as' => $routeName . '.sendemail',
            'uses' => $controller . '@sendemail',
            'operation' => 'sendemail',
        ]);
        Route::any($segment . '/returnrate', [
            'as' => $routeName . '.returnrate',
            'uses' => $controller . '@returnrate',
            'operation' => 'returnrate',
        ]);
    }

    public function returnrate()
    {
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });
        $nd = new ReturnRate();
        $items = $nd(request()->get('when') ?? 'LASTMONTH', request()->get('custtype'), request()->get('custpk'), request()->get('profilepk'));
        return view('admin.fbs.reports.returnrates', ['items' => $items, 'customers' => Customer::whereIn('pk', $custs)->get()]);
    }

    public function perform()
    {
        $manifestService = new ManifestService();
        $data = request()->except('token');

        switch (request()->get('Action')) {
            case 'Shipped':

                if (isset($data['returnedid'])) {
                    $first_line = ReturnLine::where('returnedid', $data['returnedid'])->first();
                    if ($first_line) {
                        $affected = $manifestService->assignTrackingNumber($data);
                        return view('admin.fbs.manifest.shipped', ['data' => $data, 'affected' => $affected, 'supplier' => $first_line->supplier, 'manifestService' => $manifestService]);

                    } else {
                        return view('admin.fbs.manifest.shipped', ['data' => $data, 'affected' => 0]);
                    }
                } else {
                    return view('admin.fbs.manifest.shipped', ['data' => $data, 'affected' => null]);
                }

            case 'Search':
                if (isset($data['returnedid'])) {
                    return redirect()->route('fbs/manifest.index', array_merge(['database' => config('tenant_db')], request()->except(['_token', 'Action'])));
                } else {
                    return redirect()->route('fbs/manifest.manifestlist', array_merge(['database' => config('tenant_db')], request()->except(['_token', 'Action'])));
                }
            case 'Supplier':
                return redirect()->route('fbs/manifest.manifestlist', array_merge(['database' => config('tenant_db')], request()->except(['_token', 'Action'])));
        }
    }

    public function dashboard()
    {
        $suppliers = Supplier::where('NAME', '<>', "")->whereIn('wws_sendprofiles.SUP', ['WHD','UAS', 'SOP'])->orderBy('NAME')->get(['PK', 'NAME']);

        return view('admin.fbs.manifest.dashboard', ['suppliers' => $suppliers]);
    }

    public function manifestlist()
    {
        $manifestService = new ManifestService();
        $data = request()->except('token');
        if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
            $data['profilepk'] = backpack_user()->user_key;
        }
        if (!isset($data['when'])) {
            $data['when'] = 'last365';
        }


        $manifests = $manifestService->find($data);
        $totalReturnsReceived = $manifestService->getReceivedReturnsValue($data);
        $totalItemsReceived = $manifestService->getReceivedReturnsItems($data);
        $totalCreditsReceivedOnStatements = $manifestService->getTotalCreditsReceivedOnStatements($data);
        $totalUnMatched = -$totalCreditsReceivedOnStatements - $manifests->sum('credits_received');
        $deductions = $manifestService->getDeductions($data);
        $credits = $manifestService->getCredits($data);
        return view('admin.fbs.manifest.list', ['manifests' => $manifests,
            'manifestService' => $manifestService,
            'data' => $data,
            'totalReturnsReceived' => $totalReturnsReceived,
            'totalItemsReceived' => $totalItemsReceived,
            'totalCreditsReceivedOnStatements' => $totalCreditsReceivedOnStatements,
            'totalUnMatched' => $totalUnMatched,
            'averagePartCost' => $totalItemsReceived > 0 ? number_format($totalReturnsReceived / $totalItemsReceived, 2, '.') : 0,
            'deductions' => $deductions,
            'credits' => $credits
        ]);
    }

    public function generate()
    {
        if (request()->post()) {
            $profilepk = request()->get('profilepk');
            $returnlinespk = request()->get('returnline');
            $manifestService = new ManifestService();
            $ok = $manifestService->generateManifest($profilepk, $returnlinespk);
            return view('admin.fbs.manifest.finish', ['supplier' => Supplier::find($profilepk), 'ok' => $ok, 'countlines' => count($returnlinespk), 'manifestService' => $manifestService]);
        } else {
            $profilepk = request()->get('profilepk');
            /*
            $returns = ReturnHeader::whereHas('returnline',function($query)use($profilepk){
                $query->whereNull('returned');
                $query->whereNotNull('received');
                $query->where('profilepk', $profilepk);
            });
            */
            $manifestService = new ManifestService();
            $returnlines = $manifestService->getReturnlines($profilepk);
            return view('admin.fbs.manifest.generate', ['supplier' => Supplier::find($profilepk), 'returnlines' => $returnlines]);
        }

    }

    public function sendemail()
    {
        $manifestService = new ManifestService();
        $manifestService->sendManifest(request()->get('returnedid'));
        Alert::add('success', 'Mail sent!')->flash();
        return back();
    }

    /**
     * This overrides the Backpack index
     */
    public function index()
    {



        if (request()->get('returnedid','') === '') {
            Alert::add('danger', 'A manifest ID is required for access this page!');
            return (redirect(sophio_route('fbs/manifest.dashboard', [])));

        }
        $this->crud->hasAccessOrFail('list');
        $this->data['crud'] = $this->crud;
        $this->data['title'] = $this->crud->getTitle() ?? mb_ucfirst($this->crud->entity_name_plural);
        return view($this->crud->getListView(), $this->data);
    }

    protected function setupListOperation()
    {
        $this->crud->query->with('invoice');

        CRUD::disableResponsiveTable();
        $this->crud->setPageLengthMenu(-1);
        $this->crud->addFilter([ // dropdown filter
            'name' => 'returnedid',
            'type' => 'text',
            'label' => 'Manifest ID',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'returnedid', '=', "$value");
            });
        $this->addTopBar();
        Widget::add([
                'type' => 'tablelegend',
                'content' => "Return manifest ID:" . request()->get('returnedid') . " has  " . $this->crud->count() . " items. " .
                    "Total Credit Requested: $" . $this->crud->query->sum(\DB::raw("qty*(cost+coreprice)")) . ". " .
                    "Credit Received: $" . $this->crud->query->sum(\DB::raw("-supcredamt")) . ". " .
                    "Due: $" . $this->crud->query->sum(\DB::raw("qty*(cost+coreprice)+supcredamt")) . ". "

            ]
        )->to('before_content');
        Widget::add()->type('style')->content('assets/css/sophio/compact.css');
        // $this->crud->disableResponsiveTable();
        $this->crud->setEntityNameStrings('Manifest ' . request()->get('returnedid'), 'Manifest ' . request()->get('returnedid'));
        $this->crud->addColumns([
            ['name' => 'writeoff', 'escaped' => false, 'limit' => 1000,'escaped'=>false, 'value' => function ($entry) {
                if ($entry->writeoff == null && $entry->supcredamt == 0) {

                    return '<a href="' . sophio_route('fbs/returnlines.writeoff', ['id' => $entry->pk]) . '"  >Write Off</a>';
                }
                if ($entry->supcredamt <> 0) {
                    return 'Credited';
                } else {
                    return 'Written off <br>' . (($entry->writeoff !== null) ? Carbon::parse($entry->writeoff)->format('m/d/y') : '');
                }
            }],
       ]);
        if (request()->get('returnedid')) {

        } else {
            $this->crud->addColumns([
                ['name' => 'writeoff', 'escaped' => false, 'limit' => 1000, 'value' => function ($entry) {
                    if ($entry->writeoff == null && $entry->supcredamt == 0) {

                        return '<a href="' . sophio_route('fbs/returnlines.writeoff', ['id' => $entry->pk]) . '"  >Write Off</a>';
                    }
                    if ($entry->supcredamt <> 0) {
                        return 'Credited';
                    } else {
                        return 'Written off' . (($entry->writeoff !== null) ? $entry->writeoff : '');
                    }
                }],
                ['name' => 'supplier.NAME', 'label' => 'Supplier', 'limit' => 50, 'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return sophio_route('fbs/manifest.manifestlist', ['profilepk' => $entry->profilepk, 'when' => 'LAST90']);
                    },
                ]]]);
        }
        $this->crud->addColumns([

            ['name' => 'returnedid', 'label' => 'Our Return ID'],
            ['name' => 'supcmemoid', 'label' => 'Sup. Credit'],
        ]);
        if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
            $this->crud->addColumns([
                ['name' => 'invoice.pk', 'label' => 'Our order number', ],]);
        } else {
            $this->crud->addColumns([
                ['name' => 'invoice.pk', 'label' => 'Our order number', 'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('/dbs/' . \Route::current()->parameter('database') . '/fbs/fbsorder/' . $related_key . '/show');
                    },
                ]],]);
        };
        $this->crud->addColumns([
            ['name' => 'invoice.invdate', 'label' => 'Invoiced', 'type' => 'date', 'format' => 'M/D/YY'],
            ['name' => 'return.apprdate', 'label' => 'Approved', 'type' => 'date', 'format' => 'M/D/YY'],
            ['name' => 'invoice.st_name', 'label' => 'ST Name', 'limit' => 50],
            ['name' => 'invoice.supinvpk', 'label' => 'Sup. Invoice'],
            /*
            ['name' => 'whse', 'label' => 'WHSE', 'wrapper' => [
                'href' => function ($crud, $column, $entry, $related_key) {
                    return sophio_route('fbs/manifest.index', array_merge(request()->query->all(), ['whse' => $entry->whse]));
                },
            ]],
*/
            ['name' => 'sup_ord_id', 'label' => 'Sup. Order', 'limit' => 50,],
            ['name' => 'return.suprmaid', 'label' => 'Supplier RMA', 'limit' => 50],


            ['name' => 'mfr', 'label' => 'Brand', 'limit' => 50],
            ['name' => 'wdlinecode', 'label' => 'Linecode'],
            ['name' => 'sku', 'label' => 'SKU', 'limit' => 19],
            ['name' => 'condition', 'label' => 'Condition',],
            ['name' => 'returnreason.name', 'label' => 'Return reason'],
            ['name' => 'received', 'label' => 'Returned', 'type' => 'date', 'format' => 'M/D/YY'],
            ['name' => 'qty', 'label' => 'Quantity', 'type' => 'text'],
            ['name' => 'cost', 'label' => 'Cost', 'type' => 'text'],
            ['name' => 'coreprice', 'label' => 'Core price', 'type' => 'text'],
            ['name' => 'extended_cost', 'label' => 'Extended cost', 'type' => 'text', 'value' => function ($entry) {
                return ($entry->retCode <> 2) ? $entry->qty * ($entry->cost + $entry->coreprice) : $entry->qty * $entry->cost;
            }],
            ['name' => 'supcredamt', 'label' => 'Credited', 'type' => 'text'],
            ['name' => 'credited', 'label' => 'Due', 'type' => 'text', 'value' => function ($entry) {
                return number_format(($entry->retCode <> 2) ? $entry->qty * ($entry->cost + $entry->coreprice) + $entry->supcredamt : $entry->qty * $entry->cost + $entry->supcredamt, 2, '.');
            }],
        ]);

        $this->crud->orderBy('retpk');

    }

    protected function addTopBar()
    {

        $content = [];
        if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
        } else {
            $content[] = [
                'type' => 'link',
                'href' => request()->fullUrlWithQuery(array_merge(request()->all(), ['action' => 'edit'])),
                'label' => 'Edit manifest'
            ];
        }
        /*
        $content[] = [
            'type' => 'link',
            'href' => request()->fullUrlWithQuery(array_merge(request()->all(), ['showwriteoffs' => 'true'])),
            'label' => 'Show Write-offs'
        ];
        */
        $returnline = ReturnLine::where('returnedid', request()->get('returnedid'))->first();
        if ($returnline !== null) {
            $supplier = $returnline->supplier;
            if ($supplier != null) {
                $mf = new ManifestService();
                $export = $mf->createFileForManifest($supplier->PK, request()->get('returnedid'));
                $url = $export->getUrl();

                $content[] = [
                    'type' => 'link',
                    'href' => $url,
                    'label' => 'Download Manifest'
                ];
            }
        }

        /*
        if (Storage::disk('fbs')->exists('manifest/' . $supplier->PK . '/manifest-' . request()->get('returnedid') . '.csv')) {

            $url  = Storage::disk('fbs')->url('manifest/' . $supplier->PK . '/manifest-' . request()->get('returnedid') . '.csv');

        } else {

            $mf = new ManifestService();
            $export = $mf->createFileForManifest($supplier->PK, request()->get('returnedid'));
            $url = $export->getUrl();
        }
        */
        if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
        } else {
            $content[] = [
                'type' => 'link',
                'href' => route('fbs/manifest.sendemail', ['database' => config('tenant_db'), 'returnedid' => request()->get('returnedid')]),
                'label' => 'Send manifest to Supplier Returns Email'
            ];
        }
        Widget::add([
                'type' => 'topbar',
                'content' => $content
            ]
        )->to('before_content');
    }
}