<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\SellerProfile;

class SellerProfileCrudController
    extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;


    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        CRUD::setModel(SellerProfile::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/sellerprofile');
        CRUD::setEntityNameStrings('sellerprofile', 'Seller Profile');
    }
    protected function setupListOperation()
    {
        $this->crud->addColumns([
            ["name" => "customer",
                'type' => 'text', 'limit' => 120,
                'label' => 'Customer',
                'value' => function ($entry) {
                    if ($entry->customer)
                        return $entry->customer->company . ' - ' . $entry->customer_pk . ' - ' . $entry->customer->custtype;
                }
            ],
            "storepk" ,"active"
        ]);

    }
    protected function setupShowOperation()
    {
        $this->crud->addColumns([
            ["name" => "customer",
                'type' => 'text', 'limit' => 120,
                'label' => 'Customer',
                'value' => function ($entry) {
                    if ($entry->customer)
                        return $entry->customer->company . ' - ' . $entry->customer_pk . ' - ' . $entry->customer->custtype;
                }
            ],
            'seller_database',
            'storepk',
            'rtsc_enable',
            'max_suppliers',
            'maximum_loss_item',
            'maximum_loss_order',
            'psp_low_correction',
            'reject_multiple_orders',
            'active'
        ]);
    }
}