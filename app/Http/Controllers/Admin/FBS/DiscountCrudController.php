<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Models\Discounts;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Pricing;

class DiscountCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;

    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use CustomerTrait;

    protected $customer;
    public function setup()
    {

        parent::setup();

        if (\Route::current()->parameter('accountnum') != '') {

            //sconfig(['tenant_db' => config('master_tenant_db')]);
            $this->customer = Customer::where('accountnum', trim(\Route::current()->parameter('accountnum')))->first();

            $db = $this->customer->xml['ACESPARTSDB'] ?? '';

            if ($db == '') {
                return redirect('/');
            }

            config(['custom_db' => $db]);
            CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/discount/' .\Route::current()->parameter('accountnum') . '/accountnum');
        } elseif (\Route::current()->parameter('db')) {

            config(['custom_db' => $db = \Route::current()->parameter('db')]);
            CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/discount/' . $db . '/db');
        } else {

            throw new \Illuminate\Http\Exceptions\HttpResponseException(redirect(sophio_route('fbs/pricing.dashboard')));
        }

        $this->crud->model = new Discounts();

        $this->crud->model->setTable($db . '.' . explode('.', $this->crud->model->getTable())[1]);

        $this->crud->query = clone $this->crud->totalQuery = $this->crud->model->select('*');
        $this->crud->entry = null;


        CRUD::setEntityNameStrings('Discount', 'Discount');


    }
    protected function setupListOperation()
    {
        $customer = $this->customer;

        $this->crud->addColumns(
            [
                ['name' => 'linecode'],
                ['name' => 'discount'],
                ['name' => 'effectiveDate'],
                ['name' => 'market'],
                ['name' => 'lastupdate'],

            ]
        );

    }
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
    protected function setupCreateOperation()
    {
        $this->crud->addFields([

            ['name' => 'linecode', 'type' => 'text'],
            ['name' => 'discount' ,'type' => 'number', 'decimanls' => 2, 'attributes' => ["step" => "any"], 'default' => 0.00],
            ['name' => 'effectiveDate',   'type'  => 'datetime'],
            ['name' => 'market', 'type' => 'text'],
        ]);
    }
}