<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Mail\GeneralMail;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Sophio\Common\Auth\Invite;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\UserInvite;
use Sophio\Common\SophioUser;
use Sophio\Common\UserProfile;
use Spatie\Permission\Models\Permission;

class CustomerUsersCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation {
        destroy as traitDestroy;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation {
        store as traitStore;
    }
    use \MHMartinez\ImpersonateUser\app\Http\Controllers\Operations\ImpersonateUserOperation;
    protected $customer;
    public function __construct()
    {
        parent::__construct();
    }

    public function setupExtraRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/{id}/loginsite', [
            'as' => $routeName . '.loginsite',
            'uses' => $controller . '@loginsite',
            'operation' => 'loginsite',
        ]);

        Route::any($segment . '/{id}/invite', [
            'as' => $routeName . '.invite',
            'uses' => $controller . '@invite',
            'operation' => 'invite',
        ]);

    }

    public function invite($db, $id)
    {
        $user = SophioUser::find($id);
        $invites = UserInvite::where('invited_user_id', $id)->get();
        if(request()->post()){
            $inviteManager = new Invite();
            $inviteManager->createInviteByUser($user,backpack_user(),$this->customer);


        }
        return view('admin.fbs.customer.userinvite', ['invites' => $invites, 'user'=>$user]);
    }

    public function loginsite($db, $id)
    {
        $user = SophioUser::find($id);
        if ($user->remember_token === null) {
            $user->remember_token = sha1(time());
            $user->save();
        }
        if ($user->salesrep()) {
            return redirect()->to(sophiosettings()->getStore()['VIRTUAL'] . 'logintoken?token=' . $user->remember_token . '&impersonator=' . backpack_user()->id . '&redirect_url=/account/orders');
        } else {
            return redirect()->to(sophiosettings()->getStore()['VIRTUAL'] . 'logintoken?token=' . $user->remember_token . '&impersonator=' . backpack_user()->id . '&redirect_url=/account/users');
        }
    }

    public function setup()
    {
        $this->crud->setModel(SophioUser::class);
        if (request()->get('custpk')) {
            $customer = Customer::find(request()->get('custpk'));
        } else {
            $customer = Customer::find(request()->route('custpk'));
        }
        $this->customer = $customer;
        $this->crud->query->whereHas('profilables', function ($query) use ($customer) {
            $query->where('profilable_type', 'customer');
            $query->where('profilable_id', $customer->pk);
        });
        if (request()->get('custpk')) {
            $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/customer/' . request()->get('custpk') . '/user');
        } else {
            $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/customer/' . request()->route('custpk') . '/user');
        }

        CRUD::setEntityNameStrings('User', 'Users');
        SophioUser::created(function ($entry) use ($customer) {
            $entry->givePermissionTo(Permission::where('name', 'salesrep')->first());
            $entry->remember_token = sha1($entry->name . time());
            $entry->save();
            UserProfile::create([
                'sophio_user_id' => $entry->id,
                'profilable_type' => 'customer',
                'profilable_id' => $customer->pk,
            ]);
        });
    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        $this->crud->addColumns([
            'name', 'email', 'fullname',
            [
                'label' => "Type",
                'name' => 'type',
                'value' => function ($model) {
                    if ($model->salesrep) {
                        return 'SalesRep';
                    }
                    return 'Admin';
                }
            ],

            [
                'name' => 'login',
                'label' => 'Login',
                'value' => function ($model) {
                    return 'Login as ' . $model->name;
                },
                'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/customer/users/' . $entry->id . '/loginsite');
                    },
                    'target' => '_blank',

                ],
            ],

        ]);
    }

    public function store()
    {
        CRUD::setRequest(CRUD::validateRequest());

        /** @var \Illuminate\Http\Request $request */
        $request = CRUD::getRequest();

        // Encrypt password if specified.
        if ($request->input('password') && $request->input('password') !== "") {
            $request->request->set('password', Hash::make($request->input('password')));
        } else {
            $request->request->remove('password');
        }

        CRUD::setRequest($request);
        CRUD::unsetValidation(); // Validation has already been run

        return $this->traitStore();
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        $id = $this->crud->getCurrentEntryId() ?? $id;
        UserProfile::where('sophio_user_id', $id)->delete();
        return $this->crud->delete($id);
    }

    protected function setupCreateOperation()
    {
        $this->crud->addFields(
            [
                [
                    'name' => 'name',
                    'label' => 'Username',
                    'type' => 'text'
                ],
                [
                    'name' => 'fullname',
                    'label' => 'Full name',
                    'type' => 'text'
                ],
                [
                    'name' => 'email',
                    'label' => 'Email',
                    'type' => 'text'
                ],
                [
                    'name' => 'password',
                    'label' => 'Password',
                    'type' => 'password',
                    'hint' => 'Leave empty to not change it.'
                ],
                [
                    'name' => 'phone',
                    'label' => 'Phone',
                    'type' => 'text'
                ],
                [
                    'name' => 'salesrep.maxordersize',
                    'label' => 'Maximum Order Size',
                    'type' => 'number', 'attributes' => ['step' => 'any'],
                    'decimals' => 2,
                    'prefix' => '$',
                ],
                [
                    'name' => 'salesrep.no_shipto_override',
                    'label' => 'Disable custom shipto address',
                    'type' => 'switch',
                ],
                [
                    'name' => 'description',
                    'label' => 'Note',
                    'type' => 'text'
                ],
            ]
        );
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}