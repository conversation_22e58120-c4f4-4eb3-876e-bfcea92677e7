<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\HistoryFeed;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\FBS\Supplier;

class HistoryFeedCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;

    protected $sups;

    public function setup()
    {
        if (!backpack_user()->hasAnyRole(['store', 'store-admin', 'super-admin'])) {
            $this->crud->denyAccess(['list', 'create', 'delete', 'show', 'update', 'bulkDelete']);
        }
        parent::setup();
        CRUD::setModel(HistoryFeed::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/historyfeed');
        CRUD::setEntityNameStrings('History Feed', 'History Feeds');
        $s = Supplier::where('ACTIVE', '=', 1)->whereIn('SUP', config('sophio.admin.active_sup_types'))->get();
        $this->sups = [];
        foreach ($s as $ss) {
            $this->sups[$ss->contactpk] = $ss->PK . ' ' . $ss->NAME;
        }
        $this->crud->query->orderBy('feeddate', 'DESC');
    }

    protected function setupListOperation()
    {
        $this->crud->addColumns([
            ['name' => 'contactpk','label'=>'Supplier',
                'value' => function ($entry) {
                    return $this->sups[$entry->contactpk]??'';
                }
            ],

            'feeddate', 'linecode', 'product_number', 'qty_avail', 'cost', 'coreprice', 'sellprice', 'listprice', 'map', 'part_number_unformatted', 'zipcode', 'descript', 'lastupdate'
        ]);
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'part_number_unformatted',
                'label' => 'Product Number',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'part_number_unformatted', unformatString(trim($value)));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'linecode',
                'label' => 'Linecode',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'linecode', trim($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'dropdown',
                'name' => 'contactpk',
                'label' => 'Supplier',
            ],
            $this->sups,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'contactpk', trim($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'date',
                'name' => 'feeddate',
                'label' => 'Date',
            ],
            $this->sups,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'feeddate', trim($value));
            }
        );
    }
}