<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\Widget;
use Sophio\Common\Models\FBS\Customer;

trait CustomerTrait
{
    public function customerTopBar(Customer $customer)
    {
        $content = [
            [
                'type' => 'link',
                'href' => sophio_route('fbs/customer.edit', ['id' => $customer->pk]),
                'label' => 'Edit Customer '.$customer->company.' ('.$customer->accountnum.')'

            ],
            [
                'type' => 'link',
                'href' => sophio_route('fbs/fbsorder.index', ['custpk' => $customer->pk]),
                'label' => 'Customer Orders'

            ],

        ];
        if (isset($customer->xml['MYSQLPRICING']) && strtoupper($customer->xml['MYSQLPRICING']) === "TRUE") {
            $content[] = [
                'type' => 'link',
                'href' => sophio_route('fbs/pricing/{accountnum}/accountnum.index', ['accountnum' => $customer->pk]).'?accountnum='. $customer->pk,
                'label' => 'Pricing'

            ];
        }
        $content[] = [
            'type' => 'link',
            'href' => sophio_route('fbs/customer.statement', ['id' => $customer->pk]),
            'label' => 'Statements'
        ];

        $content[] = [
            'type' => 'link',
            'href' => sophio_route('maillog.index', ['custpk' => $customer->pk]),
            'label' => 'Mail Log'

        ];

        $content[] = [
            'type' => 'link',
            'href' => sophio_route('customer/{custpk}/users.index', ['custpk' => $customer->pk]),
            'label' => 'Users'

        ];
        $content[] = [
            'type' => 'link',
            'href' => sophio_route('customer/{custpk}/custshipto.index', ['custpk' => $customer->pk]),
            'label' => 'Shipping Addresses'

        ];
        $content[] = [
            'type' => 'link',
            'href' => sophio_route('customer/{custpk}/buyers.index', ['custpk' => $customer->pk]),
            'label' => 'Nexpart buyers'

        ];
        $content[] = [
            'type' => 'link',
            'href' => sophio_route('fbs/customer.activateb2b', ['id' => $customer->pk]),
            'label' => 'Activate B2B'

        ];
        $content[] = [
            'type' => 'link',
            'href' => sophio_route('fbs/customer.apikeys', ['id' => $customer->pk]),
            'label' => 'API Keys'

        ];
        Widget::add([
                'type' => 'topbar',
                'content' => $content
            ]
        )->to('before_content');
    }
}