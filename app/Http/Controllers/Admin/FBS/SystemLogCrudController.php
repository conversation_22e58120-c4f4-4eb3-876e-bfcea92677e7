<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

use Sophio\Common\Models\SystemLog;

class SystemLogCrudController
    extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));

        CRUD::setModel(SystemLog::class);

        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/systemlog');
        CRUD::setEntityNameStrings('systemlog', 'System Log');
    }

    protected function setupListOperation()
    {
        $this->crud->orderBy('id', 'desc');
        $this->crud->addColumns([
            'task', 'created_at', 'updated_at',
            ['name' => 'message',
                'escaped' => false,
                'value' => function ($entry) {
                    return nl2br($entry->message);
                },
                'searchable'=>true,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('message', 'like', '%'.$searchTerm.'%');
                },
                'limit' => 1000
            ],
            'user_id', 'status'
        ]);
        $this->crud->addFilter([
            'type' => 'dropdown',
            'name' => 'task',
            'label' => 'Task Type'
        ],
           function(){
            $t = SystemLog::groupBy('task')->get();
            $r =[];
            foreach($t as $tt)
            {
                $r[$tt->task] = $tt->task;
            }
            return $r;
           },
            function ($value) {
                $this->crud->addClause('where', 'task',$value);
            });

    }

    protected function setupShowOperation()
    {
        $this->crud->denyAccess(['update', 'delete', 'create']);
        $this->crud->removeButton('delete');
        $this->crud->removeColumns(['bulk_actions']);
        $this->crud->set('show.setFromDb', false);
        $this->crud->addColumns([
            'task', 'created_at', 'updated_at',
            ['name' => 'message','type'=>'text',
                'escaped' => false,
                'value' => function ($entry) {
                    return nl2br($entry->message);
                },
                'limit' => 50000
            ],
            'user_id', 'status'
        ]);
    }
}