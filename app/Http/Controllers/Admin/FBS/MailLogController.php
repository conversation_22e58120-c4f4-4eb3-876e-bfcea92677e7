<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Controllers\BaseTenantCrudController;

use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\MailLog;

class MailLogController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    public function setup()
    {
        if (!backpack_user()->hasAnyRole(['store', 'store-admin', 'super-admin'])) {
            $this->crud->denyAccess(['list', 'create', 'delete', 'show', 'update', 'bulkDelete']);
        }
        parent::setup();
        CRUD::setModel(MailLog::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/maillog');
        CRUD::setEntityNameStrings('mail', 'Mails');
        $this->crud->query->orderBy('created_at', 'DESC');
    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        $this->crud->addColumns([
                ['name' => 'to', 'limit' => 1000],
                ['name' => 'subject', 'limit' => 1000],
                ['name' => 'created_at', 'type' => 'datetime'],
                ['name' => 'custpk', 'limit' => 1000],
                ['name' => 'profilepk', 'limit' => 1000],
                'sent']
        );
        CRUD::filter('custpk')->type('text')->label('Customer')->whenActive(function ($value) {
            CRUD::addClause('where', 'custpk', $value);
        });
        CRUD::filter('profilepk')->type('text')->label('Supplier')->whenActive(function ($value) {
            CRUD::addClause('where', 'profilepk', $value);
        });
        CRUD::filter('invpk')->type('text')->label('Invoice')->whenActive(function ($value) {
            CRUD::addClause('where', 'invpk', $value);
        });
    }

    protected function setupShowOperation()
    {
        $this->crud->addColumns([
            ['name' => 'to', 'limit' => 1000],
            ['name' => 'cc', 'limit' => 1000],
            ['name' => 'bcc', 'limit' => 1000],
            ['name' => 'customer.company','label'=>'Customer',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->custpk !== null) {
                            return sophio_route('fbs/customer.edit', ['id' => $entry->custpk]);
                        } else {
                            return "";
                        }

                    },
                    'target' => '_blank',

                ]
            ],
            ['name' => 'supplier.NAME','label'=>'Supplier',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->profilepk !== null) {
                            return sophio_route('fbs/supplier.edit', ['id' => $entry->profilepk]);
                        }
                    },
                    'target' => '_blank',

                ]
            ],
            ['name' => 'invpk', 'limit' => 1000,'label'=>'Invoice',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->invpk !== null) {
                            return sophio_route('fbs/fbsorder.main', ['id' => $entry->invpk]);
                        }
                    },
                    'target' => '_blank',

                ]
            ],
            ['name' => 'created_at', 'type' => 'datetime'],
            ['name' => 'subject', 'limit' => 1000],
            ['name' => 'body', 'limit' => 100000, 'escaped' => false],

            'from',
           ]);
    }
}