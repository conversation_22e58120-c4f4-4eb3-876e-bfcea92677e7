<?php

namespace App\Http\Controllers\Admin\FBS;


use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;
use Sophio\Common\ShipStation\src\Models\ShipStationWarehouse;

class ShipstationWarehouseCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;

    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use SupplierTrait;

    protected ShipStationManager $shipstationManager;
    protected Supplier $supplier;

    public function setup()
    {
        config(['tenant_db' => config('sophio.admin.default_database')]);
        $this->shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
        $this->supplier = Supplier::where('PK', \Route::current()->parameter('sid'))->first();

        $this->shipstationManager->setSupplierCreds($this->supplier);
        try {
            $this->shipstationManager->createClient();
        } catch (\Exception $e) {

        }
        $this->crud->setModel(ShipStationWarehouse::class);
        $this->crud->addClause('where', 'profilepk', \Route::current()->parameter('sid'));
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/' . \Route::current()->parameter('sid') . '/shipstation/warehouse' .
            \Route::current()->parameter('warehouseId'));
        CRUD::setEntityNameStrings('Supplier Shipstation warehouse', 'Supplier Shipstation warehouses');
    }


    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        try {
            $response = $this->shipstationManager->deleteWarehouse($this->crud->getCurrentEntry()->warehouseId);
        } catch (\Exception $e) {
            Log::channel('shipstation')->error($e->getMessage());
        }
        return $this->crud->delete($this->crud->getCurrentEntry()->id);
    }

    protected function setupListOperation()
    {
        $this->topbarSupplier($this->supplier);
        $this->shipstationManager->listWarehouses();
        $this->crud->addColumns([
            ['name' => 'warehouseId', 'label' => 'Warehouse ID'],
            ['name' => 'warehouseName', 'label' => 'Warehouse Name'],
            ['name' => 'originAddress.name', 'label' => 'Location Name'],
            ['name' => 'originAddress.company', 'label' => 'Location Company'],
            ['name' => 'originAddress.street1', 'label' => 'Location Address 1'],
            ['name' => 'originAddress.city', 'label' => 'Location City'],
            ['name' => 'originAddress.postalCode', 'label' => 'Location Postal Code'],
            ['name' => 'isDefault', 'label' => 'Default', 'type' => 'boolean', 'options' => [0 => 'No', 1 => 'Yes'],
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        if ($column['text'] === 'Yes') {
                            return 'badge badge-success';
                        }

                        return 'badge badge-default';
                    },
                ],],
        ]);
    }

    protected function setupCreateOperation()
    {
        $this->topbarSupplier($this->supplier);
        $subfields = [
            'name' => [
                'name' => 'name',
                'label' => 'name',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'company' => [
                'name' => 'company',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'street1' => [
                'name' => 'street1',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'street2' => [
                'name' => 'street2',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'street3' => [
                'name' => 'street3',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'city' => [
                'name' => 'city',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'state' => [
                'name' => 'state',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'postalCode' => [
                'name' => 'postalCode',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'country' => [
                'name' => 'country',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'phone' => [
                'name' => 'phone',
                'label' => 'Phone',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'residential' => [
                'name' => 'residential',
                'label' => 'Residential',
                'type' => 'switch',
                'attributes' => [
                    'class' => 'form-control form-control-sm'
                ]],
            'addressVerified' => [
                'name' => 'addressVerified',
                'label' => 'Address Verified (read-only)',
                'type' => 'text',
                'attributes' => [
                    'class' => 'form-control form-control-sm ', 'readonly' => 'readonly',
                ]],
        ];
        $this->crud->addFields(
            [
                ['name' => 'warehouseName', 'label' => 'Warehouse Name'],
                ['name' => 'isDefault', 'type' => 'switch'],
                [
                    'name' => 'originAddress',
                    'type' => 'jsontable',
                    'label' => 'Origin Address',
                    'fields' => $subfields,
                ],
                [
                    'name' => 'returnAddress',
                    'type' => 'jsontable',
                    'label' => 'Return Address',
                    'fields' => $subfields,
                ],

            ]
        );
    }

    protected function setupUpdateOperation()
    {

        ShipStationWarehouse::saved(function ($entry) {


            $warehouse = [
                'warehouseId' => $entry->warehouseId,
                'warehouseName' => $entry->warehouseName,
                'originAddress' => $entry->originAddress,
                'returnAddress' => $entry->returnAddress,
                'isDefault' => $entry->isDefault == 1 ? true : false,

            ];
            if($warehouse['originAddress']['residential']=="0") {
                $warehouse['originAddress']['residential'] = null;
            }else{
                $warehouse['originAddress']['residential'] = true;
            }
            if($warehouse['returnAddress']['residential']=="0") {
                $warehouse['returnAddress']['residential'] = null;
            }else{
                $warehouse['returnAddress']['residential'] = true;
            }


            $response = $this->shipstationManager->updateWarehouse($warehouse);
        });

        $this->setupCreateOperation();
    }
}