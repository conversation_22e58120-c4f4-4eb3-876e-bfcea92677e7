<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\Widget;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Repository\Settings;
use Sophio\FBSReturns\Library\ReturnsService;
use Sophio\FBSReturns\src\Models\ReturnReasons;

trait ReturnsTrait
{
    protected function addTopBar()
    {
        return;
        $content = [];
        $content[] = [
            'type' => 'link',
            'href' => backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/purchaseorder/finder'),
            'label' => 'Purchase orders'
        ];
        $content[] = [
            'type' => 'link',
            'href' => backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/returns/returnsreport'),
            'label' => 'Returns Report'
        ];
        $content[] = [
            'type' => 'link',
            'href' => backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/manifest/dashboard'),
            'label' => 'Manifest Dashboard'
        ];
        $content[] = [
            'type' => 'menu',
            'label' => 'CRUD',
            'menu' => [
                [
                    'type' => 'link',
                    'href' => backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/returns'),
                    'label' => 'Returns'
                ],
                [
                    'type' => 'link',
                    'href' => backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/returnlines'),
                    'label' => 'Return Lines'
                ],
            ]
        ];
        Widget::add([
                'type' => 'topbar',
                'content' => $content
            ]
        )->to('before_content');
    }
    public function rma($db,$invpk)
    {

        $invoice = Invoice::find($invpk);
        if (!$invoice) {
            throw new Exception('Invalid invoice id!');
        }
        $step = request()->get('step');
        $returnService = new ReturnsService(new Settings([]));
        $is_customer = true;
        if(backpack_user()) {
            $is_customer = backpack_user()->hasRole('customer') ;
        }else{
          if(cust_token_dec(request()->get('token'))){
              $is_customer =  true;
          }else{$is_customer=false;}
        }
        if (!$step) {

            return view('admin.fbs.returns.rma_one', ['invoice' => $invoice, 'returnService' => $returnService, 'reasons' => ReturnReasons::orderBy('name')->get(),'is_customer'=>$is_customer ]);
        }
        if ($step == "1") {
            $returnlines_input = request()->get('returnline');
            $qty_input = request()->get('qty');
            $rma_input = request()->get('rma');
            $reasons_input = request()->get('reason');
            $returnlines = [];
            $subtotal = 0;
            foreach ($returnlines_input as $lineitempk) {

                if ((int)$qty_input[$lineitempk] > 0 && (int)$reasons_input[$lineitempk] > 0) {
                    $returnlines[$lineitempk] = [
                        'qty' => $qty_input[$lineitempk] ?? 0,
                        'rma' => $rma_input[$lineitempk] ?? '',
                        'reason' => $reasons_input[$lineitempk] ?? 0,
                        'lineitem' => $invoice->lineitem->find($lineitempk)
                    ];
                    $subtotal += $qty_input[$lineitempk] * $invoice->lineitem->find($lineitempk)->price;

                }
            }
            return view('admin.fbs.returns.rma_two', ['invoice' => $invoice, 'reasons' => ReturnReasons::orderBy('name')->get(), 'returnlines' => $returnlines, 'subtotal' => $subtotal,'is_customer'=>$is_customer ]);

        } elseif ($step == "2") {

            $returnlines_input = request()->get('returnline');
            $qty_input = request()->get('qty');
            $rma_input = request()->get('rma');
            $reasons_input = request()->get('reason');
            $return_data = [
                'custnotes' => request()->get('custnotes'),
                'items' => []
            ];
            foreach ($returnlines_input as $lineitempk) {
                $return_data['buyrmaid'] = $rma_input[$lineitempk];
                $return_data['items'][$lineitempk] = [
                    'linepk' => $lineitempk,
                    'qty' => $qty_input[$lineitempk] ?? 0,
                    'buyrmaid' => $rma_input[$lineitempk] ?? '',
                    'retCode' => $reasons_input[$lineitempk] ?? 0,
                    'notes' => request()->get('custnotes')
                ];
            }
            $return = $returnService->createReturnForInvoice($invoice, $return_data);
            return view('admin.fbs.returns.rma_three', ['invoice' => $invoice, 'return' => $return, 'reasons' => ReturnReasons::orderBy('name')->get(),'is_customer'=>$is_customer ]);
        }
    }
}