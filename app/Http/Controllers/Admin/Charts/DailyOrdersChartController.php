<?php

namespace App\Http\Controllers\Admin\Charts;

use App\Models\Orders\Order;
use Backpack\CRUD\app\Http\Controllers\ChartController;
use ConsoleTVs\Charts\Classes\Chartjs\Chart;
use Illuminate\Support\Facades\DB;

class DailyOrdersChartController extends ChartController
{
    public function setup()
    {
        $database = \Route::current()->parameter('database');
        $this->chart = new Chart();
        // MANDATORY. Set the labels for the dataset points
        $labels = [];
        $dt = now('CDT')->toArray();
        $hour =$dt['hour'];
        for ($days_backwards = 0 ; $days_backwards <=$hour+1; $days_backwards++) {

            $labels[] = today('CDT')
                ->addHours($days_backwards)->hour;
        }
        $this->chart->labels($labels);

        // RECOMMENDED.
        // Set URL that the ChartJS library should call, to get its data using AJAX.
        $this->chart->load(backpack_url('charts/' . $database . '/dailyorders'));

        // OPTIONAL.
        $this->chart->minimalist(false);
        $this->chart->displayLegend(true);


    }


    /**
     * Respond to AJAX calls with all the chart data points.
     *
     * @return json
     */
    public function data()
    {
        $w = [];$f = []; $four = []; $eight = []; $o= []; $orders= [];
        $dt = now('CDT')->toArray();
        $hour =$dt['hour'];
        \Log::error($hour);
        \Log::error(now('CDT')->toArray());
        for ($days_backwards = 0 ; $days_backwards <=$hour+1; $days_backwards++) {

           \Log::error(today('CDT')->addHours($days_backwards));
            $dbs = Order::where('dateCreated','>=', today('CDT')
                ->addHours($days_backwards))
                ->where('dateCreated','<', today('CDT')
                    ->addHours($days_backwards+1))
                ->groupby('invstatus')->select('invstatus',DB::raw('count(*) as cnt'))->get();

            $total = 0;
            $w[$days_backwards] =0;
            $f[$days_backwards] =0;
            $four[$days_backwards] =0;
            $eight[$days_backwards] =0;
            $o[$days_backwards] =0;

            foreach ($dbs as $i=>$d) {

                switch ($d->invstatus) {
                    case 'F':
                        $f[$days_backwards] = $d->cnt;
                        break;
                    case 'W':
                        $w[$days_backwards] = $d->cnt;
                        break;
                    case '4':
                        $four[$days_backwards] = $d->cnt;
                        break;
                    case '8':
                        $eight[$days_backwards] = $d->cnt;
                        break;
                    case 'O':
                        $o[$days_backwards] = $d->cnt;
                        break;
                }
                $total = $total + $d->cnt;
            }

            $orders[] = $total;

        }

        $this->chart->dataset('Total', 'line', $orders)
            ->color('rgb(0,0,0)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');
        $this->chart->dataset('In Warehouse (W)', 'line', $w)
            ->color('rgb(96, 92, 168)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');
        $this->chart->dataset('Shipped (F)', 'line', $f)
            ->color('rgb(255, 193, 7)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');
        $this->chart->dataset('Cancel (4)', 'line', $four)
            ->color('rgba(70, 127, 208, 1)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');
        $this->chart->dataset('Partial refurnd (8)', 'line', $eight)
            ->color('rgb(77, 189, 116)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');
        $this->chart->dataset('New order (O)', 'line', $o)
            ->color('rgb(255, 0, 0)')->backgroundColor('rgba(255, 255, 255, 0.4)');
    }
}