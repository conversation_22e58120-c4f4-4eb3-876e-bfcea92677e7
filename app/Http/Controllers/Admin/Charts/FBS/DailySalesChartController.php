<?php

namespace App\Http\Controllers\Admin\Charts\FBS;

use App\Models\Orders\Order;
use Backpack\CRUD\app\Http\Controllers\ChartController;
use ConsoleTVs\Charts\Classes\Highcharts\Chart;
use Illuminate\Support\Facades\DB;

class DailySalesChartController extends ChartController
{
    public function setup()
    {
        $database = \Route::current()->parameter('database');
        $this->chart = new Chart();
        // MANDATORY. Set the labels for the dataset points
        $labels = [];

        $d = array();
        for ($i = 31; $i >= 1; $i--)
            $labels[] = date("l d M", strtotime('-' . $i . ' days'));

        $this->chart->labels($labels);

        // RECOMMENDED.
        // Set URL that the ChartJS library should call, to get its data using AJAX.
        $this->chart->load(backpack_url('charts/' . $database . '/fbs/fbsorderdashboard/dailysales'));

        // OPTIONAL.
        $this->chart->minimalist(false);
        $this->chart->displayLegend(true);
        $this->chart->options(['tooltip'=>['shared'=>true]]);

    }


    /**
     * Respond to AJAX calls with all the chart data points.
     *
     * @return json
     */
    public function data()
    {
        $d = now('CDT')->toArray();


        $i = 0;

        $database = \Route::current()->parameter('database');
        $ccustpks = DB::select("SELECT custtype FROM " . $database . ".wws_invoice WHERE invstatus='F' AND  invdate>= DATE_SUB(NOW(), INTERVAL 30 DAY)  GROUP BY custtype");
        $ddabs = DB::select("SELECT round(sum(invtotal),2) as cnt,custtype,DATEDIFF(NOW(),invdate) AS dayt FROM " . $database . ".wws_invoice WHERE invstatus='F' AND  invdate>= DATE_SUB(NOW(), INTERVAL 30 DAY)  GROUP BY  DATE_FORMAT(invdate, '%Y%m%d'),custtype ORDER BY dayt desc");
        $days = [];
        $orders = [];
        foreach ($ccustpks as $d) {
            $custpks[] = $d->custtype;
            $orders[$d->custtype] = [];
        }
        foreach ($ddabs as $d) {

            if (!isset($days[$d->dayt])) {
                $days[$d->dayt] = [];
            }
            $days[$d->dayt][$d->custtype] = $d->cnt;
        }
        foreach ($custpks as $custpk) {
            for ($day = 31; $day >= 0; $day--) {
                if (!isset($days[$day][$custpk])) {
                    $orders[$custpk][] = 0;
                } else {
                    $orders[$custpk][] = $days[$day][$custpk];
                }
            }

            $this->chart->dataset($custpk, 'line', $orders[$custpk])
                ->color('rgb('.rand(0,255).','.rand(0,255).','.rand(0,255).')')
                ;

        }


    }
}