<?php

namespace App\Http\Controllers\Admin\Charts;

use Backpack\CRUD\app\Http\Controllers\ChartController;
use ConsoleTVs\Charts\Classes\Chartjs\Chart;

class WalmartMarketItemsStatusChartController extends ChartController
{
    public function setup()
    {
        $database = \Route::current()->parameter('database');
        $this->chart = new Chart();
        // MANDATORY. Set the labels for the dataset points
        $labels = [];
    }
}