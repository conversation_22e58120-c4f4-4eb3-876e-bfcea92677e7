<?php

namespace App\Http\Controllers\Admin\Customer;

use App\Http\Controllers\Admin\FBS\CustomerTrait;
use App\Models\SophioUser;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Customer;

use Sophio\Common\UserProfile;
use Spatie\Permission\Models\Permission;

class CustomerUsersCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation {
        destroy as traitDestroy;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation {
        store as traitStore;
    }
    use CustomerTrait;

    protected $customer;

    public function __construct()
    {
        parent::__construct();
    }

    public function setupExtraRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/{id}/loginsite', [
            'as' => $routeName . '.loginsite',
            'uses' => $controller . '@loginsite',
            'operation' => 'loginsite',
        ]);


    }

    public function loginsite($db, $id)
    {
        $user = \App\Models\SophioUser::find($id);
        if ($user->remember_token == '') {
            $user->remember_token = substr(md5(mt_rand()), 0, 7);
            $user->save();
        }

        if ($user->salesrep()) {
            return redirect()->to(sophiosettings()->getStore()['VIRTUAL'] . 'logintoken?token=' . $user->remember_token.'&impersonator='.backpack_user()->id.'&redirect_url=/account/orders');
        } else {
            return redirect()->to(sophiosettings()->getStore()['VIRTUAL']  . 'logintoken?token=' . $user->remember_token.'&impersonator='.backpack_user()->id.'&redirect_url=/account/users');
        }

    }

    public function setup()
    {
        $this->crud->setModel(\App\Models\SophioUser::class);
        if (backpack_user()->hasRole(['super-admin|store-admin'])) {
            if (request()->route()->parameter('custpk')) {
                $this->customer = $customer = Customer::find(request()->route()->parameter('custpk'));
                CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/customer/' . $customer->pk . '/users');
                $this->customerTopBar($customer);
            }
        } else {
            $this->customer = $customer = backpack_user()->related_models->where('profilable_type', 'customer')[0]->related;
            CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/customer/users');
        }

        if (isset($customer)) {
            $this->crud->query->whereHas('profilables', function ($query) use ($customer) {
                $query->where('profilable_type', 'customer');
                $query->where('profilable_id', $customer->pk);
            });
            $this->crud->query->select('users.*');

            CRUD::setEntityNameStrings('User', 'Users');

            \App\Models\SophioUser::created(function ($entry) use ($customer) {
                $entry->assignRole('customer');
                $entry->remember_token = sha1($entry->name . time());
                $entry->user_key = $customer->pk;
                $entry->active = 1;
                $entry->save();
                UserProfile::create([
                    'sophio_user_id' => $entry->id,
                    'profilable_type' => 'customer',
                    'profilable_id' => $customer->pk,
                ]);
            });
        }

    }

    protected function setupListOperation()
    {
        $this->crud->addFilter(
            [
                'name' => 'role',
                'type' => 'dropdown',
                'label' => trans('backpack::permissionmanager.role'),
            ],
            config('permission.models.role')::whereIn('name',['customer','salesrep','sales','supplier'])->pluck('name', 'id')->toArray(),
            function ($value) { // if the filter is active
                $this->crud->addClause('whereHas', 'roles', function ($query) use ($value) {
                    $query->where('role_id', '=', $value);
                });
            }
        );

        $this->crud->addColumns([
            'email', 'fullname',
            [
                'name' => 'salesrep',
                'label' => 'Type',
                'orderable' => true,

                'value' => function ($entry) {
                        return implode(',',$entry->getRoleNames()->toArray());

                }
            ],

            [
                'name' => 'login',
                'label' => 'Login',
                'value' => function ($model) {
                    return 'Login as ' . $model->namete;
                },
                'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/customer/users/' . $entry->id . '/loginsite');
                    },
                    'target' => '_blank',

                ],
            ],

        ]);
    }

    public function store()
    {
        CRUD::setRequest(CRUD::validateRequest());

        /** @var \Illuminate\Http\Request $request */
        $request = CRUD::getRequest();

        // Encrypt password if specified.
        if ($request->input('password') && $request->input('password') !== "") {
            $request->request->set('password', Hash::make($request->input('password')));
        } else {
            $request->request->remove('password');
        }

        CRUD::setRequest($request);
        CRUD::unsetValidation(); // Validation has already been run

        return $this->traitStore();
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        $id = $this->crud->getCurrentEntryId() ?? $id;
        UserProfile::where('sophio_user_id', $id)->delete();
        return $this->crud->delete($id);
    }

    protected function setupCreateOperation()
    {
        CRUD::addSaveAction([
            'name' => 'save_action_one',
            'redirect' => function($crud, $request, $itemId) {
                $user = \App\Models\SophioUser::find($itemId);
                if ($user->remember_token == '') {
                    $user->remember_token = substr(md5(mt_rand()), 0, 7);
                    $user->save();
                }
                return sophiosettings()->getStore()['VIRTUAL']  . 'logintoken?token=' . $user->remember_token.'&redirect_url=/account/users';

            },
            'button_text' => 'Create and Login',

            'visible' => function($crud) {
                return true;
            },
            'referrer_url' => function($crud, $request, $itemId) {
                return $crud->route;
            },
            'order' => 1,
        ]);
        $name = $this->customer->firstname . $this->customer->lastname !== "" ? $this->customer->firstname . ' ' . $this->customer->lastname : ($this->customer->company !== null ? $this->customer->company . random_int(0, 20) : '');
        $username = strtolower(unformatString($name));
        $found = true;
        do {
            if(SophioUser::where('name', $username)->exists()) {
                $username = $username.random_int(0, 9);
            } else {
                $found = false;
            }
        } while ($found);

        $this->crud->addFields(
            [
                [
                    'name' => 'email',
                    'label' => 'Email',
                    'type' => 'text',
                    'default' => $this->customer->email,
                ],
                [
                    'name' => 'name',
                    'label' => 'Username',
                    'type'  => 'slug',
                    'remove' => '/[*-+~.()!:@]/g',
                    'strict' => true,
                    'lower' => false,
                    'separator' => '', // separator to use
                    'replacement'=>'',
                    'trim' => true, // trim whitespace
                    'target'  => 'email',
                    'default' =>$username,
                ],
                [
                    'name' => 'fullname',
                    'label' => 'Full name',
                    'type' => 'text',
                    'default' => $name,
                ],

                [
                    'name' => 'password',
                    'label' => 'Password',
                    'type' => 'text',
                    'default' => substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10)
                ],
                [
                    'name' => 'phone',
                    'label' => 'Phone',
                    'type' => 'text',
                    'default' => notNullAndNotEmpty($this->customer->mobile) ? $this->customer->mobile : $this->customer->phone,
                ],

                [
                    'name' => 'description',
                    'label' => 'Note',
                    'type' => 'text'
                ],
                [
                    'name' => 'description',
                    'label' => 'Note',
                    'type' => 'text',
                    'default' => 'Added by ' . backpack_user()->name
                ],

            ]
        );
        $this->crud->setValidation([
            'email' => 'required|email|unique:' . config('permission.table_names.users', 'users') . ',email',
            'name' => 'required|string|alpha_num|unique:' . config('permission.table_names.users', 'users') . ',name',

        ]);
    }

    protected function setupUpdateOperation()
    {
        $id= $this->crud->getCurrentEntryId();

        $this->crud->addFields(
            [
                [
                    'name' => 'email',
                    'label' => 'Email',
                    'type' => 'text'
                ],
                [
                    'name' => 'name',
                    'label' => 'Username',
                    'type'  => 'slug',
                    'remove' => '/[*-+~.()!:@]/g',
                    'strict' => true,
                    'lower' => false,
                    'separator' => '', // separator to use
                    'replacement'=>'',
                    'trim' => true, // trim whitespace
                    'target'  => 'email',
                ],
                [
                    'name' => 'fullname',
                    'label' => 'Full name',
                    'type' => 'text'
                ],

                [
                    'name' => 'password',
                    'label' => 'Password',
                    'type' => 'password',
                    'hint' => 'Leave empty to not change it.'
                ],
                [
                    'name' => 'phone',
                    'label' => 'Phone',
                    'type' => 'text'
                ],

                [
                    'name' => 'description',
                    'label' => 'Note',
                    'type' => 'text'
                ],
                [
                    'name' => 'description',
                    'label' => 'Note',
                    'type' => 'text'
                ],
                [   // Checklist
                    'label'     => 'Roles',
                    'type'      => 'checklist',
                    'name'      => 'roles',
                    'entity'    => 'roles',
                    'attribute' => 'name',
                    'model'     => "Backpack\PermissionManager\app\Models\Role",
                    'pivot'     => true,
                    'show_select_all' => true, // default false
                    // 'number_of_columns' => 3,
                    'options' => (function ($query) {
                        return $query->whereIn('name', ['customer','salesrep','sales']);
                    }),
                ]
            ]
        );
        $cuser = $this->crud->getCurrentEntry();
        if($cuser->hasRole('salesrep') || $cuser->hasRole('sales')) {
          $this->crud->addField([
              'name' =>'salesrep',
              'subfields' => [
                  ['name'=>'maxordersize','type'=>'number'],
                  ['name'=>'no_shipto_override','type'=>'switch','Do not allow to set a custom shipping address'],
              ]
          ]);
        }
        $this->crud->setValidation([
            'email'    => 'required|unique:'.config('permission.table_names.users', 'users').',email,'.$id,
            'name'     => 'required|unique:'.config('permission.table_names.users', 'users').',name,'.$id,
         ]);
    }
}