<?php

namespace App\Http\Controllers\Admin\Customer;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Facades\Route;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\ApiKeys;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Repository\Settings;
use Sophio\FBSOrder\Library\Actions\MarketingRebate;
use Sophio\Stripe\src\Library\StripeManager;

class CustomerAccountController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;

    public function __construct()
    {


        parent::__construct();
    }

    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        $this->crud->setModel(Customer::class);
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/customer/account');
        CRUD::setEntityNameStrings('customer', 'Customer');
        $this->crud->addClause('where', 'pk', backpack_user()->user_key);

        /*
         * This is a bit more complicated: a custom Unique rule must be made, but also in ftpusers we
         * would need to store the entity (customer/supplier) id, so we can perform uniqueness
         * (otherwise you don't know if the tested value is a new attempt or tests existing value that already belongs
         * to entity
        $this->crud->setValidation([
            'FTPUSERID'=>['unique:facetednew.ftpusers,username,pk',new LocalFtpUser()]
            ]
        );
        */
        setLayoutBoxed();
    }

    public function setupExtraRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . 'payment', [
            'as' => $routeName . '.payment',
            'uses' => $controller . '@payment',
            'operation' => 'payment',
        ]);
        Route::any($segment . 'stripeportalsession', [
            'as' => $routeName . '.stripeportalsession',
            'uses' => $controller . '@stripeportalsession',
            'operation' => 'stripeportalsession',
        ]);
        Route::any($segment . 'stripecreatecustomer', [
            'as' => $routeName . '.stripecreatecustomer',
            'uses' => $controller . '@stripecreatecustomer',
            'operation' => 'stripecreatecustomer',
        ]);
        Route::any($segment . '/rebates', [
            'as' => $routeName . '.rebates',
            'uses' => $controller . '@rebates',
            'operation' => 'rebates',
        ]);
        Route::any($segment . '/{id}/api', [
            'as' => $routeName . '.api',
            'uses' => $controller . '@api',
            'operation' => 'api',
        ]);
    }

    public function stripecreatecustomer()
    {
        $customer = $this->crud->query->first();
        $settings = new Settings();
        $settings->setSettingsByCustomer($customer);
        $stripe = new StripeManager($settings);
        if ($stripe->createCustomer($customer)) {
            return redirect()->to(sophio_route('customer/account.stripeportalsession'));
        }

        return view('admin.customer.payment', ['customer' => $customer, 'error' => 'Failed to create customer in Stripe.Please contact support.']);

    }

    public function stripeportalsession()
    {
        $customer = $this->crud->query->first();
        $settings = new Settings();
        $settings->setSettingsByCustomer($customer);
        if ($customer->paymethod == "ST" && isset($customer->xml['STRIPECUSTOMERID']) && $customer->xml['STRIPECUSTOMERID'] !== "") {
            $stripe = new StripeManager($settings);
            $portal = $stripe->getClient()->billingPortal->sessions->create([
                'customer' => $customer->xml['STRIPECUSTOMERID'],
                'return_url' => route('backpack.dashboard')
            ]);
            return redirect($portal->url);
        } else {

        }
    }

    public function api()
    {
    //    config([str_replace('::', '.', config('backpack.ui.view_namespace')) . 'options.useFluidContainers' => true]);
        $customer = $this->crud->query->first();
        $this->submenu($customer);
        $apikeys = ApiKeys::where('custpk', $customer->pk)->get();
        return view('admin.customer.api', ['customer' => $customer, 'apikeys' => $apikeys]);
    }

    public function payment()
    {
        $customer = $this->crud->query->first();

        return view('admin.customer.payment', ['customer' => $customer]);
    }

    public function rebates()
    {
        $message = "";
        if (request()->post()) {
            $message = (new MarketingRebate())(request()->get('custpk'), request()->get('when'));
        }
        $custpk = backpack_user()->user_key;
        return view('admin.customer.rebate', ['message' => $message, 'custpk' => $custpk]);
    }

    public function submenu($customer)
    {
        $content = [
            [
                'type' => 'text',
                'label' => 'Your Customer ID is ' . $customer->pk. ' on Store ID '.$customer->storepk
            ]];
        $content[] =
            [
                'type' => 'link',
                'href' => sophio_route('customer/account.edit', ['id' => $customer->pk]),
                'label' => 'Edit Settings',

            ];
        $content[] =
            [
                'type' => 'link',
                'href' => sophio_route('customer/account.api', ['id' => $customer->pk]),
                'label' => 'API Keys',

            ];
        Widget::add([
            'type' => 'topbar',
            'content' => $content
        ])->to('before_content');

    }

    protected function setupUpdateOperation()
    {
        $customer = $this->crud->query->first();
        $this->submenu($customer);
        $this->crud->setSubheading(" ");
        $this->crud->setOperationSetting("resetButton", false);
        $this->crud->setHeading(" ");
        CRUD::setOperationSetting('showEntryCount', true);
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        $fields=[
            [
                'name' => 'company',
                'label' => 'Company',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-12',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'lastname',
                'label' => 'Last Name',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'firstname',
                'label' => 'First Name',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'address',
                'label' => 'Address',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-12',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'address2',
                'label' => 'Address2',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-12',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'city',
                'label' => 'City',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'county',
                'label' => 'county',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'state',
                'label' => 'state',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'zip',
                'label' => 'zip',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'phone',
                'label' => 'phone',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'fax',
                'label' => 'fax',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'taxid',
                'label' => 'taxid',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-12',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'email',
                'label' => 'email',
                'type' => 'text',
                'tab' => 'Contact'
            ],


        ];
        if(sophiosettings()->getStore()->STORETYPE=='TE') {
            $fields[]=    [
                'name' => 'SALESREP',
                'label' => 'Enable Sales Reps',

                'limit' => 1000,
                'fake' => true,
                'store_in' => 'xml',
                'type' => 'select_from_array',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Enabled',

                ],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Sales'
            ];
            $fields[] =[
                'name' => 'NOSHIPTOOVERRIDE',
                'label' => 'Disallow Sales Rep to use custom shipping addresses',

                'limit' => 1000,
                'fake' => true,
                'store_in' => 'xml',
                'type' => 'select_from_array',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Enabled',

                ],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Sales'
            ];
            $fields=  [
                'name' => 'SALERSREP_MAXORDERSIZE',
                'label' => 'Max order size for sales rep.',
                'type' => 'number', 'attributes' => ['step' => 'any'],
                'decimals' => 2,
                'prefix' => '$',
                'default' => 0.01,
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Sales'
            ];
        }

        $this->crud->addFields($fields);
    }

}