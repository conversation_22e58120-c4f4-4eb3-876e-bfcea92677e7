<?php

namespace App\Http\Controllers\Admin\NonApps;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use App\Http\Requests\NonAppSubCategoryRequest;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;


class SubCategoryCrudController extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\BulkDeleteOperation;
    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        parent::setup();

        CRUD::setModel(\App\Models\NonApps\SubCategory::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/nonappssubcategory');
        CRUD::setEntityNameStrings('nonappssubcategory', \Route::current()->parameter('database') . ' \  Non Apps Categories');

    }

    protected function setupListOperation()
    {
        $this->crud->addClause('where', 'clientId', '=',$this->clientId);
        $this->crud->orderBy('SubCategoryName', 'ASC');
        $this->crud->addColumns([

            ['name'=>'SubCategoryName','orderable'=>true],
            [
                'name'  => 'image_url', // The db column name
                'label' => 'Image', // Table column heading
                'type'  => 'image',
                'prefix' => 'https://images-us1.sophio.com'
            ]]);
        $this->addCustomCrudFilters();
        $this->crud->enableExportButtons();

    }

    protected function setupShowOperation()
    {
        $this->addCustomCrudFilters();
        $this->crud->addColumns($this->getFields());
    }

    protected function setupCreateOperation()
    {
        $this->crud->set('show.setFromDb', false);
        $this->crud->setValidation(NonAppSubCategoryRequest::class);
        $this->crud->addFields($this->getFields());
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(NonAppSubCategoryRequest::class);
        $this->setupCreateOperation();

    }

    public function getFields()
    {
        return [

            [
                'name' => 'SubCategoryName',
                'label' => 'SubCategoryName',
                'type' => 'text'
            ],

            [ // image
                'label' => 'image_url',
                'name' => 'image_url',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'disk' => 'imagespool'

            ],


        ];
    }

    protected function addCustomCrudFilters()
    {
        $this->crud->addFilter(
            [ // text filter
                'type'  => 'text',
                'name'  => 'SubCategoryName',
                'label' => 'SubCategoryName',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'SubCategoryName', 'LIKE', "%$value%");
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'checkbox',
                'label' => 'No main image',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'image_url', '');
            }
        );

    }
}