<?php

namespace App\Http\Controllers\Admin\NonApps;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use App\Http\Requests\NonAppManufacturerRequest;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\DB;


class ManufacturerCrudController extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\BulkDeleteOperation;
    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        session(['clientId'=>request()->get('clientId')]);
        parent::setup();
        CRUD::setModel(\App\Models\NonApps\Manufacturer::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/nonappsmanufacturer');
        CRUD::setEntityNameStrings('nonappsmanufacturer', \Route::current()->parameter('database') . ' \  Non Apps Categories');
        $this->crud->enableExportButtons();
    }

    protected function setupListOperation()
    {

        $this->crud->orderBy('mfg_name', 'ASC');
        $this->crud->addColumns([

            ['name'=>'mfg_name',    'label' => 'Manufacturer Name',
                'type' => 'text'],
           ]);
        $this->addCustomCrudFilters();
        $this->crud->enableExportButtons();

    }

    protected function setupShowOperation()
    {
        $this->addCustomCrudFilters();
        $this->crud->addColumns($this->getFields());
    }

    protected function setupCreateOperation()
    {
        $this->crud->set('show.setFromDb', false);
        $this->crud->setValidation(NonAppManufacturerRequest::class);
        $this->crud->addFields($this->getFields());
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(NonAppManufacturerRequest::class);
        $this->setupCreateOperation();

    }

    public function getFields()
    {
        return [

            [
                'name' => 'mfg_name',
                'label' => 'Manufacturer Name',
                'type' => 'text'
            ]

        ];
    }

    protected function addCustomCrudFilters()
    {
        $this->crud->addFilter(
            [ // text filter
                'type'  => 'text',
                'name'  => 'mfg_name',
                'label' => 'Manufacturer Name',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'ManufacturerName', 'LIKE', "%$value%");
            }
        );


    }
}