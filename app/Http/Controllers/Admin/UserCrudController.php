<?php

namespace App\Http\Controllers\Admin;

use App\Models\SophioUser;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use App\Http\Requests\UserStoreCrudRequest as StoreRequest;
use  App\Http\Requests\UserUpdateCrudRequest as UpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Route;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Auth\Invite;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Models\UserInvite;
use Sophio\Common\Repository\Settings;
use Sophio\Common\UserProfile;

class UserCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation {
        store as traitStore;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation {
        update as traitUpdate;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \MHMartinez\ImpersonateUser\app\Http\Controllers\Operations\ImpersonateUserOperation {
        impersonateUser as impersonateUserOriginal;
    }
    use \Backpack\Pro\Http\Controllers\Operations\FetchOperation;

    public function setup()
    {

        $this->crud->setModel(config('backpack.permissionmanager.models.user'));
        $this->crud->setEntityNameStrings(trans('backpack::permissionmanager.user'), trans('backpack::permissionmanager.users'));
        $this->crud->setRoute(backpack_url('user'));


    }
    public function impersonateUser(int $id)
    {
        $user = SophioUser::find($id);
        $settings = new Settings();
        if($user->hasRole('salesrep|customer|sales|marketing') && $settings->get('IMPERSONATE_TO_FRONTEND')==='TRUE') {
            if($user->remember_token=='') {
                $user->remember_token = substr(md5(mt_rand()), 0, 7);
                $user->save();
            }
            if($user->hasRole('customer')) {
                return redirect()->to($settings->getStore()['VIRTUAL']. 'logintoken?token=' . $user->remember_token.'&impersonator='.backpack_user()->id.'&redirect_url=/account/users');
            }else{
                return redirect()->to($settings->getStore()['VIRTUAL']. 'logintoken?token=' . $user->remember_token.'&impersonator='.backpack_user()->id.'&redirect_url=/account/orders');
            }

        }else{
            return $this->impersonateUserOriginal($id);
        }

    }
    public function setupRoutes($segment, $routeName, $controller)
    {
        preg_match_all('/(?<=^|;)setup([^;]+?)Routes(;|$)/', implode(';', get_class_methods($this)), $matches);

        if (count($matches[1])) {
            foreach ($matches[1] as $methodName) {
                $this->{'setup'.$methodName.'Routes'}($segment, $routeName, $controller);
            }
        }
        Route::any($segment . '/{id}/invite', [
            'as' => $routeName . '.invite',
            'uses' => $controller . '@invite',
            'operation' => 'invite',
        ]);
    }
    public function invite( $id)
    {
        $user = \Sophio\Common\SophioUser::find($id);
        $invites = UserInvite::where('invited_user_id', $id)->get();
        if(request()->post()){
            $inviteManager = new Invite();
            $inviteManager->createInviteByUser($user,backpack_user());
            Alert::add('success', 'Invite sent!')->flash();
            return redirect()->back();
        }
        return view('admin.user.userinvite', ['invites' => $invites, 'user'=>$user]);
    }
    public function setupListOperation()
    {

       $this->crud->button('invite')->stack('line')->view('crud::buttons.quick')->meta([
            'access' => true,
            'label' => 'Invite',
            'icon' => 'la la-envelope',
            'wrapper' => [
                'element' => 'a',
                'href' =>function ($entry) {
                    return backpack_url('user/'.$entry->id.'/invite');
                },
                'target' => '_blank',
                'title' => 'Send a new email to this user',
            ]
        ]);
        $this->crud->orderBy('name');
        $this->crud->addColumns([
            [
                'name' => 'name',
                'label' => trans('backpack::permissionmanager.name'),
                'type' => 'text',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('name', 'like', '%' . $searchTerm . '%');
                }
            ], [
                'name' => 'fullname',
                'label' => "Full Name",
                'type' => 'text',

            ],
            [
                'name' => 'email',
                'label' => trans('backpack::permissionmanager.email'),
                'type' => 'email',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('email', 'like', '%' . $searchTerm . '%');
                }
            ],
            [ // n-n relationship (with pivot table)
                'label' => trans('backpack::permissionmanager.roles'), // Table column heading
                'type' => 'select_multiple',
                'name' => 'roles', // the method that defines the relationship in your Model
                'entity' => 'roles', // the method that defines the relationship in your Model
                'attribute' => 'name', // foreign key attribute that is shown to user
                'model' => config('permission.models.role'), // foreign key model
                'searchLogic' => false,
            ],
            [
                'name' => 'user_key',
                'label' => "Entity",
                'type' => 'text',
                'limit'=>50,
                'searchLogic' => false,
                'value' => function ($entry) {
                    if ($entry->hasRole(['supplier']) && $entry->user_key !== null) {
                        $supplier = Supplier::find($entry->user_key);
                        if ($supplier) {
                            return $supplier->NAME;
                        }
                    } else {

                    }
                    return "";
                }
            ],
            [
                'name' => 'customers',
                'label' => 'Customer Profile',
                'type' => 'relationship',

                'searchLogic' => false

            ],
            [
                'name' => 'suppliers',
                'label' => 'Supplier Profile',
                'type' => 'relationship',
                'attribute' => 'NAME',
                'searchLogic' => false


            ],

            [
                'name' => 'description',
                'limit'=>100,
                'label' => "Notes",
                'type' => 'text',
                'searchLogic' => false

            ],
        ]);

        if (backpack_pro()) {
            // Role Filter
            $this->crud->addFilter(
                [
                    'name' => 'role',
                    'type' => 'dropdown',
                    'label' => trans('backpack::permissionmanager.role'),
                ],
                config('permission.models.role')::all()->pluck('name', 'id')->toArray(),
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'roles', function ($query) use ($value) {
                        $query->where('role_id', '=', $value);
                    });
                }
            );

            // Extra Permission Filter
            $this->crud->addFilter(
                [
                    'name' => 'permissions',
                    'type' => 'select2',
                    'label' => trans('backpack::permissionmanager.extra_permissions'),
                ],
                config('permission.models.permission')::all()->pluck('name', 'id')->toArray(),
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'permissions', function ($query) use ($value) {
                        $query->where('permission_id', '=', $value);
                    });
                }
            );
            $this->crud->addFilter([
                'name'=>'custpk',
                'type'=>'text',],false,
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'profilables', function ($query) use ($value) {
                        $query->where('profilable_id', '=', $value);
                        $query->where('profilable_type', '=', 'customer');
                    });
                }
             );
            $this->crud->addFilter([
                'name'=>'supplierpk',
                'type'=>'text',],false,
                function ($value) { // if the filter is active
                    $this->crud->addClause('whereHas', 'profilables', function ($query) use ($value) {
                        $query->where('profilable_id', '=', $value);
                        $query->where('profilable_type', '=', 'supplier');
                    });
                }
            );
        }
    }

    public function setupCreateOperation()
    {
        $this->addUserFields();
        $this->crud->setValidation(StoreRequest::class);
    }

    public function setupUpdateOperation()
    {
        $this->addUserFields();
        $this->crud->setValidation(UpdateRequest::class);
    }

    /**
     * Store a newly created resource in the database.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store()
    {

        $this->crud->setRequest($this->crud->validateRequest());
        $this->crud->setRequest($this->handlePasswordInput($this->crud->getRequest()));
        $this->crud->unsetValidation(); // validation has already been run

        return $this->traitStore();
    }

    /**
     * Update the specified resource in the database.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update()
    {

        $this->crud->setRequest($this->crud->validateRequest());
        $this->crud->setRequest($this->handlePasswordInput($this->crud->getRequest()));
        $this->crud->unsetValidation(); // validation has already been run

        return $this->traitUpdate();
    }

    /**
     * Handle password input fields.
     */
    protected function handlePasswordInput($request)
    {
        // Remove fields not present on the user.
        $request->request->remove('password_confirmation');
        $request->request->remove('roles_show');
        $request->request->remove('permissions_show');

        // Encrypt password if specified.
        if ($request->input('password')) {
            $request->request->set('password', Hash::make($request->input('password')));
        } else {
            $request->request->remove('password');
        }

        return $request;
    }
    public function fetchCustomers()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        $customer = new Customer;
        $customer->setTable('wws_customers');
        return $this->fetch([
            'model' => $customer,
            'query' => function ($model) {
                $search = request()->input('q') ?? false;
                if ($search) {
                    return $model->where('company', 'LIKE', '%' . $search . '%')->orWhere('pk', $search);
                } else {
                    return $model;
                }
            }
        ]);
    }
    protected function addUserFields()
    {
        $this->crud->addFields([
            [
                'name'=>'active',
                'label'=>'Active',
                'type'        => 'select_from_array',
                'options'     => [

                    0 => "Disabled",
                    1 => "Enabled"
                ],
                'tab'=>'General'
            ],
            [
                'name' => 'email',
                'label' => trans('backpack::permissionmanager.email'),
                'type' => 'email',
                'tab'=>'General'
            ],
            [
                'name' => 'name',
                'label' => 'Username',
                'type'  => 'slug',
                'remove' => '/[*-+~.()!:@]/g',
                'strict' => true,
                'lower' => false,
                'separator' => '', // separator to use
                'replacement'=>'',
                'trim' => true, // trim whitespace
                'target'  => 'email',
                'hint' =>'Used for login',
                'tab'=>'General'
            ],
            [
                'name' => 'fullname',
                'label' => 'Full name',
                'type' => 'text',
                'hint' =>'Display Name',
                'tab'=>'General'
            ],
            [
                'name' => 'description',
                'label' => 'Description',
                'type' => 'text',
                'tab'=>'General'
            ],
            [
                'name' => 'phone',
                'label' => 'Phone',
                'type' => 'text',
                'tab'=>'General'
            ],

            [
                'name' => 'user_key',
                'label' => 'User relation ID',
                'type' => 'text',
                'hint' =>'Can be either a supplier PK or a customer PK.',
                'tab'=>'Companies'
            ],
            [
                'name' => 'customers',
                'label' => 'Customer Profile',
                'type' => 'relationship',
                'tab'=>'Companies',
                'ajax' => true,

            ],
            [
                'name' => 'suppliers',
                'label' => 'Supplier Profile',
                'type' => 'relationship',
                'attribute' => 'NAME',
                'tab'=>'Companies'

            ],
            /*
            [
                'name' => 'customers',
                'label' => 'Customer Profile',
                'type' => 'relationship',
                'tab'=>'Companies','ajax' => true,

            ],
            [
                'name' => 'suppliers',
                'label' => 'Supplier Profile',
                'type' => 'relationship',
                'attribute' => 'NAME',
                'tab'=>'Companies'

            ],
            [
                'name' => 'salesrep',
                'label' => 'Sales Rep',
                'type' => 'relationship',
                'attribute' => 'name',
                'tab'=>'Companies',
                'inline_create' => true,
                'min_rows' => 0,
                'max_rows' => 1,
                'subfields'   => [
                    ['name'=>'name','type'=>'text'],
                    ['name'=>'store','type'=>'relationship','attribute'=>'STORENAME'],
                    ['name'=>'customer','type'=>'relationship' ,   'ajax' => true,],
                    ['name'=>'addr','type'=>'text'],
                    ['name'=>'addr2','type'=>'text'],
                    ['name'=>'city','type'=>'text'],
                    ['name'=>'state','type'=>'text'],
                    ['name'=>'zip','type'=>'text'],
                    ['name'=>'phone','type'=>'text'],
                    ['name'=>'email','type'=>'text'],
                    ['name'=>'comment','type'=>'text'],
                    ['name'=>'rate','type'=>'number'],
                    ['name'=>'maxordersize','type'=>'number'],
                    ['name'=>'no_shipto_override','type'=>'switch'],
                ]
            ],
            */

            [
                'name' => 'password',
                'label' => trans('backpack::permissionmanager.password'),
                'type' => 'password',
                'tab'=>'General'
            ],

            [
                'name' => 'password_confirmation',
                'label' => trans('backpack::permissionmanager.password_confirmation'),
                'type' => 'password',
                'tab'=>'General'
            ],
            [  'tab'=>'Permissions',
                // two interconnected entities
                'label' => trans('backpack::permissionmanager.user_role_permission'),
                'field_unique_name' => 'user_role_permission',
                'type' => 'checklist_dependency',
                'name' => 'roles,permissions',
                'subfields' => [
                    'primary' => [
                        'label' => trans('backpack::permissionmanager.roles'),
                        'name' => 'roles', // the method that defines the relationship in your Model
                        'entity' => 'roles', // the method that defines the relationship in your Model
                        'entity_secondary' => 'permissions', // the method that defines the relationship in your Model
                        'attribute' => 'name', // foreign key attribute that is shown to user
                        'model' => config('permission.models.role'), // foreign key model
                        'pivot' => true, // on create&update, do you need to add/delete pivot table entries?]
                        'number_columns' => 3, //can be 1,2,3,4,6
                    ],
                    'secondary' => [
                        'label' => mb_ucfirst(trans('backpack::permissionmanager.permission_plural')),
                        'name' => 'permissions', // the method that defines the relationship in your Model
                        'entity' => 'permissions', // the method that defines the relationship in your Model
                        'entity_primary' => 'roles', // the method that defines the relationship in your Model
                        'attribute' => 'name', // foreign key attribute that is shown to user
                        'model' => config('permission.models.permission'), // foreign key model
                        'pivot' => true, // on create&update, do you need to add/delete pivot table entries?]
                        'number_columns' => 3, //can be 1,2,3,4,6

                    ],
                ],
            ],
        ]);
    }
}