<?php

namespace App\Http\Controllers\Admin;


use App\Library\Sophio\Products;
use App\Library\Sophio\Exporters\ZoroExporter;
use App\Library\Sophio\Exporters\ZoroNPITemplate;

use App\Library\Sophio\HorizonUtils;
use App\Library\Sophio\Jobs\Zoro\ExportDatabase;
use Illuminate\Support\Facades\Validator;

class ZoroExportController
{
    public function contactpk()
    {
        $parts = [];
        $limit = 10;
        $contactpk = "";
        $part_number_unformatted = "";

        $validator = Validator::make(request()->all(), [
            'contactpk' => 'required',


        ]);
        if (!$validator->fails()) {
            $contactpk = $validator->safe()->only(['contactpk'])['contactpk'];
            $limit = request()->limit;
            $zoro = new ZoroExporter();
            $zoro->products($contactpk);
        }

        return view('admin.zoroexport.contactpk', ['contactpk' => $contactpk, 'parts' => $parts, 'limit' => $limit]);
    }
    public function npiinventory()
    {
        ini_set('max_execution_time', -1);
        $database = "";
        $validator = Validator::make(request()->all(), [
            'database' => 'required',
        ]);

       // $jobs = HorizonUtils::getJobByName("App\\Library\\Sophio\\Jobs\\Zoro\\ExportDatabase", request()->getHost());
     $jobs = [];
        $dispatched = false;
        $zoro = new ZoroExporter();
        if (!$validator->fails()) {
            $dispatched = true;
            $settings= ['type'=>request()->get('type'),'source_export'=>request()->get('source_export'),'contactpk'=>request()->get('contactpk'),'database'=>request()->get('database')];
            $settings['dimensions'] = request()->get('dimensions')??"false";
            $settings['allowedcountries'] = request()->get('allowedcountries')??"false";
            $settings['issues'] = request()->get('issues')??"false";
            ExportDatabase::dispatch($settings);

        }
        $npi = new ZoroNPITemplate('');
        $files = $npi->getAllFiles();
        return view('admin.zoroexport.inventory', ['database' => $database,'jobs'=>$jobs,'dispatched'=>$dispatched,'files'=>$files]);

    }
    public function npi()
    {

    }
}
