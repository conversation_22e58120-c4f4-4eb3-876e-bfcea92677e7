<?php

namespace App\Http\Controllers\Admin;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Sophio\Common\Models\Catalog;

/**
 * Class BaseTetantCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class BaseCatalogCrudController extends  CrudController
{

    public   $clientId;
    public function setup()
    {

            $this->database = request()->route()->parameter('database');

            session(['database'=>$this->database]);

            $cp = Catalog::where('database',$this->database)->first();

            if (!$cp || !$cp->clientId) {

                $this->clientId = null;

            } else {
                $this->clientId = $cp->clientId;


            }
            session(['clientId'=>$this->clientId]);


    }



}