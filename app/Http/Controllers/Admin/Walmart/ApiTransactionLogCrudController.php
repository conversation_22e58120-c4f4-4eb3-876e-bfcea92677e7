<?php

namespace App\Http\Controllers\Admin\Walmart;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class ApiTransactionLogCrudController  extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    public function setup()
    {
        CRUD::setModel(\Sophio\Walmart\Models\Transaction::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/walmart/transaction');
        CRUD::setEntityNameStrings('walmarttransaction', 'Walmart Transaction');
    }
    protected function setupListOperation()
    {
        CRUD::addColumns([
            'id',
            'correlation_id',
            'endpoint', ['name'=>'request_params','limit'=>120],
            [
                'name' => 'object',
                'label' => 'Linked',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('walmart/'.strtolower($entry->object).'/' . $entry->object_id.'/show');
                    },
                    'target' => '_blank',

                ]
            ],
            [
                'name' => 'created_at',
                'label' => 'Created',
                'type' => 'datetime'
            ],
            [
                'name' => 'updated_at',
                'label' => 'Last Updated',
                'type' => 'datetime'
            ]
        ]);
        $this->crud->addFilter([
            'type' => 'dropdown',
            'name' => 'object',
            'label' => 'Type'
        ],
            [
                "Feed" => "Bulk Feed",
                "FeedItem" => "Feed Item",
                "CostFeed" => "Cost Feed",
                "InventoryFeed" => "Inventory Feed"


            ],
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'object', $value);
            }
        );
    }
    protected function setupShowOperation()
    {
        $this->crud->denyAccess(['update', 'delete', 'create']);
        $this->crud->removeButton('delete');
        $this->crud->removeColumns(['bulk_actions']);

        CRUD::addColumns([
            'id',

            ['name'=>'correlation_id','limit'=>120],
            ['name'=>'endpoint','limit'=>120],
            ['name'=>'request_params','limit'=>120],
            [
                'name' => 'created_at',
                'label' => 'Created',
                'type' => 'datetime'
            ],
            [
                'name' => 'updated_at',
                'label' => 'Last Updated',
                'type' => 'datetime'
            ],
            [
                'name' => 'object',
                'label' => 'Linked',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('walmart/'.strtolower($entry->object).'/' . $entry->object_id.'/show');
                    },
                    'target' => '_blank',

                ]
            ],
            [
                'name'=>'request_body',
                'label' => 'Request',
                'type'=>'raw_xml_pretty'

            ],
            [
                'name'=>'response_body',
                'label' => 'Response',
                'type'=>'raw_xml_pretty'

            ]
        ]);
    }
}