<?php

namespace App\Http\Controllers\Admin\Walmart;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class FeedItemsInventoryHistoryCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;


    public function setup()
    {
        CRUD::setModel(\Sophio\Walmart\Models\FeedItemInventoryHistory::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/walmart/feediteminventoryhistory');
        CRUD::setEntityNameStrings('feediteminventoryhistory', 'Walmart Feed Items Inventory History');

    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('showEntryCount', false);
        CRUD::addColumns([
            [
                'name' => 'id',
                'type' => 'number',
                'label' => 'ID',
            ],
            [
                'name' => 'marketSellerSku',
                'type' => 'text',
                'label' => 'SKU', 'limit' => 255,
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->marketplaceitem)
                            return backpack_url('walmart/marketplaceitem/' . $entry->marketplaceitem->pk . '/show');
                    },
                    'target' => '_blank',

                ]

            ],

            [
                'name' => 'supplier_pk',
                'type' => 'text',
                'label' => 'ShipNode', 'limit' => 255,
                'value' => function ($entry) {
                    return $entry->supplier->SETTINGS['WALMARTSHIPNODE'] . " (" . $entry->supplier->contactpk . ")";
                },
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/sophio_fbs/fbs/supplier/' . $entry->supplier_pk . '/show');
                    },
                    'target' => '_blank',

                ]

            ],
            [
                'name' => 'qty_avail',
                'type' => 'number',
                'label' => 'qty_avail',

            ],
            [
                'name' => 'errors',
                'type' => 'text',
                'label' => 'Errors', 'limit' => 255,

            ],
            [
                'name' => 'feed.created_at',
                'type' => 'text',
                'label' => 'Date'

            ],
            [
                'name' => 'feed',
                'type' => 'relationship',
                'limit'=>100,
                'label' => 'Feed',
                'attribute'=>'feedid',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('walmart/inventoryfeed/' . $entry->feed_id . '/show');
                    },
                    'target' => '_blank',

                ]
            ],

        ]);

        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'marketSellerSku',
            'label' => 'SKU'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'marketSellerSku', '=', "$value");
            }
        );


        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'contactpk',
            'label' => 'contactpk'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'contactpk', '=', "$value");
            }
        );
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'errors',
            'label' => 'errors'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('whereNotNull', 'errors');
            }
        );
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'qty_avail',
            'label' => 'Stock'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'qty_avail', '>', 0);
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'feed_id',
            'label' => 'feed_id'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'feed_id', '=', "$value");
            }
        );
        $this->crud->orderBy('feed_id', 'desc');
    }

    protected function setupShowOperation()
    {
        CRUD::addColumns([
            [
                'name' => 'id',
                'type' => 'number',
                'label' => 'ID',


            ],
            [
                'name' => 'marketSellerSku',
                'type' => 'text',
                'label' => 'SKU', 'limit' => 255,
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->marketplaceitem)
                            return backpack_url('walmart/marketplaceitem/' . $entry->marketplaceitem->pk . '/show');
                    },
                    'target' => '_blank',

                ]

            ],
            [
                'name' => 'supplier_pk',
                'type' => 'text',
                'label' => 'Supplier', 'limit' => 255,
                'value' => function ($entry) {
                    return $entry->supplier->NAME . " (" . $entry->supplier->contactpk . ")";
                },
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/sophio_fbs/fbs/supplier/' . $entry->supplier_pk . '/show');
                    },
                    'target' => '_blank',

                ]

            ],
            [
                'name' => 'qty_avail',
                'type' => 'text',
                'label' => 'qty_avail', 'limit' => 255

            ],
            [
                'name' => 'feed_id',
                'type' => 'text',
                'label' => 'Feed',


                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('walmart/inventoryfeed/' . $entry->feed_id . '/show');
                    },
                    'target' => '_blank',

                ]
            ],
            [
                'name' => 'errors',
                'type' => 'text',
                'label' => 'errors', 'limit' => 10000

            ],

        ]);

    }
}