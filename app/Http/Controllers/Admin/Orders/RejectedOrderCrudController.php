<?php

namespace App\Http\Controllers\Admin\Orders;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use App\Models\Orders\RejectedOrder;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class RejectedOrderCrudController extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    use \Backpack\CRUD\app\Http\Controllers\Operations\BulkDeleteOperation;
    public function setup()
    {
        parent::setup();
        CRUD::setModel(RejectedOrder::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/rejectedorder');
        CRUD::setEntityNameStrings('rejectedorders', \Route::current()->parameter('database') . ' \ Rejected Orders');

    }

    protected function setupListOperation()
    {
        $this->crud->orderBy('dateCreated','desc');
        $this->crud->denyAccess(['update','delete','create']);
        $this->crud->removeButton('delete');
        $this->crud->removeColumns(['bulk_actions']);
        $this->crud->enableDetailsRow();
        $this->crud->setDetailsRowView('vendor.backpack.crud.details_row.order');
        $this->crud->addColumns(
            [
                [
                    'name' => 'po_number',
                    'label' => 'orderNumber',
                    'type' => 'text'
                ],
                [
                    'name' => 'orderBody',
                    'label' => 'Parts',
                    'type' => 'parts_list_from_body',
                    'searchLogic' => function($query,$column,$searchTerm){
                        $query->orWhere('orderBody','like','%'.$searchTerm.'%');
                    }
                ],
                [
                    'name' => 'reason',
                    'label' => 'Reject reason',
                    'type' => 'text'
                ],

                [
                    'name' => 'dateCreated',
                    'label' => 'Date',
                    'type' => 'datetime'
                ],
            ]);

        $this->crud->addFilter([
            'type' =>'simple',
            'name' =>'multi',
            'label' => 'Multiline',

        ],false,function(){
            $this->crud->addClause('whereRaw','JSON_LENGTH(JSON_EXTRACT(orderBody,"$.items"))>1');
        });

    }
    protected function setupShowOperation()
    {
        $this->crud->denyAccess(['update','delete','create']);
        $this->crud->removeButton('delete');
        $this->crud->removeColumns(['bulk_actions']);
        $this->crud->set('show.setFromDb', false);
        $this->crud->addColumns([
            [
                'name' => 'po_number',
                'label' => 'orderNumber',
                'type' => 'text'
            ],

            [
                'name' => 'reason',
                'label' => 'Reject reason',
                'type' => 'text'
            ],
            [
                'name'=>'dateCreated',
                'label'=>'Date Added',
                'type' => 'datetime'

            ],
            [
                'name'=>'supplierlog_id',
                'label'=>'supplierlog_id',
                'type' => 'number',
                'wrapper' => [
                    'element' => 'a',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/'.session('database').'/supplierlog/'.$entry->supplierlog_id.'/show');
                    },
                    'target' => '_blank',

                ]

            ],
            [
                'name'=>'orderBody',
                'label'=>'Details',
                'type' => 'orderBody'

            ]
        ]);
    }
}