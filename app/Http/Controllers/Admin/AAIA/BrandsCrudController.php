<?php

namespace App\Http\Controllers\Admin\AAIA;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Sophio\Common\Models\AAIA\Brand;
use Sophio\Common\Models\AAIA\BrandLogo;
use Sophio\Common\Models\FBS\WhiDcfAfmkt;
use Sophio\Common\Models\WHIACES\WhiAcesBrandMatching;

class BrandsCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;

    public function setup()
    {
        CRUD::setModel(Brand::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/aaia/brands');
        CRUD::setEntityNameStrings('AAIA Brand', 'AAIA Brands');
        Validator::extend('uniqueWhiAcesBrandMatching', function ($attribute, $value, $parameters, $validator) {
            $req = Arr::dot(request()->all());
            $a = explode('.', $attribute);

            $count = WhiAcesBrandMatching::where('aces_BrandID', $req['WhiAcesBrandMatching.' . $a[1] . '.aces_BrandID'])
                ->where('mfg_code', $req['WhiAcesBrandMatching.' . $a[1] . '.mfg_code'])->where('id', '<>', $req['WhiAcesBrandMatching.' . $a[1] . '.id'])
                ->count();

            return $count === 0;
        });
    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        CRUD::button('email')->stack('top')->view('crud::buttons.quick')->meta([
            'access' => true,
            'label' => 'Download WHI Master DCF',
            'icon' => 'la la-download',
            'wrapper' => [
                'element' => 'a',
                'href' => sophio_route('fbs/dcf.downloadMasterDCF'),


            ]
        ]);
        CRUD::addColumns([
            ['name' => 'BrandID', 'searchLogic' => false,            'wrapper'=>[
                'target'=>'_blank',
                'href' => function ($crud, $column, $entry, $related_key) {
                    return sophio_route('catalog/brands.brandreport',['brandid'=>$entry->BrandID]);

                },
            ]],
            ['name' => 'BrandName', 'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('BrandName', 'like', '%' . $searchTerm . '%');
            }],
            ['name' => 'ParentID', 'searchLogic' => false,],
            ['name' => 'ParentCompany', 'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('ParentCompany', 'like', '%' . $searchTerm . '%');
            }],
            ['name' => 'SubBrandID', 'searchLogic' => false,],
            ['name' => 'SubBrand', 'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('SubBrand', 'like', '%' . $searchTerm . '%');
            }],
            ['name' => 'WhiAcesBrandMatching',
                'type' => 'relationship ',
                'label' => 'WHI mfg codes'

            ],
            [
                'name' => 'logo',
                'type' => 'text','escaped'=>false,'limit'=>1000,
                'value' => function ($entry) {
                    if ($entry->SubBrandID !== null && $entry->SubBrandID !== "") {
                        $b = BrandLogo::where('BrandID', $entry->SubBrandID)->first();

                    } else {
                        $b = BrandLogo::where('BrandID', $entry->BrandID)->first();

                    }
                    if (isset($b)) {
                        return
                        '<img src="'.Storage::disk('imagespool')->url($b->filename).'" style="
        max-height: 25px;
        width: auto;
        border-radius: 3px;">';
                    }else{

                        return 'Add';
                    }
                },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {

                        if ($entry->SubBrandID !== null && $entry->SubBrandID !== "") {
                            $b = BrandLogo::where('BrandID', $entry->SubBrandID)->first();


                        } else {
                            $b = BrandLogo::where('BrandID', $entry->BrandID)->first();

                        }
                        if (isset($b)) {
                            return backpack_url('aaia/brandslogo/' . $b->id . '/edit');
                        }else{
                            return backpack_url('aaia/brandslogo/create?BrandID='.($entry->SubBrandID!==""?$entry->SubBrandID:$entry->BrandID));
                        }
                    },
                ]
            ]
        ]);
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'BrandID',
            'label' => 'AAIA BrandID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'BrandID', $value);
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'ParentID',
            'label' => 'ParentID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'ParentID', $value);
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'SubBrandID',
            'label' => 'SubBrandID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'SubBrandID', $value);
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'WhiAcesBrandMatching',
            'label' => 'WHI mfg Code'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('whereHas', 'WhiAcesBrandMatching', function ($query) use ($value) {
                    $query->where('mfg_code', $value);
                });
            }
        );
        $this->crud->addFilter([
            'type' => 'dropdown',
            'name' => 'haswhicodes',
            'label' => 'Has WHI codes '
        ], ['true' => 'Yes', 'false' => 'No'],
            function ($value) { // if the filter is active
                if ($value == 'true') {
                    $this->crud->addClause('whereHas', 'WhiAcesBrandMatching');
                } else {
                    $this->crud->addClause('whereDoesntHave', 'WhiAcesBrandMatching');
                }

            }
        );
    }

    protected function setupUpdateOperation()
    {
        CRUD::field('BrandID');
        CRUD::field('BrandName');
        CRUD::field('ParentID');
        CRUD::field('ParentCompany');
        CRUD::field('SubBrandID');
        CRUD::field('SubBrand');
        CRUD::field([   // relationship
            'name' => 'WhiAcesBrandMatching', // the method on your model that defines the relationship
            'type' => "repeatable",
            'label' => 'WHI MFG Codes',
            'subfields' => [ // also works as: "fields"
                [
                    'name' => 'aces_BrandID',
                    'type' => 'text',
                    'label' => 'AAIA BrandID',
                    'wrapper' => ['class' => 'form-group col-md-4'],
                ],
                [
                    'name' => 'mfg_code',
                    'type' => 'text',
                    'label' => 'WHI MFG Code',
                    'wrapper' => ['class' => 'form-group col-md-4'],
                    'validationRules' => 'required|uniqueWhiAcesBrandMatching',
                    'validationMessages' => [
                        'required' => 'mfg code cannot be empty',
                        'uniqueWhiAcesBrandMatching' =>
                            'There is already a record with same MFG code and Brand ID',
                    ]
                ],
                [
                    'name' => 'mfg_name',
                    'type' => 'text',
                    'label' => 'Mfg Name',
                    'wrapper' => ['class' => 'form-group col-md-4'],
                ],
            ]
            // OPTIONALS:
            // 'label' => "Category",
            // 'attribute' => "title", // attribute on model that is shown to user
            // 'placeholder' => "Select a category", // placeholder for the select2 input
        ]);

    }
}