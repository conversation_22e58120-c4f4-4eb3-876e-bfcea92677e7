<?php

namespace App\Http\Controllers\Admin\AAIA;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Sophio\Common\Models\AAIA\Brand;
use Sophio\Common\Models\AAIA\Part;
use Sophio\Common\Models\AAIA\Taxonomy;
use Sophio\Common\Models\AAIA\TaxonomyOverride;
use Sophio\Common\Models\WHIACES\WhiAcesBrandMatching;

class PartsCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;

    public function setup()
    {
        CRUD::setModel(Part::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/aaia/part');
        CRUD::setEntityNameStrings('AAIA Part Type', 'AAIA Part Types');

        Part::saving(function ($part) {
            $to = TaxonomyOverride::where('taxonomy_type', 'PartTerminologyID')->where('taxonomy_id', $part->PartTerminologyID)->first();

            if ($to) {
                $to->update(['value' => $part->PartTerminologyName]);
            } else {

                TaxonomyOverride::create(['taxonomy_type' => 'PartTerminologyID', 'taxonomy_id' => $part->PartTerminologyID, 'value' => $part->getDirty()['PartTerminologyName'], 'original_value' => $part->getOriginal('PartTerminologyName')]);
            }
            Taxonomy::where('PartTerminologyID', $part->PartTerminologyID)->update(['PartTerminologyName' => $part->PartTerminologyName, 'parttype_slug' => Str::slug($part->PartTerminologyName)]);
        });
        if (!$this->crud->getRequest()->has('order')) {
            $this->crud->orderBy('PartTerminologyName', 'asc');
        }
    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        CRUD::addColumns([
                ['name' => 'PartTerminologyID', 'searchLogic' => false, 'label' => 'ID'],
                ['name' => 'PartTerminologyName', 'label' => 'Name', 'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('PartTerminologyName', 'like', '%' . $searchTerm . '%');
                }],
                ['name' => 'taxonomy.SubCategoryName', 'label' => 'Sub Category', 'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhereHas('taxonomy', function ($q) use ($searchTerm) {
                        $q->where("SubCategoryName", "like", "%" . $searchTerm . "%");
                    });
                }],
                ['name' => 'taxonomy.CategoryName', 'label' => 'Category', 'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhereHas('taxonomy', function ($q) use ($searchTerm) {
                        $q->where("CategoryName", "like", "%" . $searchTerm . "%");
                    });
                }],
                ['name' => 'override', 'label' => 'Modified', 'type' => 'check'],
            ]
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'PartTerminologyID',
            'label' => 'ID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'PartTerminologyID', $value);
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'CategoryID',
            'label' => 'Category ID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('whereHas','taxonomy',function($q) use ($value) {
                    $q->where('CategoryID', $value);
                });
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'SubCategoryID',
            'label' => 'SubCategory ID'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('whereHas','taxonomy',function($q) use ($value) {
                    $q->where('SubCategoryID', $value);
                });
            }
        );
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'PartTerminologyName',

            'label' => 'Name'
        ], null,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'PartTerminologyName', 'LIKE', '%' . $value . '%');
            }
        );
    }

    protected function setupUpdateOperation()
    {
        $p = $this->crud->getCurrentEntry()  ;
        CRUD::field(['name' => 'PartTerminologyName', 'label' => 'Name', 'hint' => 'A full reindex is required for changed to get reflected on the website.']);
        CRUD::field(['name' => 'override.original_value',
            'default'=>$p->PartTerminologyName,
            'label' => 'Original value',  'attributes' => ['readonly' => 'readonly' ]]);
    }
}