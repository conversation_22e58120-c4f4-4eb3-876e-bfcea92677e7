<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;

use Illuminate\Mail\Mailable;
use Illuminate\Queue\Middleware\RateLimited;
use Illuminate\Queue\SerializesModels;

use Sophio\Common\Traits\UniqueEmails;

class ManualException extends Mailable
{
    use Queueable, SerializesModels;
    use UniqueEmails;
    public $content;
    public $no_logging=true;
    public function middleware()
    {
        return [(new RateLimited('sendcrashmails'))->dontRelease()];
    }
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($content)
    {
        $this->content = $content;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->checkUnique();

        $m = $this->view('emails.exception')->subject('['.env('APP_DOMAIN').'] Exception occured')
            ->with('content', $this->content);
        if (env('MAIL_MAILER') === 'sendgrid') {
            return $this->sendToSendGrid($m);
        }
        return $m;
    }
}