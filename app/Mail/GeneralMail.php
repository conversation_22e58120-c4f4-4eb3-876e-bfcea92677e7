<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

use Sophio\Common\Traits\UniqueEmails;

class GeneralMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels ,UniqueEmails;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $data;

    public function __construct($data)
    {
        //
        $this->data = $data;

        $this->onQueue('emails');
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {

        if (isset($this->data['from']) && isset($this->data['from']['address'])) {
            $this->from($this->data['from']['address'], $this->data['from']['name']);
        } else {
            $this->from(config('sophio.admin.default_from.address'), config('sophio.admin.default_from.name'));
        }


        $this->bcc(config('sophio.admin.mail_senders.catchme'));
        if (isset($this->data['replyTo']) && isset($this->data['replyTo']['address'])) {
            $this->replyTo($this->data['replyTo']['address'], $this->data['replyTo']['name']);
        } else {
            $this->replyTo(config('sophio.admin.default_replyTo.address'), config('sophio.admin.default_replyTo.name'));
        }
        $this->checkUnique();
        $m = $this->subject($this->data['subject'])->view($this->data['view'])->with(['data' => $this->data]);
        if (isset($this->data['attach'])) {
            foreach ($this->data['attach'] as $a) {
                $m->attach($a);
            }
        }


        return $m;
    }


    public function getData()
    {
        return $this->data;
    }
}
