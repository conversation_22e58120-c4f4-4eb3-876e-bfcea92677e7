<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Sophio\Common\Traits\UniqueEmails;

class TrackingToCustomer     extends Mailable
{
    use UniqueEmails, Queueable, SerializesModels;
    protected $data;

    public function __construct($data)
    {
        //
        $this->data = $data;


    }
    public function build()
    {
        if (count(array_filter(config('sophio.admin.mail_senders.catchme'))) > 0) {

            $this->bcc(config('sophio.admin.mail_senders.catchme'));
        }
        $this->checkUnique();
        $m = $this->markdown('emails.custtrackingnumber')->from(config('sophio.admin.default_from.address'), config('sophio.admin.default_from.name'))
            ->with('invoice', $this->data['invoice'])
            ->with('ordercreator', $this->data['ordercreator'])
            ->with('track_num', $this->data['track_num'])
            ->with('track_url', $this->data['track_url'])
            ->subject((sophiosettings()->get('demo')?'DEMO ':'')."Tracking Number for PO:" . $this->data['invoice']->ponumber);


        return $m;
    }

}