<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

require __DIR__ . '/../bootstrap/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
if (request()->get('key') == 'Jww3232fw') {
    try {
        $supplier = \Sophio\Common\Models\FBS\Supplier::where('PK', 910)->first();
        $importFeed = new \Sophio\FBSInventoryImporter\Library\ImportFeed($supplier, 'b2ccentral_aces', ['onlysummary'=>"1","pushToWarehouse"=>false,'postImport'=>false,'startdate'=>Carbon::now()]);
        $importFeed->setStorageDisk('tmp');
        $importFeed->setLocalPrefix('par/');
        $importFeed->retrieveFileFromFTP();
        $file = $importFeed->getLocalPath();
        echo json_encode(['status'=>'SUCCESS','file'=>$file]);exit();


    }catch (\Exception $e) {
        report($e);
        echo json_encode(['status'=>'FAIL']);exit();
    }
}



$response = $rs->request(
    array(
        'carrier' => 'FedEx',
        'action' => 'GetAllRates',
        'params' =>
            array(
                'account_number' => 'YOUR_ACCOUNT_NUMBER',
                'meter_number' => 'YOUR_METER_NUMBER',
                'key' => 'YOUR_KEY',
                'password' => 'YOUR_PASSWORD',
                'cod_fund_type' => 'PERSONAL_CHECK',
                'cod_amount' => 774.6,
                'currency' => 'USD',
                'packages' =>
                    array(
                        0 =>
                            array(
                                'weight' => 9,
                                'width' => 20,
                                'height' => 6,
                                'length' => 30,
                            ),
                    ),
                'customs' =>
                    array(),
                'shipper' => 'My Company',
                'ship_city' => 'Vancouver',
                'ship_state' => 'WA',
                'ship_code' => '98685',
                'ship_country' => 'US',
                'to_state' => 'CA',
                'to_country' => 'US',
                'to_code' => '90210',
                'weight_unit' => 'LB',
                'packaging_type' => 'YOUR_PACKAGING',
                'dropoff_type' => 'REGULAR_PICKUP',
                'test' => true,
            ),
    )
);