
@media only screen and (max-width: 660px) {
    table[class=w0], td[class=w0] {
        width: 0 !important;
    }
    table[class=w10], td[class=w10], img[class=w10] {
        width: 10px !important;
    }
    table[class=w15], td[class=w15], img[class=w15] {
        width: 5px !important;
    }
    table[class=w30], td[class=w30], img[class=w30] {
        width: 10px !important;
    }
    table[class=w60], td[class=w60], img[class=w60] {
        width: 10px !important;
    }
    table[class=w125], td[class=w125], img[class=w125] {
        width: 80px !important;
    }
    table[class=w130], td[class=w130], img[class=w130] {
        width: 55px !important;
    }
    table[class=w140], td[class=w140], img[class=w140] {
        width: 90px !important;
    }
    table[class=w160], td[class=w160], img[class=w160] {
        width: 180px !important;
    }
    table[class=w170], td[class=w170], img[class=w170] {
        width: 100px !important;
    }
    table[class=w180], td[class=w180], img[class=w180] {
        width: 80px !important;
    }
    table[class=w195], td[class=w195], img[class=w195] {
        width: 80px !important;
    }
    table[class=w220], td[class=w220], img[class=w220] {
        width: 80px !important;
    }
    table[class=w240], td[class=w240], img[class=w240] {
        width: 180px !important;
    }
    table[class=w255], td[class=w255], img[class=w255] {
        width: 185px !important;
    }
    table[class=w275], td[class=w275], img[class=w275] {
        width: 135px !important;
    }
    table[class=w280], td[class=w280], img[class=w280] {
        width: 135px !important;
    }
    table[class=w300], td[class=w300], img[class=w300] {
        width: 140px !important;
    }
    table[class=w325], td[class=w325], img[class=w325] {
        width: 95px !important;
    }
    table[class=w360], td[class=w360], img[class=w360] {
        width: 140px !important;
    }
    table[class=w410], td[class=w410], img[class=w410] {
        width: 180px !important;
    }
    table[class=w470], td[class=w470], img[class=w470] {
        width: 200px !important;
    }
    table[class=w580], td[class=w580], img[class=w580] {
        width: 280px !important;
    }
    table[class=w640], td[class=w640], img[class=w640] {
        width: 300px !important;
    }
    table[class*=hide], td[class*=hide], img[class*=hide], p[class*=hide], span[class*=hide] {
        display: none !important;
    }
    table[class=h0], td[class=h0] {
        height: 0 !important;
    }
    p[class=footer-content-left] {
        text-align: center !important;
    }
    #headline p {
        font-size: 30px !important;
    }
    .article-content, #left-sidebar {
        -webkit-text-size-adjust: 90% !important;
        -ms-text-size-adjust: 90% !important;
    }
    .header-content, .footer-content-left {
        -webkit-text-size-adjust: 80% !important;
        -ms-text-size-adjust: 80% !important;
    }
    img {
        height: auto;
        line-height: 100%;
    }
}
/* Client-specific Styles */
#outlook a {
    padding: 0;
}	/* Force Outlook to provide a "view in browser" button. */
body {
    width: 100% !important;
}
.ReadMsgBody {
    width: 100%;
}
.ExternalClass {
    width: 100%;
    display: block !important;
} /* Force Hotmail to display emails at full width */
/* Reset Styles */
/* Add 100px so mobile switch bar doesn't cover street address. */
body {
    background-color: #dedede;
    margin: 0;
    padding: 0;
}
img {
    outline: none;
    text-decoration: none;
    display: block;
}
br, strong br, b br, em br, i br {
    line-height: 100%;
}
h1, h2, h3, h4, h5, h6 {
    line-height: 100% !important;
    -webkit-font-smoothing: antialiased;
}
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    color: blue !important;
}
h1 a:active, h2 a:active, h3 a:active, h4 a:active, h5 a:active, h6 a:active {
    color: red !important;
}
/* Preferably not the same color as the normal header link color.  There is limited support for psuedo classes in email clients, this was added just for good measure. */
h1 a:visited, h2 a:visited, h3 a:visited, h4 a:visited, h5 a:visited, h6 a:visited {
    color: purple !important;
}
/* Preferably not the same color as the normal header link color. There is limited support for psuedo classes in email clients, this was added just for good measure. */
table td, table tr {
    border-collapse: collapse;
    border-top:0 !important;
}
.yshortcuts, .yshortcuts a, .yshortcuts a:link, .yshortcuts a:visited, .yshortcuts a:hover, .yshortcuts a span {
    color: black;
    text-decoration: none !important;
    border-bottom: none !important;
    background: none !important;
}	/* Body text color for the New Yahoo.  This example sets the font of Yahoo's Shortcuts to black. */
/* This most probably won't work in all email clients. Don't include code blocks in email. */
code {
    white-space: normal;
    word-break: break-all;
}
#background-table {
    background-color: #dedede;
}
/* Webkit Elements */
#top-bar {
    border-radius: 6px 6px 0px 0px;
    -moz-border-radius: 6px 6px 0px 0px;
    -webkit-border-radius: 6px 6px 0px 0px;
    -webkit-font-smoothing: antialiased;
    background-color: #c7c7c7;
    color: #ededed;
}
#top-bar a {
    font-weight: bold;
    color: #ffffff;
    text-decoration: none;
}
#footer {
    border-radius: 0px 0px 6px 6px;
    -moz-border-radius: 0px 0px 6px 6px;
    -webkit-border-radius: 0px 0px 6px 6px;
    -webkit-font-smoothing: antialiased;
}
/* Fonts and Content */
body, td {
    font-family: 'Helvetica Neue', Arial, Helvetica, Geneva, sans-serif;
}
.header-content, .footer-content-left, .footer-content-right {
    -webkit-text-size-adjust: none;
    -ms-text-size-adjust: none;
}
/* Prevent Webkit and Windows Mobile platforms from changing default font sizes on header and footer. */
.header-content {
    font-size: 12px;
    color: #ededed;
}
.header-content a {
    font-weight: bold;
    color: #ffffff;
    text-decoration: none;
}
#headline p {
    color: #444444;
    font-family: 'Helvetica Neue', Arial, Helvetica, Geneva, sans-serif;
    font-size: 32px;
    text-align: center;
    margin-top: 0px;
    margin-bottom: 30px;
}
#headline p a {
    color: #444444;
    text-decoration: none;
}
.article-title, .totals {
    font-size: 18px;
    line-height: 24px;
    color: #6b676b;
    font-weight: bold;
    margin-top: 0px;
    margin-bottom: 18px;
    font-family: 'Helvetica Neue', Arial, Helvetica, Geneva, sans-serif;
}

table {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
}

.table-bordered {
    border: 1px solid #dddddd;
    border-collapse: separate;
    border-left: 0;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.table-bordered th, .table-bordered td {
    border-left: 1px solid #dddddd;
}

.table-condensed th, .table-condensed td {
    padding: 4px 5px;
}

.table th, .table td {
    padding: 1px;
    line-height: normal;
    text-align: left;
    vertical-align: top;

}

table.totals{ width: auto;}
table.totals td{ text-align: right;}
table.table-order-detail {font-size: 12px;}

.section-title {
    font-size: 18px;
    line-height: 24px;
    color: #ffffff;
    font-weight: bold;
    margin-top: 0px;
    margin-bottom: 18px;
    padding: 4px;
    font-family: 'Helvetica Neue', Arial, Helvetica, Geneva, sans-serif;
}
.article-title a {
    color: #6b676b;
    text-decoration: none;
}
.article-title.with-meta {
    margin-bottom: 0;
}
.article-meta {
    font-size: 13px;
    line-height: 20px;
    color: #ccc;
    font-weight: bold;
    margin-top: 0;
}
.article-content {
    font-size: 13px;
    line-height: 18px;
    color: #444444;
    margin-top: 0px;
    margin-bottom: 18px;
    font-family: 'Helvetica Neue', Arial, Helvetica, Geneva, sans-serif;
}
.article-content a {
    color: #2f82de;
    font-weight: bold;
    text-decoration: none;
}
.article-content img {
    max-width: 100%
}
.article-content ol, .article-content ul {
    margin-top: 0px;
    margin-bottom: 18px;
    margin-left: 19px;
    padding: 0;
}
.article-content li {
    font-size: 13px;
    line-height: 18px;
    color: #444444;
}
.article-content li a {
    color: #2f82de;
    text-decoration: underline;
}
.article-content p {
    margin-bottom: 15px;
}
.footer-content-left {
    font-size: 12px;
    line-height: 15px;
    color: #ededed;
    margin-top: 0px;
    margin-bottom: 0px;
}
.footer-content-left a {
    color: #ffffff;
    font-weight: bold;
    text-decoration: none;
}
.footer-content-right {
    font-size: 12px;
    line-height: 16px;
    color: #ededed;
    margin-top: 0px;
    margin-bottom: 0px;
}
.footer-content-right a {
    color: #ffffff;
    font-weight: bold;
    text-decoration: none;
}
#footer {
    background-color: #c7c7c7;
    color: #ededed;
}
#footer a {
    color: #ffffff;
    text-decoration: none;
    font-weight: bold;
}

#street-address {
    font-size: 16px;
    line-height: 24px;
    color: #fff;
    white-space: normal;
}

.well {background-color: #fff; margin: 0; padding: 0;
    border: 1px solid #e3e3e3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

.alert {
    padding: 8px 35px 8px 14px;
    margin-bottom: 20px;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    background-color: #fcf8e3;
    border: 1px solid #fbeed5;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

@media print {
    .btn,.no-print {display: none;}
}
